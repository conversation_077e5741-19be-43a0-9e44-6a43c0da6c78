package com.anginatech.textrepeater.repository;

import android.content.Context;
import android.util.Log;

import com.anginatech.textrepeater.Config;
import com.anginatech.textrepeater.models.AdConfigResponse;
import com.anginatech.textrepeater.models.AppSettingsResponse;
import com.anginatech.textrepeater.models.TextContentResponse;
import com.anginatech.textrepeater.models.UserRegistrationRequest;
import com.anginatech.textrepeater.models.UserRegistrationResponse;
import com.anginatech.textrepeater.models.ApiResponse;
import com.anginatech.textrepeater.network.ModernApiClient;
import com.anginatech.textrepeater.network.NetworkUtils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Modern Repository Pattern Implementation
 * Handles data operations with caching, offline support, and performance optimizations
 */
public class TextRepeaterRepository {
    private static final String TAG = "TextRepeaterRepository";
    private static TextRepeaterRepository instance;
    
    private ModernApiClient apiClient;
    private ExecutorService executorService;
    private Context context;

    // Cache timestamps for intelligent refresh
    private long lastAdConfigFetch = 0;
    private long lastTextContentFetch = 0;
    private long lastAppSettingsFetch = 0;
    
    // Cache duration in milliseconds
    private static final long CACHE_DURATION_AD_CONFIG = 5 * 60 * 1000; // 5 minutes
    private static final long CACHE_DURATION_TEXT_CONTENT = 10 * 60 * 1000; // 10 minutes
    private static final long CACHE_DURATION_APP_SETTINGS = 5 * 60 * 1000; // 5 minutes

    private TextRepeaterRepository(Context context) {
        this.context = context.getApplicationContext();
        this.apiClient = ModernApiClient.getInstance(context);
        this.executorService = Executors.newFixedThreadPool(3); // Optimized thread pool
    }

    public static synchronized TextRepeaterRepository getInstance(Context context) {
        if (instance == null) {
            instance = new TextRepeaterRepository(context);
        }
        return instance;
    }

    /**
     * Fetch AdMob configuration with intelligent caching
     */
    public void fetchAdMobConfig(RepositoryCallback<AdConfigResponse> callback) {
        // Check if we need to refresh cache
        long currentTime = System.currentTimeMillis();
        boolean shouldRefresh = (currentTime - lastAdConfigFetch) > CACHE_DURATION_AD_CONFIG;
        
        if (!shouldRefresh && apiClient.isCacheAvailable()) {
            Log.d(TAG, "Using cached AdMob config");
        }

        Call<ApiResponse<AdConfigResponse>> call = apiClient.getApiService()
            .getAdMobConfig(Config.ENDPOINT_ADS_CONFIG);

        call.enqueue(new Callback<ApiResponse<AdConfigResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<AdConfigResponse>> call, 
                                 Response<ApiResponse<AdConfigResponse>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<AdConfigResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        lastAdConfigFetch = currentTime;
                        callback.onSuccess(apiResponse.getData());
                        Log.d(TAG, "AdMob config fetched successfully");
                    } else {
                        callback.onError(apiResponse.getError() != null ? 
                            apiResponse.getError().getMessage() : "Unknown error");
                    }
                } else {
                    callback.onError("Failed to fetch AdMob config: " + response.code());
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<AdConfigResponse>> call, Throwable t) {
                Log.e(TAG, "AdMob config fetch failed: " + t.getMessage());
                callback.onError("Network error: " + t.getMessage());
            }
        });
    }

    /**
     * Fetch text content with optimized caching
     */
    public void fetchTextContent(RepositoryCallback<TextContentResponse> callback) {
        long currentTime = System.currentTimeMillis();
        boolean shouldRefresh = (currentTime - lastTextContentFetch) > CACHE_DURATION_TEXT_CONTENT;
        
        if (!shouldRefresh && apiClient.isCacheAvailable()) {
            Log.d(TAG, "Using cached text content");
        }

        Call<ApiResponse<TextContentResponse>> call = apiClient.getApiService()
            .getTextContent(Config.ENDPOINT_TEXT_CONTENT);

        call.enqueue(new Callback<ApiResponse<TextContentResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<TextContentResponse>> call, 
                                 Response<ApiResponse<TextContentResponse>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<TextContentResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        lastTextContentFetch = currentTime;
                        callback.onSuccess(apiResponse.getData());
                        Log.d(TAG, "Text content fetched successfully");
                    } else {
                        callback.onError(apiResponse.getError() != null ? 
                            apiResponse.getError().getMessage() : "Unknown error");
                    }
                } else {
                    callback.onError("Failed to fetch text content: " + response.code());
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<TextContentResponse>> call, Throwable t) {
                Log.e(TAG, "Text content fetch failed: " + t.getMessage());
                callback.onError("Network error: " + t.getMessage());
            }
        });
    }

    /**
     * Fetch app settings with caching
     */
    public void fetchAppSettings(RepositoryCallback<AppSettingsResponse> callback) {
        long currentTime = System.currentTimeMillis();
        boolean shouldRefresh = (currentTime - lastAppSettingsFetch) > CACHE_DURATION_APP_SETTINGS;
        
        Call<ApiResponse<AppSettingsResponse>> call = apiClient.getApiService()
            .getAppSettings(Config.ENDPOINT_SETTINGS);

        call.enqueue(new Callback<ApiResponse<AppSettingsResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<AppSettingsResponse>> call, 
                                 Response<ApiResponse<AppSettingsResponse>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<AppSettingsResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        lastAppSettingsFetch = currentTime;
                        callback.onSuccess(apiResponse.getData());
                        Log.d(TAG, "App settings fetched successfully");
                    } else {
                        callback.onError(apiResponse.getError() != null ? 
                            apiResponse.getError().getMessage() : "Unknown error");
                    }
                } else {
                    callback.onError("Failed to fetch app settings: " + response.code());
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<AppSettingsResponse>> call, Throwable t) {
                Log.e(TAG, "App settings fetch failed: " + t.getMessage());
                callback.onError("Network error: " + t.getMessage());
            }
        });
    }

    /**
     * Register user with optimized request
     */
    public void registerUser(UserRegistrationRequest request, 
                           RepositoryCallback<UserRegistrationResponse> callback) {
        Call<ApiResponse<UserRegistrationResponse>> call = apiClient.getApiService()
            .registerUser(Config.ENDPOINT_USER_REGISTER, request);

        call.enqueue(new Callback<ApiResponse<UserRegistrationResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<UserRegistrationResponse>> call, 
                                 Response<ApiResponse<UserRegistrationResponse>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<UserRegistrationResponse> apiResponse = response.body();
                    if (apiResponse.isSuccess()) {
                        callback.onSuccess(apiResponse.getData());
                        Log.d(TAG, "User registered successfully");
                    } else {
                        callback.onError(apiResponse.getError() != null ? 
                            apiResponse.getError().getMessage() : "Registration failed");
                    }
                } else {
                    callback.onError("Failed to register user: " + response.code());
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<UserRegistrationResponse>> call, Throwable t) {
                Log.e(TAG, "User registration failed: " + t.getMessage());
                callback.onError("Network error: " + t.getMessage());
            }
        });
    }

    /**
     * Clear all caches for fresh data
     */
    public void clearCache() {
        apiClient.clearCache();
        lastAdConfigFetch = 0;
        lastTextContentFetch = 0;
        lastAppSettingsFetch = 0;
        Log.d(TAG, "All caches cleared");
    }

    /**
     * Check if network is available
     */
    public boolean isNetworkAvailable() {
        return NetworkUtils.isNetworkAvailable(context);
    }

    /**
     * Repository callback interface
     */
    public interface RepositoryCallback<T> {
        void onSuccess(T data);
        void onError(String error);
    }
}
