<?php
$page_title = 'Ad Analytics';
require_once 'includes/header.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get date range from query parameters
$start_date = $_GET['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
$end_date = $_GET['end_date'] ?? date('Y-m-d');

// Get ad performance data
$stmt = $conn->prepare("
    SELECT
        DATE(event_timestamp) as date,
        ad_unit_id,
        event_type,
        COUNT(*) as count,
        SUM(CASE WHEN event_type = 'impression' THEN 1 ELSE 0 END) as impressions,
        SUM(CASE WHEN event_type = 'click' THEN 1 ELSE 0 END) as clicks
    FROM ad_analytics
    WHERE DATE(event_timestamp) BETWEEN :start_date AND :end_date
    GROUP BY DATE(event_timestamp), ad_unit_id, event_type
    ORDER BY date DESC
");
$stmt->bindParam(':start_date', $start_date);
$stmt->bindParam(':end_date', $end_date);
$stmt->execute();
$analytics_data = $stmt->fetchAll();

// Get summary statistics
$stmt = $conn->prepare("
    SELECT
        COUNT(CASE WHEN event_type = 'impression' THEN 1 END) as total_impressions,
        COUNT(CASE WHEN event_type = 'click' THEN 1 END) as total_clicks,
        COUNT(DISTINCT ad_unit_id) as active_ad_units,
        COUNT(DISTINCT device_id) as unique_users
    FROM ad_analytics
    WHERE DATE(event_timestamp) BETWEEN :start_date AND :end_date
");
$stmt->bindParam(':start_date', $start_date);
$stmt->bindParam(':end_date', $end_date);
$stmt->execute();
$summary = $stmt->fetch();

// Calculate CTR
$ctr = $summary['total_impressions'] > 0 ?
    ($summary['total_clicks'] / $summary['total_impressions']) * 100 : 0;

// Get daily performance data for chart
$stmt = $conn->prepare("
    SELECT
        DATE(event_timestamp) as date,
        SUM(CASE WHEN event_type = 'impression' THEN 1 ELSE 0 END) as impressions,
        SUM(CASE WHEN event_type = 'click' THEN 1 ELSE 0 END) as clicks
    FROM ad_analytics
    WHERE DATE(event_timestamp) BETWEEN :start_date AND :end_date
    GROUP BY DATE(event_timestamp)
    ORDER BY date ASC
");
$stmt->bindParam(':start_date', $start_date);
$stmt->bindParam(':end_date', $end_date);
$stmt->execute();
$daily_performance = $stmt->fetchAll();

// Get top performing ad units
$stmt = $conn->prepare("
    SELECT
        ad_unit_id,
        SUM(CASE WHEN event_type = 'impression' THEN 1 ELSE 0 END) as impressions,
        SUM(CASE WHEN event_type = 'click' THEN 1 ELSE 0 END) as clicks,
        (SUM(CASE WHEN event_type = 'click' THEN 1 ELSE 0 END) /
         NULLIF(SUM(CASE WHEN event_type = 'impression' THEN 1 ELSE 0 END), 0)) * 100 as ctr
    FROM ad_analytics
    WHERE DATE(event_timestamp) BETWEEN :start_date AND :end_date
    GROUP BY ad_unit_id
    ORDER BY impressions DESC
    LIMIT 10
");
$stmt->bindParam(':start_date', $start_date);
$stmt->bindParam(':end_date', $end_date);
$stmt->execute();
$top_ad_units = $stmt->fetchAll();
?>

<!-- Date Range Filter -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Date Range Filter</h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label for="start_date" class="form-label">Start Date</label>
                        <input type="date" class="form-control" id="start_date" name="start_date"
                               value="<?php echo htmlspecialchars($start_date); ?>">
                    </div>
                    <div class="col-md-4">
                        <label for="end_date" class="form-label">End Date</label>
                        <input type="date" class="form-control" id="end_date" name="end_date"
                               value="<?php echo htmlspecialchars($end_date); ?>">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>Apply Filter
                            </button>
                            <a href="ad-analytics.php" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh me-2"></i>Reset
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Impressions
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($summary['total_impressions']); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-eye fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Total Clicks
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($summary['total_clicks']); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Click-Through Rate
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($ctr, 2); ?>%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-percentage fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Active Ad Units
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($summary['active_ad_units']); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-ad fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Performance Chart -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Daily Performance Trend</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="performanceChart" height="100"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Top Performing Ad Units -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Top Performing Ad Units</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered" width="100%" cellspacing="0">
                        <thead>
                            <tr>
                                <th>Ad Unit ID</th>
                                <th>Impressions</th>
                                <th>Clicks</th>
                                <th>CTR (%)</th>
                                <th>Performance</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if (empty($top_ad_units)): ?>
                                <tr>
                                    <td colspan="5" class="text-center text-muted">No data available for the selected date range</td>
                                </tr>
                            <?php else: ?>
                                <?php foreach ($top_ad_units as $ad_unit): ?>
                                    <tr>
                                        <td>
                                            <code><?php echo htmlspecialchars($ad_unit['ad_unit_id']); ?></code>
                                        </td>
                                        <td><?php echo number_format($ad_unit['impressions']); ?></td>
                                        <td><?php echo number_format($ad_unit['clicks']); ?></td>
                                        <td><?php echo number_format($ad_unit['ctr'], 2); ?>%</td>
                                        <td>
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar"
                                                     style="width: <?php echo min($ad_unit['ctr'] * 10, 100); ?>%"
                                                     aria-valuenow="<?php echo $ad_unit['ctr']; ?>"
                                                     aria-valuemin="0" aria-valuemax="10">
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>

<?php
$page_scripts = "
<script>
// Performance Chart
const ctx = document.getElementById('performanceChart').getContext('2d');
const performanceChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: [" . implode(',', array_map(function($item) {
            return "'" . date('M j', strtotime($item['date'])) . "'";
        }, $daily_performance)) . "],
        datasets: [{
            label: 'Impressions',
            data: [" . implode(',', array_column($daily_performance, 'impressions')) . "],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1,
            fill: true
        }, {
            label: 'Clicks',
            data: [" . implode(',', array_column($daily_performance, 'clicks')) . "],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            tension: 0.1,
            fill: true
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                }
            },
            x: {
                grid: {
                    color: 'rgba(0, 0, 0, 0.1)'
                }
            }
        },
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: false
            }
        },
        interaction: {
            intersect: false,
            mode: 'index'
        }
    }
});
</script>
";

require_once 'includes/footer.php';
?>