<?php
/**
 * Background Notification Service
 * A standalone PHP service that runs continuously in the background
 * to process scheduled notifications without requiring browser or external scheduling
 *
 * Features:
 * - Runs continuously as a background process
 * - Automatic restart on errors
 * - Memory management
 * - Detailed logging
 * - Graceful shutdown handling
 *
 * Usage:
 * php background-notification-service.php
 *
 * To run in background (Windows):
 * start /B php background-notification-service.php
 *
 * To run in background (Linux):
 * nohup php background-notification-service.php > /dev/null 2>&1 &
 */

// Prevent script timeout
set_time_limit(0);
ini_set('memory_limit', '128M');

// Include required files
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/NotificationManager.php';
require_once __DIR__ . '/../classes/FirebaseV1Service.php';

class BackgroundNotificationService {
    private $isRunning = true;
    private $logFile;
    private $pidFile;
    private $checkInterval = 60; // Check every 60 seconds
    private $maxMemoryUsage = 100 * 1024 * 1024; // 100MB
    private $startTime;
    private $processedCount = 0;
    private $errorCount = 0;

    public function __construct() {
        $this->logFile = __DIR__ . '/background-service.log';
        $this->pidFile = __DIR__ . '/background-service.pid';
        $this->startTime = time();

        // Register signal handlers for graceful shutdown
        if (function_exists('pcntl_signal')) {
            pcntl_signal(SIGTERM, [$this, 'shutdown']);
            pcntl_signal(SIGINT, [$this, 'shutdown']);
        }

        // Register shutdown function
        register_shutdown_function([$this, 'cleanup']);
    }

    /**
     * Start the background service
     */
    public function start() {
        $this->log("=== Background Notification Service Starting ===");
        $this->log("Process ID: " . getmypid());
        $this->log("Check interval: {$this->checkInterval} seconds");
        $this->log("Max memory usage: " . ($this->maxMemoryUsage / 1024 / 1024) . "MB");

        // Write PID file
        file_put_contents($this->pidFile, getmypid());

        // Check if another instance is running
        if ($this->isAnotherInstanceRunning()) {
            $this->log("Another instance is already running. Exiting.");
            exit(1);
        }

        $this->log("Service started successfully");

        // Main service loop
        while ($this->isRunning) {
            try {
                $this->processScheduledNotifications();
                $this->checkMemoryUsage();
                $this->sleep();

                // Process signals if available
                if (function_exists('pcntl_signal_dispatch')) {
                    pcntl_signal_dispatch();
                }

            } catch (Exception $e) {
                $this->errorCount++;
                $this->log("ERROR: " . $e->getMessage());
                $this->log("Stack trace: " . $e->getTraceAsString());

                // If too many errors, restart
                if ($this->errorCount > 10) {
                    $this->log("Too many errors. Restarting service...");
                    $this->restart();
                }

                // Wait before retrying
                sleep(30);
            }
        }

        $this->log("=== Background Notification Service Stopped ===");
    }

    /**
     * Process scheduled notifications
     */
    private function processScheduledNotifications() {
        try {
            $notificationManager = new NotificationManager();
            $scheduledNotifications = $notificationManager->getScheduledNotifications();

            if (empty($scheduledNotifications)) {
                $this->log("No scheduled notifications to process", 'DEBUG');
                return;
            }

            $this->log("Found " . count($scheduledNotifications) . " scheduled notifications to process");

            $successful = 0;
            $failed = 0;

            foreach ($scheduledNotifications as $notification) {
                $notificationId = $notification['id'];
                $title = $notification['title'];

                $this->log("Processing notification ID: $notificationId - '$title'");

                try {
                    $result = $notificationManager->sendNotification($notificationId);

                    if ($result['success']) {
                        $successful++;
                        $this->processedCount++;
                        $this->log("✓ Successfully sent notification ID: $notificationId to " . $result['sent_count'] . " users");
                    } else {
                        $failed++;
                        $this->log("✗ Failed to send notification ID: $notificationId - " . $result['message']);
                    }

                } catch (Exception $e) {
                    $failed++;
                    $this->log("✗ Exception sending notification ID: $notificationId - " . $e->getMessage());
                }

                // Small delay between notifications
                usleep(100000); // 0.1 second
            }

            if ($successful > 0 || $failed > 0) {
                $this->log("Batch complete: $successful successful, $failed failed");
            }

        } catch (Exception $e) {
            throw new Exception("Error processing scheduled notifications: " . $e->getMessage());
        }
    }

    /**
     * Check memory usage and restart if needed
     */
    private function checkMemoryUsage() {
        $memoryUsage = memory_get_usage(true);

        if ($memoryUsage > $this->maxMemoryUsage) {
            $this->log("Memory usage exceeded limit (" . ($memoryUsage / 1024 / 1024) . "MB). Restarting...");
            $this->restart();
        }
    }

    /**
     * Sleep for the check interval
     */
    private function sleep() {
        sleep($this->checkInterval);
    }

    /**
     * Check if another instance is running
     */
    private function isAnotherInstanceRunning() {
        if (!file_exists($this->pidFile)) {
            return false;
        }

        $pid = trim(file_get_contents($this->pidFile));

        // Check if process is still running (Windows)
        if (PHP_OS_FAMILY === 'Windows') {
            $output = shell_exec("tasklist /FI \"PID eq $pid\" 2>NUL");
            return strpos($output, $pid) !== false;
        } else {
            // Unix/Linux
            return file_exists("/proc/$pid");
        }
    }

    /**
     * Log message with timestamp
     */
    private function log($message, $level = 'INFO') {
        $timestamp = date('Y-m-d H:i:s');
        $memoryUsage = round(memory_get_usage(true) / 1024 / 1024, 2);
        $uptime = $this->formatUptime(time() - $this->startTime);

        $logEntry = "[$timestamp] [$level] [Memory: {$memoryUsage}MB] [Uptime: $uptime] [Processed: {$this->processedCount}] $message" . PHP_EOL;

        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);

        // Also output to console
        echo $logEntry;
    }

    /**
     * Format uptime in human readable format
     */
    private function formatUptime($seconds) {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $seconds = $seconds % 60;

        return sprintf("%02d:%02d:%02d", $hours, $minutes, $seconds);
    }

    /**
     * Graceful shutdown
     */
    public function shutdown($signal = null) {
        $this->log("Received shutdown signal" . ($signal ? " ($signal)" : ""));
        $this->isRunning = false;
    }

    /**
     * Restart the service
     */
    private function restart() {
        $this->log("Restarting service...");
        $this->cleanup();

        // Restart the script
        $command = "php " . __FILE__;
        if (PHP_OS_FAMILY === 'Windows') {
            pclose(popen("start /B $command", "r"));
        } else {
            exec("nohup $command > /dev/null 2>&1 &");
        }

        exit(0);
    }

    /**
     * Cleanup on shutdown
     */
    public function cleanup() {
        if (file_exists($this->pidFile)) {
            unlink($this->pidFile);
        }

        $uptime = $this->formatUptime(time() - $this->startTime);
        $this->log("Service cleanup complete. Total uptime: $uptime, Total processed: {$this->processedCount}");
    }
}

// Check if running from command line
if (php_sapi_name() !== 'cli') {
    die("This script must be run from command line\n");
}

// Handle command line arguments
$command = $argv[1] ?? 'start';

switch ($command) {
    case 'start':
        $service = new BackgroundNotificationService();
        $service->start();
        break;

    case 'stop':
        $pidFile = __DIR__ . '/background-service.pid';
        if (file_exists($pidFile)) {
            $pid = trim(file_get_contents($pidFile));
            if (PHP_OS_FAMILY === 'Windows') {
                exec("taskkill /PID $pid /F");
            } else {
                exec("kill $pid");
            }
            echo "Service stopped (PID: $pid)\n";
        } else {
            echo "Service is not running\n";
        }
        break;

    case 'status':
        $pidFile = __DIR__ . '/background-service.pid';
        if (file_exists($pidFile)) {
            $pid = trim(file_get_contents($pidFile));
            echo "Service is running (PID: $pid)\n";
        } else {
            echo "Service is not running\n";
        }
        break;

    case 'restart':
        // Stop first
        $pidFile = __DIR__ . '/background-service.pid';
        if (file_exists($pidFile)) {
            $pid = trim(file_get_contents($pidFile));
            if (PHP_OS_FAMILY === 'Windows') {
                exec("taskkill /PID $pid /F");
            } else {
                exec("kill $pid");
            }
            sleep(2);
        }

        // Start again
        $service = new BackgroundNotificationService();
        $service->start();
        break;

    default:
        echo "Usage: php background-notification-service.php [start|stop|status|restart]\n";
        echo "  start   - Start the background service\n";
        echo "  stop    - Stop the background service\n";
        echo "  status  - Check service status\n";
        echo "  restart - Restart the service\n";
        exit(1);
}
?>
