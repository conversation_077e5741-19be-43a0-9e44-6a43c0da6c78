package com.anginatech.textrepeater.network;

import android.content.Context;
import android.util.Log;

import com.anginatech.textrepeater.Config;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.File;
import java.util.concurrent.TimeUnit;

import okhttp3.Cache;
import okhttp3.CacheControl;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * Modern API Client with advanced caching and performance optimizations
 * Replaces the slow Volley-based ApiClient with much faster Retrofit + OkHttp
 */
public class ModernApiClient {
    private static final String TAG = "ModernApiClient";
    private static ModernApiClient instance;
    private ApiService apiService;
    private OkHttpClient okHttpClient;
    private Context context;

    // Cache settings for better performance
    private static final int CACHE_SIZE = 10 * 1024 * 1024; // 10MB cache
    private static final int CONNECT_TIMEOUT = 15; // seconds
    private static final int READ_TIMEOUT = 30; // seconds
    private static final int WRITE_TIMEOUT = 30; // seconds

    private ModernApiClient(Context context) {
        this.context = context.getApplicationContext();
        initializeClient();
    }

    public static synchronized ModernApiClient getInstance(Context context) {
        if (instance == null) {
            instance = new ModernApiClient(context);
        }
        return instance;
    }

    private void initializeClient() {
        // Create cache directory
        File cacheDir = new File(context.getCacheDir(), "http_cache");
        Cache cache = new Cache(cacheDir, CACHE_SIZE);

        // Create logging interceptor for debugging
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
        loggingInterceptor.setLevel(Config.DEBUG_MODE ?
            HttpLoggingInterceptor.Level.BODY : HttpLoggingInterceptor.Level.NONE);

        // Create cache interceptor for offline support
        Interceptor cacheInterceptor = chain -> {
            Request request = chain.request();

            // Add cache control for GET requests
            if (request.method().equals("GET")) {
                CacheControl cacheControl = new CacheControl.Builder()
                    .maxAge(5, TimeUnit.MINUTES) // Cache for 5 minutes
                    .build();

                request = request.newBuilder()
                    .cacheControl(cacheControl)
                    .build();
            }

            Response response = chain.proceed(request);

            // Cache successful responses
            if (response.isSuccessful()) {
                response = response.newBuilder()
                    .header("Cache-Control", "public, max-age=300") // 5 minutes
                    .build();
            }

            return response;
        };

        // Create offline cache interceptor
        Interceptor offlineCacheInterceptor = chain -> {
            Request request = chain.request();

            if (!NetworkUtils.isNetworkAvailable(context)) {
                Log.d(TAG, "No network available, using cache");
                CacheControl cacheControl = new CacheControl.Builder()
                    .maxStale(7, TimeUnit.DAYS) // Use cache up to 7 days when offline
                    .build();

                request = request.newBuilder()
                    .cacheControl(cacheControl)
                    .build();
            }

            return chain.proceed(request);
        };

        // Build OkHttp client with optimizations
        okHttpClient = new OkHttpClient.Builder()
            .cache(cache)
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.SECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.SECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.SECONDS)
            .addInterceptor(loggingInterceptor)
            .addInterceptor(cacheInterceptor)
            .addNetworkInterceptor(offlineCacheInterceptor)
            .retryOnConnectionFailure(true)
            .build();

        // Create Gson with optimized settings
        Gson gson = new GsonBuilder()
            .setLenient()
            .create();

        // Build Retrofit instance with proper base URL (must end with /)
        Retrofit retrofit = new Retrofit.Builder()
            .baseUrl(Config.BASE_URL) // This now ends with / as required by Retrofit
            .client(okHttpClient)
            .addConverterFactory(GsonConverterFactory.create(gson))
            .build();

        apiService = retrofit.create(ApiService.class);

        Log.d(TAG, "Modern API Client initialized with caching and performance optimizations");
    }

    public ApiService getApiService() {
        return apiService;
    }

    /**
     * Clear cache for fresh data
     */
    public void clearCache() {
        try {
            if (okHttpClient.cache() != null) {
                okHttpClient.cache().evictAll();
                Log.d(TAG, "Cache cleared successfully");
            }
        } catch (Exception e) {
            Log.e(TAG, "Error clearing cache: " + e.getMessage());
        }
    }

    /**
     * Get cache size in bytes
     */
    public long getCacheSize() {
        try {
            if (okHttpClient.cache() != null) {
                return okHttpClient.cache().size();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting cache size: " + e.getMessage());
        }
        return 0;
    }

    /**
     * Check if request can be served from cache
     */
    public boolean isCacheAvailable() {
        return okHttpClient.cache() != null && getCacheSize() > 0;
    }
}
