<?php
require_once 'config/database.php';

class Auth {
    private $conn;
    private $table_name = "admin_users";

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Login user with username/email and password
     */
    public function login($username, $password) {
        try {
            // Check for too many failed attempts
            if ($this->isAccountLocked($username)) {
                return [
                    'success' => false,
                    'message' => 'Account temporarily locked due to too many failed attempts. Try again later.'
                ];
            }

            $query = "SELECT id, username, email, password, full_name, role, status, is_active, created_at, last_login
                     FROM " . $this->table_name . "
                     WHERE (username = :username OR email = :username) AND (is_active = 1 OR status = 'active')";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':username', $username);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch();

                if (password_verify($password, $user['password'])) {
                    // Successful login
                    $this->updateLastLogin($user['id']);
                    $this->clearFailedAttempts($username);
                    $this->logLoginHistory($user['id'], true);
                    $this->createSession($user);

                    return [
                        'success' => true,
                        'message' => 'Login successful',
                        'user' => $user
                    ];
                } else {
                    // Wrong password
                    $this->recordFailedAttempt($username);
                    return [
                        'success' => false,
                        'message' => 'Invalid credentials'
                    ];
                }
            } else {
                // User not found
                $this->recordFailedAttempt($username);
                return [
                    'success' => false,
                    'message' => 'Invalid credentials'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Login failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Create user session
     */
    private function createSession($user) {
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_id'] = $user['id'];
        $_SESSION['admin_username'] = $user['username'];
        $_SESSION['admin_email'] = $user['email'];
        $_SESSION['admin_full_name'] = $user['full_name'];
        $_SESSION['admin_role'] = $user['role'];
        $_SESSION['login_time'] = time();

        // Regenerate session ID for security
        session_regenerate_id(true);
    }

    /**
     * Check if user is logged in
     */
    public function isLoggedIn() {
        if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
            return false;
        }

        // Check session timeout
        if (isset($_SESSION['login_time']) &&
            (time() - $_SESSION['login_time']) > Config::SESSION_TIMEOUT) {
            $this->logout();
            return false;
        }

        return true;
    }

    /**
     * Logout user
     */
    public function logout() {
        session_unset();
        session_destroy();
        session_start();
    }

    /**
     * Update last login time
     */
    private function updateLastLogin($user_id) {
        $query = "UPDATE " . $this->table_name . " SET last_login = NOW() WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $user_id);
        $stmt->execute();
    }

    /**
     * Log login history
     */
    private function logLoginHistory($user_id, $successful = true) {
        try {
            $ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
            $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';

            $query = "INSERT INTO admin_login_history
                     (user_id, login_time, ip_address, user_agent, login_successful)
                     VALUES (:user_id, NOW(), :ip_address, :user_agent, :login_successful)";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':user_id', $user_id);
            $stmt->bindParam(':ip_address', $ip);
            $stmt->bindParam(':user_agent', $user_agent);
            $stmt->bindParam(':login_successful', $successful, PDO::PARAM_BOOL);
            $stmt->execute();
        } catch (Exception $e) {
            // Don't fail login if logging fails
            error_log("Failed to log login history: " . $e->getMessage());
        }
    }

    /**
     * Record failed login attempt
     */
    private function recordFailedAttempt($username) {
        $ip = $_SERVER['REMOTE_ADDR'];
        $key = 'failed_attempts_' . md5($username . $ip);

        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = [];
        }

        $_SESSION[$key][] = time();

        // Keep only attempts from last hour
        $_SESSION[$key] = array_filter($_SESSION[$key], function($time) {
            return (time() - $time) < 3600;
        });
    }

    /**
     * Check if account is locked
     */
    private function isAccountLocked($username) {
        $ip = $_SERVER['REMOTE_ADDR'];
        $key = 'failed_attempts_' . md5($username . $ip);

        if (!isset($_SESSION[$key])) {
            return false;
        }

        // Count recent failed attempts
        $recent_attempts = array_filter($_SESSION[$key], function($time) {
            return (time() - $time) < Config::LOGIN_LOCKOUT_TIME;
        });

        return count($recent_attempts) >= Config::MAX_LOGIN_ATTEMPTS;
    }

    /**
     * Clear failed attempts
     */
    private function clearFailedAttempts($username) {
        $ip = $_SERVER['REMOTE_ADDR'];
        $key = 'failed_attempts_' . md5($username . $ip);
        unset($_SESSION[$key]);
    }

    /**
     * Check user role
     */
    public function hasRole($required_role) {
        if (!$this->isLoggedIn()) {
            return false;
        }

        $user_role = $_SESSION['admin_role'];
        $roles = ['moderator', 'admin', 'super_admin'];

        $user_level = array_search($user_role, $roles);
        $required_level = array_search($required_role, $roles);

        return $user_level !== false && $required_level !== false && $user_level >= $required_level;
    }

    /**
     * Get current user info
     */
    public function getCurrentUser() {
        if (!$this->isLoggedIn()) {
            return null;
        }

        // Fetch fresh user data from database
        try {
            $query = "SELECT id, username, email, full_name, role, status, is_active, created_at, last_login
                     FROM " . $this->table_name . "
                     WHERE id = :id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $_SESSION['admin_id']);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                return $stmt->fetch();
            }
        } catch (Exception $e) {
            // Fallback to session data if database query fails
            error_log("Failed to fetch current user: " . $e->getMessage());
        }

        // Fallback to session data
        return [
            'id' => $_SESSION['admin_id'],
            'username' => $_SESSION['admin_username'],
            'email' => $_SESSION['admin_email'],
            'full_name' => $_SESSION['admin_full_name'],
            'role' => $_SESSION['admin_role'],
            'status' => 'active', // Default fallback
            'created_at' => null,
            'last_login' => null
        ];
    }

    /**
     * Change password
     */
    public function changePassword($user_id, $old_password, $new_password) {
        try {
            // Verify old password
            $query = "SELECT password FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $user_id);
            $stmt->execute();

            if ($stmt->rowCount() > 0) {
                $user = $stmt->fetch();

                if (!password_verify($old_password, $user['password'])) {
                    return [
                        'success' => false,
                        'message' => 'Current password is incorrect'
                    ];
                }

                // Update password
                $new_hash = password_hash($new_password, PASSWORD_DEFAULT);
                $update_query = "UPDATE " . $this->table_name . "
                               SET password = :password, updated_at = NOW()
                               WHERE id = :id";

                $update_stmt = $this->conn->prepare($update_query);
                $update_stmt->bindParam(':password', $new_hash);
                $update_stmt->bindParam(':id', $user_id);

                if ($update_stmt->execute()) {
                    return [
                        'success' => true,
                        'message' => 'Password changed successfully'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Failed to update password'
                    ];
                }
            } else {
                return [
                    'success' => false,
                    'message' => 'User not found'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error changing password: ' . $e->getMessage()
            ];
        }
    }
}
?>
