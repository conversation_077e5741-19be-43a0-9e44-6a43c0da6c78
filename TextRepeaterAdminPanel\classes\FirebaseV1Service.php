<?php

/**
 * Firebase Cloud Messaging v1 API Service
 * Handles authentication and messaging using Firebase v1 API with service account
 */
class FirebaseV1Service {
    private $serviceAccountPath;
    private $projectId;
    private $accessToken;
    private $tokenExpiry;
    
    public function __construct($serviceAccountPath = null) {
        $this->serviceAccountPath = $serviceAccountPath ?: __DIR__ . '/../includes/service_account.json';
        $this->loadServiceAccount();
    }
    
    /**
     * Load service account configuration
     */
    private function loadServiceAccount() {
        if (!file_exists($this->serviceAccountPath)) {
            throw new Exception('Service account file not found: ' . $this->serviceAccountPath);
        }
        
        $serviceAccount = json_decode(file_get_contents($this->serviceAccountPath), true);
        if (!$serviceAccount) {
            throw new Exception('Invalid service account JSON file');
        }
        
        $this->projectId = $serviceAccount['project_id'];
    }
    
    /**
     * Generate JWT token for Firebase v1 API authentication
     */
    private function generateJWT() {
        $serviceAccount = json_decode(file_get_contents($this->serviceAccountPath), true);
        
        $header = [
            'alg' => 'RS256',
            'typ' => 'JWT'
        ];
        
        $now = time();
        $payload = [
            'iss' => $serviceAccount['client_email'],
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
            'aud' => 'https://oauth2.googleapis.com/token',
            'iat' => $now,
            'exp' => $now + 3600 // 1 hour
        ];
        
        $headerEncoded = $this->base64UrlEncode(json_encode($header));
        $payloadEncoded = $this->base64UrlEncode(json_encode($payload));
        
        $signature = '';
        $data = $headerEncoded . '.' . $payloadEncoded;
        
        // Sign with private key
        $privateKey = $serviceAccount['private_key'];
        openssl_sign($data, $signature, $privateKey, OPENSSL_ALGO_SHA256);
        $signatureEncoded = $this->base64UrlEncode($signature);
        
        return $data . '.' . $signatureEncoded;
    }
    
    /**
     * Base64 URL encode
     */
    private function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    /**
     * Get access token for Firebase v1 API
     */
    private function getAccessToken() {
        // Check if token is still valid
        if ($this->accessToken && $this->tokenExpiry && time() < $this->tokenExpiry - 300) {
            return $this->accessToken;
        }
        
        $jwt = $this->generateJWT();
        
        $postData = [
            'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
            'assertion' => $jwt
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'https://oauth2.googleapis.com/token');
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception('Failed to get access token. HTTP Code: ' . $httpCode . ', Response: ' . $response);
        }
        
        $tokenData = json_decode($response, true);
        if (!$tokenData || !isset($tokenData['access_token'])) {
            throw new Exception('Invalid token response: ' . $response);
        }
        
        $this->accessToken = $tokenData['access_token'];
        $this->tokenExpiry = time() + ($tokenData['expires_in'] ?? 3600);
        
        return $this->accessToken;
    }
    
    /**
     * Send notification using Firebase v1 API
     */
    public function sendNotification($tokens, $title, $body, $data = [], $imageUrl = null) {
        try {
            $accessToken = $this->getAccessToken();
            $url = "https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send";
            
            $results = [];
            $successCount = 0;
            $failureCount = 0;
            
            // Send to each token individually (v1 API doesn't support batch sending like legacy API)
            foreach ($tokens as $token) {
                $message = [
                    'message' => [
                        'token' => $token,
                        'notification' => [
                            'title' => $title,
                            'body' => $body
                        ],
                        'data' => $data,
                        'android' => [
                            'notification' => [
                                'icon' => 'ic_notification',
                                'sound' => 'default',
                                'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                            ]
                        ]
                    ]
                ];
                
                // Add image if provided
                if ($imageUrl) {
                    $message['message']['notification']['image'] = $imageUrl;
                }
                
                $headers = [
                    'Authorization: Bearer ' . $accessToken,
                    'Content-Type: application/json'
                ];
                
                $ch = curl_init();
                curl_setopt($ch, CURLOPT_URL, $url);
                curl_setopt($ch, CURLOPT_POST, true);
                curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
                curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
                curl_setopt($ch, CURLOPT_TIMEOUT, 30);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                curl_close($ch);
                
                if ($httpCode === 200) {
                    $successCount++;
                    $results[] = [
                        'token' => $token,
                        'success' => true,
                        'response' => json_decode($response, true)
                    ];
                } else {
                    $failureCount++;
                    $results[] = [
                        'token' => $token,
                        'success' => false,
                        'error' => $response,
                        'http_code' => $httpCode
                    ];
                }
            }
            
            return [
                'success' => $successCount > 0,
                'sent_count' => $successCount,
                'failed_count' => $failureCount,
                'total_count' => count($tokens),
                'results' => $results
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Firebase v1 API error: ' . $e->getMessage(),
                'sent_count' => 0,
                'failed_count' => count($tokens)
            ];
        }
    }
    
    /**
     * Send notification to topic using Firebase v1 API
     */
    public function sendToTopic($topic, $title, $body, $data = [], $imageUrl = null) {
        try {
            $accessToken = $this->getAccessToken();
            $url = "https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send";
            
            $message = [
                'message' => [
                    'topic' => $topic,
                    'notification' => [
                        'title' => $title,
                        'body' => $body
                    ],
                    'data' => $data,
                    'android' => [
                        'notification' => [
                            'icon' => 'ic_notification',
                            'sound' => 'default',
                            'click_action' => 'FLUTTER_NOTIFICATION_CLICK'
                        ]
                    ]
                ]
            ];
            
            // Add image if provided
            if ($imageUrl) {
                $message['message']['notification']['image'] = $imageUrl;
            }
            
            $headers = [
                'Authorization: Bearer ' . $accessToken,
                'Content-Type: application/json'
            ];
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($message));
            curl_setopt($ch, CURLOPT_TIMEOUT, 30);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            if ($httpCode === 200) {
                return [
                    'success' => true,
                    'response' => json_decode($response, true)
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to send to topic. HTTP Code: ' . $httpCode,
                    'response' => $response
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Firebase v1 API error: ' . $e->getMessage()
            ];
        }
    }
}
