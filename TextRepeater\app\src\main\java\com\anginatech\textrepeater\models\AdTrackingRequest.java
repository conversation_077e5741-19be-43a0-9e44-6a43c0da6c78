package com.anginatech.textrepeater.models;

import com.google.gson.annotations.SerializedName;

/**
 * Ad Tracking Request Model
 */
public class AdTrackingRequest {
    
    @SerializedName("device_id")
    private String deviceId;
    
    @SerializedName("ad_type")
    private String adType;
    
    @SerializedName("ad_unit_id")
    private String adUnitId;
    
    @SerializedName("event_type")
    private String eventType;
    
    @SerializedName("revenue")
    private double revenue;
    
    @SerializedName("currency")
    private String currency;
    
    @SerializedName("session_id")
    private String sessionId;
    
    @SerializedName("app_version")
    private String appVersion;
    
    @SerializedName("placement")
    private String placement;
    
    @SerializedName("network")
    private String network;

    // Constructors
    public AdTrackingRequest() {}

    public AdTrackingRequest(String deviceId, String adType, String eventType) {
        this.deviceId = deviceId;
        this.adType = adType;
        this.eventType = eventType;
        this.currency = "USD";
        this.network = "admob";
    }

    // Getters and Setters
    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getAdType() {
        return adType;
    }

    public void setAdType(String adType) {
        this.adType = adType;
    }

    public String getAdUnitId() {
        return adUnitId;
    }

    public void setAdUnitId(String adUnitId) {
        this.adUnitId = adUnitId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public double getRevenue() {
        return revenue;
    }

    public void setRevenue(double revenue) {
        this.revenue = revenue;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getPlacement() {
        return placement;
    }

    public void setPlacement(String placement) {
        this.placement = placement;
    }

    public String getNetwork() {
        return network;
    }

    public void setNetwork(String network) {
        this.network = network;
    }
}
