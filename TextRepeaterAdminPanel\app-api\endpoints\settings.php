<?php
/**
 * Settings Endpoint Handler
 * Handles all app settings and configuration API requests
 */

class SettingsEndpoint {
    private $conn;
    private $response;

    public function __construct() {
        $this->conn = getAPIDatabase();
        $this->response = new APIResponse();
    }

    /**
     * Handle settings endpoint requests
     */
    public function handle($method, $pathParts) {
        $action = $pathParts[1] ?? 'settings';

        switch ($action) {
            case 'settings':
                return $this->handleAppSettings($method);

            case 'config':
                return $this->handleAppConfig($method);

            case 'features':
                return $this->handleFeatures($method);

            case 'version':
                return $this->handleVersionCheck($method);

            case 'maintenance':
                return $this->handleMaintenanceStatus($method);

            default:
                return $this->response->notFound('Settings endpoint');
        }
    }

    /**
     * Handle app settings requests
     */
    private function handleAppSettings($method) {
        if ($method !== 'GET') {
            return $this->response->error('Method not allowed', 405);
        }

        try {
            // First check if app_settings table exists
            $checkTable = "SHOW TABLES LIKE 'app_settings'";
            $stmt = $this->conn->prepare($checkTable);
            $stmt->execute();

            if ($stmt->rowCount() == 0) {
                // Table doesn't exist, return default settings
                $settings = [
                    ['setting_key' => 'app_version', 'setting_value' => '3.9.1', 'setting_type' => 'string', 'description' => 'Current app version'],
                    ['setting_key' => 'maintenance_mode', 'setting_value' => 'false', 'setting_type' => 'boolean', 'description' => 'Maintenance mode status'],
                    ['setting_key' => 'force_update', 'setting_value' => 'false', 'setting_type' => 'boolean', 'description' => 'Force app update'],
                    ['setting_key' => 'min_supported_version', 'setting_value' => '3.0.0', 'setting_type' => 'string', 'description' => 'Minimum supported version']
                ];
            } else {
                // Table exists, try to query it
                $query = "SELECT setting_key, setting_value, setting_type, description
                         FROM app_settings
                         WHERE is_public = 1
                         ORDER BY setting_key";

                $stmt = $this->conn->prepare($query);
                $stmt->execute();
                $settings = $stmt->fetchAll();

                // If no settings found, return defaults
                if (empty($settings)) {
                    $settings = [
                        ['setting_key' => 'app_version', 'setting_value' => '3.9.1', 'setting_type' => 'string', 'description' => 'Current app version'],
                        ['setting_key' => 'maintenance_mode', 'setting_value' => 'false', 'setting_type' => 'boolean', 'description' => 'Maintenance mode status']
                    ];
                }
            }

            // Create Android-compatible response format
            $result = [];
            foreach ($settings as $setting) {
                $value = $this->convertSettingValue($setting['setting_value'], $setting['setting_type']);
                $result[$setting['setting_key']] = $value; // Direct value assignment for Android compatibility
            }

            // Ensure required fields exist with proper types
            $result['app_version'] = $result['app_version'] ?? '3.9.1';
            $result['maintenance_mode'] = isset($result['maintenance_mode']) ? (bool)$result['maintenance_mode'] : false;
            $result['force_update'] = isset($result['force_update']) ? (bool)$result['force_update'] : false;
            $result['min_supported_version'] = $result['min_supported_version'] ?? '3.0.0';
            $result['ad_refresh_interval'] = isset($result['ad_refresh_interval']) ? (int)$result['ad_refresh_interval'] : 30;
            $result['notification_enabled'] = isset($result['notification_enabled']) ? (bool)$result['notification_enabled'] : true;

            // Add cache headers
            header('Cache-Control: public, max-age=300'); // 5 minutes cache

            return $this->response->success($result);

        } catch (Exception $e) {
            return $this->response->serverError('Failed to get app settings');
        }
    }

    /**
     * Handle app configuration requests
     */
    private function handleAppConfig($method) {
        if ($method !== 'GET') {
            return $this->response->error('Method not allowed', 405);
        }

        try {
            $config = [
                'app' => [
                    'name' => APP_NAME,
                    'package' => APP_PACKAGE,
                    'version' => API_VERSION,
                    'min_version' => MIN_APP_VERSION
                ],
                'features' => [
                    'admob_enabled' => ADMOB_API_ENABLED,
                    'notifications_enabled' => !empty(FCM_SERVER_KEY),
                    'analytics_enabled' => true,
                    'user_management_enabled' => true
                ],
                'limits' => [
                    'rate_limit_per_hour' => API_RATE_LIMIT,
                    'request_timeout' => API_TIMEOUT
                ],
                'endpoints' => [
                    'ads' => '/ads',
                    'users' => '/users',
                    'settings' => '/settings',
                    'notifications' => '/notifications'
                ]
            ];

            // Add cache headers
            header('Cache-Control: public, max-age=600'); // 10 minutes cache

            return $this->response->success($config);

        } catch (Exception $e) {
            return $this->response->serverError('Failed to get app configuration');
        }
    }

    /**
     * Handle feature flags requests
     */
    private function handleFeatures($method) {
        if ($method !== 'GET') {
            return $this->response->error('Method not allowed', 405);
        }

        try {
            $query = "SELECT feature_key, is_enabled, rollout_percentage, description
                     FROM feature_flags
                     WHERE is_active = 1
                     ORDER BY feature_key";

            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $features = $stmt->fetchAll();

            $result = [];
            foreach ($features as $feature) {
                $result[$feature['feature_key']] = [
                    'enabled' => (bool)$feature['is_enabled'],
                    'rollout_percentage' => (int)$feature['rollout_percentage'],
                    'description' => $feature['description']
                ];
            }

            // Add cache headers
            header('Cache-Control: public, max-age=300'); // 5 minutes cache

            return $this->response->success($result);

        } catch (Exception $e) {
            return $this->response->serverError('Failed to get feature flags');
        }
    }

    /**
     * Handle version check requests
     */
    private function handleVersionCheck($method) {
        if ($method !== 'GET') {
            return $this->response->error('Method not allowed', 405);
        }

        $currentVersion = $_GET['version'] ?? '';
        $platform = $_GET['platform'] ?? 'android';

        try {
            $query = "SELECT
                        latest_version,
                        min_supported_version,
                        force_update,
                        update_message,
                        download_url
                     FROM app_versions
                     WHERE platform = :platform AND is_active = 1
                     ORDER BY created_at DESC
                     LIMIT 1";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':platform', $platform);
            $stmt->execute();

            $versionInfo = $stmt->fetch();

            if ($versionInfo) {
                $needsUpdate = version_compare($currentVersion, $versionInfo['latest_version'], '<');
                $isSupported = version_compare($currentVersion, $versionInfo['min_supported_version'], '>=');

                $result = [
                    'current_version' => $currentVersion,
                    'latest_version' => $versionInfo['latest_version'],
                    'min_supported_version' => $versionInfo['min_supported_version'],
                    'needs_update' => $needsUpdate,
                    'is_supported' => $isSupported,
                    'force_update' => (bool)$versionInfo['force_update'],
                    'update_message' => $versionInfo['update_message'],
                    'download_url' => $versionInfo['download_url']
                ];

                return $this->response->success($result);
            } else {
                return $this->response->notFound('Version information');
            }

        } catch (Exception $e) {
            return $this->response->serverError('Failed to check version');
        }
    }

    /**
     * Handle maintenance status requests
     */
    private function handleMaintenanceStatus($method) {
        if ($method !== 'GET') {
            return $this->response->error('Method not allowed', 405);
        }

        $isMaintenanceMode = isMaintenanceMode();

        $result = [
            'maintenance_mode' => $isMaintenanceMode,
            'timestamp' => date('c')
        ];

        if ($isMaintenanceMode) {
            // Try to get maintenance message from file
            $maintenanceFile = '../maintenance.flag';
            if (file_exists($maintenanceFile)) {
                $maintenanceData = file_get_contents($maintenanceFile);
                $data = json_decode($maintenanceData, true);

                if ($data) {
                    $result['message'] = $data['message'] ?? 'System is under maintenance';
                    $result['estimated_end'] = $data['estimated_end'] ?? null;
                } else {
                    $result['message'] = 'System is under maintenance';
                }
            }
        }

        return $this->response->success($result);
    }

    /**
     * Convert setting value based on type
     */
    private function convertSettingValue($value, $type) {
        switch ($type) {
            case 'boolean':
                return filter_var($value, FILTER_VALIDATE_BOOLEAN);

            case 'integer':
                return intval($value);

            case 'float':
                return floatval($value);

            case 'json':
                $decoded = json_decode($value, true);
                return $decoded !== null ? $decoded : $value;

            case 'array':
                return explode(',', $value);

            default:
                return $value;
        }
    }
}
?>
