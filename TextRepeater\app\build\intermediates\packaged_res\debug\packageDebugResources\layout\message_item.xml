<!-- item_sms.xml -->
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"

    >


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/smsItemClick"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        android:layout_marginTop="10dp"


        >


        <androidx.appcompat.widget.AppCompatCheckBox
            android:id="@+id/checkBoxSms"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:layout_marginStart="10dp"
            app:buttonTint="@color/love"
            />

        <TextView
            android:id="@+id/smsText"
            android:layout_width="290dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            android:fontFamily="@font/artifika"
            android:gravity="start"
            android:lineSpacingExtra="2dp"
            android:layout_marginEnd="16dp"
            android:text="Hello"
            android:textColor="@color/primary_text"
            android:textSize="15sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@+id/checkBoxSms"
            app:layout_constraintTop_toTopOf="parent" />


        <View
            android:layout_width="match_parent"
            android:layout_height="0.8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/smsText"
            app:layout_constraintVertical_bias="1.0"
            android:background="@color/emoji_view_color"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>