package com.anginatech.textrepeater;

import android.content.Context;

import java.util.ArrayList;
import java.util.List;

public class EmojiListManager {
    private List<String> emojiList;
    private Context context;

    public EmojiListManager() {
        emojiList = new ArrayList<>();
        initializeEmojiList();
    }

    private void initializeEmojiList() {


        emojiList.add(
                "🗣️💬👋\n" +
                        "👋 HI! 👋\n" +
                        "💬 💬\n" +
                        "🗣️💬👋"
        );


        emojiList.add(
                "👋🌟👋\n" +
                        "🌟 HELLO! 🌟\n" +
                        "👋    👋\n" +
                        "🌟👋🌟"
        );

        emojiList.add(
                "💬☀️💛☀️💬\n" +
                        "🌟 HEY THERE! 🌟\n" +
                        "💬☀️💛☀️💬"
        );

        emojiList.add(
                "🤗💙🤗\n" +
                        "💙 HOW ARE 💙\n" +
                        "🤗  YOU? 🤗\n" +
                        "💙🤗💙"
        );

        emojiList.add(
                "🙌😊🙌\n" +
                        "😊 WHAT'S UP? 😊\n" +
                        "🙌  🙌\n" +
                        "😊🙌😊"
        );

        emojiList.add(
                "🎤💬👂\n" +
                        "💬 TALK TO ME! 💬\n" +
                        "👂 I'M LISTENING 👂\n" +
                        "🎤💬👂"
        );

        emojiList.add(
                "💭🗨️💭\n" +
                        "🗨️ LET'S CHAT! 🗨️\n" +
                        "💭    💭\n" +
                        "🗨️💭🗨️"
        );

        emojiList.add(
                "      🌄      \n" +
                        "   🐦🌞🦋   \n" +
                        "  🌸🌻🌼🌺  \n" +
                        "╰┈┈┈┈☕️┈┈┈┈╯\n" +
                        "  🍞🥐🥞🥓  \n" +
                        "   GOOD MORNING   "
        );

        emojiList.add(
                "   🌙 ⁕ 🌙   \n" +
                        "✨ 🌠 💤 🌠 ✨\n" +
                        "  🌌 🛌 🌌  \n" +
                        "  ╰┈┈ GOOD NIGHT ┈┈╯  \n" +
                        "    🌃 🌉 🌃    "
        );

        emojiList.add(
                "    🌞    \n" +
                        "  🕑🌴🕑  \n" +
                        "GOOD AFTERNOON\n" +
                        "  🍽️🍹🍽️  \n" +
                        "    🌻    "
        );

        emojiList.add(
                "   🌇🌆🌃   \n" +
                        "  🌉✨🌉  \n" +
                        "  GOOD EVENING  \n" +
                        "  🍷🌙🍷  \n" +
                        "   🌠🌌🌠   "
        );
        emojiList.add(
                "  💖💖💖  \n" +
                        "💖👫💌💖\n" +
                        "💖 I Love You 💖\n" +
                        "💖💋🌹💖\n" +
                        "  💖💖💖  "
        );
        emojiList.add(
                "   🎀🎀🎀   \n" +
                        "🎁💝🍫🎁\n" +
                        "💌 HAPPY VALENTINE’S DAY💌\n" +
                        "🎁💘🌹🎁\n" +
                        "   🎀🎀🎀   "
        );

        emojiList.add(
                "    💔💔💔    \n" +
                        "  😢  MISS  😢  \n" +
                        "💔 I MISS YOU 💔\n" +
                        "  😢  💔  😢  \n" +
                        "    💔💔💔    "
        );

        emojiList.add(
                " 👩❤️👨 \n" +
                        "💍💞💍\n" +
                        "LOVE OF MY LIFE\n" +
                        "💍💘💍\n" +
                        " ⏳️🌹⏳️ "
        );

        emojiList.add(
                "╔♡═══════♡╗\n" +
                        "  💋💄💋💋💄💋 \n" +
                        "  💋💄💋 HAPPY KISS DAY  \n" +
                        "  💏💞💏  💋💄💋 \n" +
                        "╚♡═══════♡╝"
        );

        emojiList.add(
                "🌹🌹🌹🌹\n" +
                        "🌹💮🌺🌹\n" +
                        "🌹 HAPPY ROSE DAY 🌹\n" +
                        "🌹🏵️🌼🌹\n" +
                        "🌹🌹🌹🌹"
        );
        emojiList.add(
                "  💍🌟💍  \n" +
                        "🌹🥂🌹\n" +
                        "╰┈❤️ HAPPY ANNIVERSARY ❤️┈╯\n" +
                        "🌹🍾🌹\n" +
                        "  💍✨💍  "
        );
        emojiList.add(
                "   🎂✨🎂   \n" +
                        "🎁🕯️🎉🕯️🎁\n" +
                        "✨ HAPPY BIRTHDAY ✨\n" +
                        "🎈🍰🎈🍰🎈\n" +
                        "   🥳🎊🥳   "
        );
        emojiList.add(
                "  🎊🎆🎊  \n" +
                        "🎉 HAPPY 🎉\n" +
                        "🎆 NEW YEAR 🎆\n" +
                        "  🎊🎇🎊  "
        );
        emojiList.add(
                " 💔💔💔 \n" +
                        "💔🌧️💔\n" +
                        "╰┈😭┈╯\n" +
                        "🔨🧩🔨\n" +
                        "  BROKEN HEART  "
        );
        emojiList.add(
                " 🌌👩❤️👨🌌 \n" +
                        "💫💍💫\n" +
                        "ENDLESS LOVE\n" +
                        "💞🌠💞\n" +
                        " 🌟♾️🌟 "
        );
        emojiList.add(
                " ♾️🌵♾️ \n" +
                        "⚡️💀⚡️\n" +
                        "ENDLESS PAIN\n" +
                        "⛓️😭⛓️\n" +
                        " ♾️\uD83D\uDCA8♾️ "
        );
        emojiList.add(
                " 🌑🌀🌑 \n" +
                        "🌀🚶♀️🌀\n" +
                        "╰┈🌫️ LOST MYSELF 🌫️┈╯\n" +
                        "🌀🌀🌀\n" +
                        " 🌑❓🌑 "
        );
        emojiList.add(
                "    🔥    \n" +
                        "  ❤️🌹❤️  \n" +
                        "👩❤️👨 LOVE OF MY LIFE 👩❤️👨\n" +
                        "  💍⏳💍  \n" +
                        "    🌠    "
        );
        emojiList.add(
                " 🍫🍫🍫 \n" +
                        "🍩~🍫~🍩\n" +
                        "🎉 HAPPY CHOCOLATE DAY 🎉\n" +
                        "🍪~🍫~🍪\n" +
                        " 🍫🍫🍫 "
        );
        emojiList.add(
                "   ⚛️🌀⚛️   \n" +
                        "🌀👥🌀\n" +
                        "╰┈💞 I FEEL YOU 💞┈╯\n" +
                        "🌀🤝🌀\n" +
                        "   ⚛️✨⚛️   "
        );
        emojiList.add(
                "  🤗🤲🤗  \n" +
                        " ❤️ TAKE ❤️ \n" +
                        "🤲  CARE  🤲\n" +
                        "  🤗🤲🤗  "
        );


        emojiList.add(
                "🎀🎀🎀🎀🎀\n" +
                        "🎁 BEST 🎁\n" +
                        "🎀 WISHES 🎀\n" +
                        "🎁🎀🎁🎀🎁\n" +
                        " 🎀🎁🎀🎁"
        );

        emojiList.add(
                "  👨‍👦💙👨‍👦  \n" +
                        "💙 HAPPY 💙\n" +
                        "👨‍👦 FATHER'S DAY 👨‍👦\n" +
                        "  💙👨‍👦💙  "
        );
        emojiList.add(
                "  👩‍👧💖👩‍👧  \n" +
                        "💖 HAPPY 💖\n" +
                        "👩‍👧 MOTHER'S DAY 👩‍👧\n" +
                        "  💖👩‍👧💖  "
        );
        emojiList.add(
                "  👰💍🤵  \n" +
                        "💖 HAPPY 💖\n" +
                        "💍 WEDDING DAY 💍\n" +
                        "  👰💖🤵  "
        );
        emojiList.add(
                "  💍💖💍  \n" +
                        "💑 HAPPY 💑\n" +
                        "💍 ENGAGEMENT 💍\n" +
                        "  💖💍💖  "
        );
        emojiList.add(
                "  😃💖😃  \n" +
                        "💖 KEEP 💖\n" +
                        "😃 SMILING 😃\n" +
                        "  💖😃💖  "
        );
        emojiList.add(
                "  😊🌟😊  \n" +
                        "🌟 STAY 🌟\n" +
                        "😊 POSITIVE 😊\n" +
                        "  🌟😊🌟  "
        );
        emojiList.add(
                "  🎉🏝️🎉  \n" +
                        "🏝️  HAPPY  🏝️\n" +
                        "🎉  HOLIDAY  🎉\n" +
                        "  🏝️🎉🏝️  "
        );
        emojiList.add(
                "   🌞💐🌞   \n" +
                        "💐 HAVE A 💐\n" +
                        "🌞 NICE DAY 🌞\n" +
                        "   💐🌞💐   "
        );

        emojiList.add(
                "  🌙✨🌙  \n" +
                        "✨ SWEET ✨\n" +
                        "🌙 DREAMS 🌙\n" +
                        "  ✨🌙✨  "
        );

        emojiList.add(
                " 🌸🌼🌺 \n" +
                        "🌞😊🌞\n" +
                        "╰┈💮 STAY HAPPY 💮┈╯\n" +
                        "🦋🐝🌻\n" +
                        " 🌈🌷🌈 "
        );

        emojiList.add(
                "  🎉🎉🎉  \n" +
                        " 🏆 CONGRATS 🏆\n" +
                        "  🎊🏅🎊  \n" +
                        "  🎉🎉🎉  "
        );

        emojiList.add(
                "   🍀⁕🍀   \n" +
                        "🍀✨🌟✨🍀\n" +
                        "  ALL THE BEST  \n" +
                        "🍀💎💍💎🍀\n" +
                        "   🍀⁕🍀   "
        );

        emojiList.add(
                "   🎈   \n" +
                        "🌍🎈🌍\n" +
                        "✈️ HAPPY JOURNEY ✈️\n" +
                        "🌄🏞️🌅\n" +
                        "   🧳   "
        );

        emojiList.add(
                "   🌈   \n" +
                        "🍀💰🍀\n" +
                        "🏆 GOOD LUCK 🏆\n" +
                        "🎰🎲🎯\n" +
                        "   🍀   "
        );

        emojiList.add(
                "   🌙🌟🌙   \n" +
                        "✨🕌✨\n" +
                        "╰┈🕋 EID MUBARAK 🕋┈╯\n" +
                        "🌙🍬🌙\n" +
                        "   🥮🌠🥮   "
        );

        emojiList.add(
                "👑\n" +
                        "🏆💼🏆\n" +
                        "WISH YOU SUCCESS!\n" +
                        "📈🚀📈\n" +
                        "👑"
        );

        emojiList.add(
                "   🏆🎯🏆   \n" +
                        "🎯        🎯\n" +
                        "🏆 WISH YOU🏆\n" +
                        "🎯 SUCCESS 🎯\n" +
                        "   🏆🎯🏆   "
        );

        emojiList.add(
                "       🎄  \n" +
                        "      🎄🎅🎄  \n" +
                        "    🎄  🎁  🎄  \n" +
                        "  🎄   MERRY  🎄  \n" +
                        " 🎄🎅CHRISTMAS🎅🎄"
        );

        emojiList.add(
                "☀️🌈☀️\n" +
                        "☀️HAVE GREAT ☀️\n" +
                        "🌈 DAY! 🌈\n" +
                        "☀️☀️☀️"
        );

        emojiList.add(
                "☮️❤️☮️\n" +
                        "☮️ PEACE ☮️\n" +
                        "❤️ & LOVE ❤️\n" +
                        "☮️❤️☮️"
        );

        emojiList.add(
                "🌟🎉🌟\n" +
                        "🎉 ENJOY EVERY🎉\n" +
                        "🌟 MOMENT 🌟\n" +
                        "🎉🎉🎉"
        );

        emojiList.add(
                "🤗🤗🤗\n" +
                        "🤗SENDING WARM 🤗\n" +
                        "🤗 HUGS 🤗\n" +
                        "🤗🤗🤗"
        );

        emojiList.add(
                "🤝🤝🤝\n" +
                        "🤝HAPPY FRIEND 🤝\n" +
                        "🤝  SHIP  🤝\n" +
                        "🤝 DAY 🤝\n" +
                        "🤝🤝🤝"
        );

        emojiList.add(
                "💞💖💞\n" +
                        "💞 LOVE 💞\n" +
                        "💞FOREVER💞\n" +
                        "💞💖💞"
        );

        emojiList.add(
                "    🔥    \n" +
                        "  💖🌹💖  \n" +
                        "👩❤️👨LOVE FOREVER 👩❤️👨\n" +
                        "  💍⏳💍  \n" +
                        "    🌠    "
        );

        emojiList.add(
                "❤️ 🖤 ❤️\n" +
                        "❤️ HEART ❤️\n" +
                        "❤️  &  ❤️\n" +
                        "❤️ SOUL ❤️\n" +
                        "🖤 ❤️ 🖤"
        );

        emojiList.add(
                "💘💘💘\n" +
                        "💘 DEEPLY IN💘\n" +
                        "💘 LOVE 💘\n" +
                        "💘💘💘"
        );

        emojiList.add(
                "🌴🌺🌴\n" +
                        "🌴YOU ARE🌴\n" +
                        "🌺MY🌺\n" +
                        "🌴PARADISE🌴\n" +
                        "🌺🌴🌺"
        );

        emojiList.add(
                "🔥❤️🔥\n" +
                        "🔥 PASSIONATE 🔥\n" +
                        "❤️  SOUL  ❤️\n" +
                        "🔥❤️🔥"
        );

        emojiList.add(
                "⏳💫⏳\n" +
                        "💫  TIMELESS  💫\n" +
                        "⏳  BOND  ⏳\n" +
                        "💫⏳💫"
        );

        emojiList.add(
                "💞💑💞\n" +
                        "💞  MY  💞\n" +
                        "💑 OTHER 💑\n" +
                        "💞  HALF  💞\n" +
                        "💞💑💞"
        );

        emojiList.add(
                "✨🌟✨\n" +
                        "✨ YOU ARE  ✨\n" +
                        "🌟 MY LIGHT 🌟\n" +
                        "✨🌟✨"
        );

        emojiList.add(
                "💖🌹💖\n" +
                        "💖 MY 💖\n" +
                        "🌹 BELOVED 🌹\n" +
                        "💖💖💖"
        );

        emojiList.add(
                "💓💓💓\n" +
                        "💓 MY 💓\n" +
                        "💓 HEARTBEAT 💓\n" +
                        "💓💓💓"
        );

        emojiList.add(
                "🌧️💔🌧️\n" +
                        "💔 LONELY 💔\n" +
                        "🌧️ SOUL 🌧️\n" +
                        "💔🌧️💔"
        );

        emojiList.add(
                "❤️🌍❤️\n" +
                        "❤️ MY ❤️\n" +
                        "🌍 EVERYTHING 🌍\n" +
                        "❤️🌍❤️"
        );

        emojiList.add(
                "🌙⭐🌙\n" +
                        "⭐ ALWAYS ⭐\n" +
                        "🌙 WITH YOU 🌙\n" +
                        "⭐🌙⭐"
        );

        emojiList.add(
                "🌎💖🌎\n" +
                        "💖 YOU ARE 💖\n" +
                        "🌎 MY WORLD 🌎\n" +
                        "💖🌎💖"
        );

        emojiList.add(
                "💑💞💑\n" +
                        "💞 MY 💞\n" +
                        "💑 SOULMATE 💑\n" +
                        "💞💑💞"
        );

        emojiList.add(
                "🌿✨✨✨🌿\n" +
                        "✨ 🙏 STAY BLESSED 🙏 ✨\n" +
                        "🌿✨✨✨🌿"
        );

        emojiList.add(
                "🔆💡💖💡🔆\n" +
                        "💖 KEEP BELIEVING 💖\n" +
                        "🔆💡💖💡🔆"
        );

        emojiList.add(
                "✨⭐🌙⭐✨\n" +
                        "💙 BELIEVE IN YOU 💙\n" +
                        "✨⭐🌙⭐✨"
        );

        emojiList.add(
                "🔥🔥💪🔥🔥\n" +
                        "💖 STAY STRONG 💖\n" +
                        "🔥🔥💪🔥🔥"
        );

        emojiList.add(
                "🚪🚶‍♂️💭🚶‍♀️🚪\n" +
                        "👋 GOODBYE! 👋\n" +
                        "🚪🚶‍♂️💭🚶‍♀️🚪"
        );

        emojiList.add(
                "🎶🎧🛋️🎧🎶\n" +
                        "😎 JUST CHILLING 😎\n" +
                        "🎶🎧🛋️🎧🎶"
        );

        emojiList.add(
                "🤝💫💞💫🤝\n" +
                        "💖 NICE TO MEET YOU 💖\n" +
                        "🤝💫💞💫🤝"
        );



    }

    public List<String> getEmojiList() {
        return emojiList;
    }

}