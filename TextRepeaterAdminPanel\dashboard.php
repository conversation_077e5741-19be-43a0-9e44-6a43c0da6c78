<?php
$page_title = 'Dashboard';
require_once 'includes/header.php';

// Get dashboard statistics
$database = new Database();
$conn = $database->getConnection();

// Get total app users
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM app_users");
$stmt->execute();
$total_users = $stmt->fetch()['total'];

// Get active users (last 7 days)
$stmt = $conn->prepare("SELECT COUNT(*) as active FROM app_users WHERE last_seen >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
$stmt->execute();
$active_users = $stmt->fetch()['active'];

// Get total notifications sent
$stmt = $conn->prepare("SELECT COUNT(*) as total FROM notifications WHERE status = 'sent'");
$stmt->execute();
$total_notifications = $stmt->fetch()['total'];

// Get today's ad impressions
$stmt = $conn->prepare("SELECT COUNT(*) as impressions FROM ad_analytics WHERE event_type = 'impression' AND DATE(event_timestamp) = CURDATE()");
$stmt->execute();
$today_impressions = $stmt->fetch()['impressions'];

// Get today's ad clicks
$stmt = $conn->prepare("SELECT COUNT(*) as clicks FROM ad_analytics WHERE event_type = 'click' AND DATE(event_timestamp) = CURDATE()");
$stmt->execute();
$today_clicks = $stmt->fetch()['clicks'];

// Calculate CTR
$ctr = $today_impressions > 0 ? ($today_clicks / $today_impressions) * 100 : 0;

// Get recent notifications
$stmt = $conn->prepare("SELECT * FROM notifications ORDER BY created_at DESC LIMIT 5");
$stmt->execute();
$recent_notifications = $stmt->fetchAll();

// Get ad performance data for chart (last 7 days)
$stmt = $conn->prepare("
    SELECT 
        DATE(event_timestamp) as date,
        SUM(CASE WHEN event_type = 'impression' THEN 1 ELSE 0 END) as impressions,
        SUM(CASE WHEN event_type = 'click' THEN 1 ELSE 0 END) as clicks
    FROM ad_analytics 
    WHERE event_timestamp >= DATE_SUB(NOW(), INTERVAL 7 DAY)
    GROUP BY DATE(event_timestamp)
    ORDER BY date ASC
");
$stmt->execute();
$ad_performance = $stmt->fetchAll();
?>

<!-- Dashboard Content -->
<div class="row">
    <!-- Statistics Cards -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total App Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($total_users); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Active Users (7 days)
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($active_users); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Today's Ad Impressions
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($today_impressions); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-eye fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Today's CTR
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($ctr, 2); ?>%
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-mouse-pointer fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Ad Performance Chart -->
    <div class="col-xl-8 col-lg-7">
        <div class="card shadow mb-4">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Ad Performance (Last 7 Days)</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="adPerformanceChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="col-xl-4 col-lg-5">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="notifications.php" class="btn btn-primary">
                        <i class="fas fa-bell me-2"></i>Send Notification
                    </a>
                    <a href="admob-config.php" class="btn btn-outline-primary">
                        <i class="fas fa-ad me-2"></i>Configure AdMob
                    </a>
                    <a href="app-users.php" class="btn btn-outline-primary">
                        <i class="fas fa-users me-2"></i>View Users
                    </a>
                    <a href="ad-analytics.php" class="btn btn-outline-primary">
                        <i class="fas fa-chart-line me-2"></i>View Analytics
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Notifications -->
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Notifications</h6>
            </div>
            <div class="card-body">
                <?php if (empty($recent_notifications)): ?>
                    <p class="text-muted">No notifications sent yet.</p>
                <?php else: ?>
                    <?php foreach ($recent_notifications as $notification): ?>
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-bell text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="fw-bold"><?php echo htmlspecialchars($notification['title']); ?></div>
                                <div class="text-muted small">
                                    <?php echo date('M j, Y g:i A', strtotime($notification['created_at'])); ?>
                                </div>
                            </div>
                            <div class="flex-shrink-0">
                                <span class="badge bg-<?php echo $notification['status'] == 'sent' ? 'success' : 'warning'; ?>">
                                    <?php echo ucfirst($notification['status']); ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                    <div class="text-center">
                        <a href="notification-history.php" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row">
    <div class="col-12">
        <div class="card shadow mb-4">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">System Status</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-server fa-2x text-success mb-2"></i>
                            <div class="fw-bold">Database</div>
                            <div class="text-success">Online</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-cloud fa-2x text-success mb-2"></i>
                            <div class="fw-bold">FCM Service</div>
                            <div class="text-success">Connected</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-ad fa-2x text-success mb-2"></i>
                            <div class="fw-bold">AdMob API</div>
                            <div class="text-success">Active</div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <i class="fas fa-shield-alt fa-2x text-success mb-2"></i>
                            <div class="fw-bold">Security</div>
                            <div class="text-success">Secure</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>

<?php
$page_scripts = "
<script>
// Ad Performance Chart
const ctx = document.getElementById('adPerformanceChart').getContext('2d');
const adPerformanceChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: [" . implode(',', array_map(function($item) { return "'" . date('M j', strtotime($item['date'])) . "'"; }, $ad_performance)) . "],
        datasets: [{
            label: 'Impressions',
            data: [" . implode(',', array_column($ad_performance, 'impressions')) . "],
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.1)',
            tension: 0.1
        }, {
            label: 'Clicks',
            data: [" . implode(',', array_column($ad_performance, 'clicks')) . "],
            borderColor: 'rgb(255, 99, 132)',
            backgroundColor: 'rgba(255, 99, 132, 0.1)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        },
        plugins: {
            legend: {
                position: 'top',
            },
            title: {
                display: false
            }
        }
    }
});
</script>
";

require_once 'includes/footer.php';
?>
