package com.anginatech.textrepeater;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.AppCompatButton;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.viewpager.widget.ViewPager;


public class Navigation_Activity extends AppCompatActivity {


    ViewPager slideViewPager;
    LinearLayout dot_layout;
    ViewPagerAdapter viewPagerAdapter;
    AppCompatButton skipButton,nextButton;
    private ImageView[] dot;
    private int numOfPages = 3;


    ViewPager.OnPageChangeListener viewPagerListener = new ViewPager.OnPageChangeListener() {
        @Override
        public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {

        }

        @Override
        public void onPageSelected(int position) {

            addDots(position);
            if (position>0){

            }else {
            }
            if (position==2) {
                nextButton.setText("GET STARTED");
            }else {
                nextButton.setText("NEXT");
            }
        }

        @Override
        public void onPageScrollStateChanged(int state) {

        }
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_navigation);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        SharedPreferences sharedPreferences = getSharedPreferences("nightModePrefs", MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }



        nextButton = findViewById(R.id.nextButton);
        skipButton = findViewById(R.id.skipButton);




        nextButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (getItem(0)<2){
                    slideViewPager.setCurrentItem(getItem(1),true);
                }else {
                    SharedPreferences sharedPreferences = getSharedPreferences("MyAppPrefs", MODE_PRIVATE);
                    SharedPreferences.Editor editor = sharedPreferences.edit();
                    editor.putBoolean("isFirstTime", false);
                    editor.apply();
                    startActivity(new Intent(Navigation_Activity.this, MainActivity.class));
                    finish();
                }

            }
        });

        skipButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SharedPreferences sharedPreferences = getSharedPreferences("MyAppPrefs", MODE_PRIVATE);
                SharedPreferences.Editor editor = sharedPreferences.edit();
                editor.putBoolean("isFirstTime", false);
                editor.apply();
                startActivity(new Intent(Navigation_Activity.this, MainActivity.class));
                finish();

            }
        });
        slideViewPager = findViewById(R.id.sliderViewPager);
        dot_layout = findViewById(R.id.dot_layout);

        viewPagerAdapter = new ViewPagerAdapter(this);
        slideViewPager.setAdapter(viewPagerAdapter);
        addDots(0);
        slideViewPager.addOnPageChangeListener(viewPagerListener);

    }


    private int getItem (int i) {
        return slideViewPager.getCurrentItem()+i;
    }

    private void addDots(int currentPage) {
        dot_layout.removeAllViews();

        dot = new ImageView[numOfPages];

        for (int i = 0; i < numOfPages; i++) {
            dot[i] = new ImageView(this);

            if (i == currentPage) {
                dot[i].setImageDrawable(ContextCompat.getDrawable(Navigation_Activity.this, R.drawable.dot_active));

            } else {
                dot[i].setImageDrawable(ContextCompat.getDrawable(Navigation_Activity.this, R.drawable.dot_inactive));
            }

            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT,
                    LinearLayout.LayoutParams.WRAP_CONTENT);
            params.setMargins(8, 0, 8, 0);

            dot_layout.addView(dot[i], params);
        }

    }
}