package com.anginatech.textrepeater;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RelativeLayout;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.viewpager2.widget.ViewPager2;

import com.android.volley.Request;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;
import com.anginatech.textrepeater.models.Category;
import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

public class Message_Activity extends AppCompatActivity {

    private TabLayout tabLayout;
    private ViewPager2 viewPager;
    private ViewPagerAdapter2 legacyAdapter;
    private DynamicViewPagerAdapter dynamicAdapter;
    private DynamicDatabaseHelper dynamicDatabaseHelper;
    private boolean useDynamicMode = true; // Set to true to use dynamic categories

    MaterialToolbar messages_materialToolbar;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_message);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        SharedPreferences sharedPreferences = getSharedPreferences("nightModePrefs", MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }


        messages_materialToolbar = findViewById(R.id.messages_materialToolbar);
        tabLayout = findViewById(R.id.tabLayout);
        viewPager = findViewById(R.id.viewPager);



        messages_materialToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(Message_Activity.this, MainActivity.class));
                finish();
            }
        });

        messages_materialToolbar.setTitleTextAppearance(Message_Activity.this,R.style.RobotoBoldTextAppearance);

        // Initialize database helper
        MyApplication app = (MyApplication) getApplication();
        dynamicDatabaseHelper = app.getDynamicDatabaseHelper();

        // Configure TabLayout for scrollable tabs
        configureTabLayout();

        // Setup ViewPager with dynamic or legacy adapter
        setupViewPager();
    }

    private void configureTabLayout() {
        // Ensure TabLayout is properly configured for scrollable mode
        tabLayout.setTabMode(TabLayout.MODE_SCROLLABLE);
        tabLayout.setTabGravity(TabLayout.GRAVITY_START);

        // Set minimum and maximum tab widths for better display
        // This will be applied when tabs are created
    }

    private void setupViewPager() {
        if (useDynamicMode) {
            setupDynamicViewPager();
        } else {
            setupLegacyViewPager();
        }
    }

    private void setupDynamicViewPager() {
        // Get categories from database
        List<Category> categories = dynamicDatabaseHelper.getAllCategories();

        if (categories.isEmpty()) {
            // Fallback to legacy mode if no dynamic categories found
            setupLegacyViewPager();
            return;
        }

        // Create dynamic adapter
        dynamicAdapter = new DynamicViewPagerAdapter(this, false);
        dynamicAdapter.updateCategories(categories);
        viewPager.setAdapter(dynamicAdapter);

        // Setup tabs with dynamic categories
        TabLayoutMediator mediator = new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            Category category = dynamicAdapter.getCategoryAt(position);
            if (category != null) {
                tab.setText(category.getDisplayNameWithEmoji());
            }
        });
        mediator.attach();

        // Post-configuration to ensure proper tab display
        tabLayout.post(() -> {
            for (int i = 0; i < tabLayout.getTabCount(); i++) {
                TabLayout.Tab tab = tabLayout.getTabAt(i);
                if (tab != null && tab.view != null) {
                    // Allow tabs to expand to show full text
                    tab.view.setMinimumWidth(0);

                    // Find TextView within the tab view and configure it
                    android.widget.TextView textView = findTextViewInTab(tab.view);
                    if (textView != null) {
                        textView.setEllipsize(null);
                        textView.setSingleLine(false);
                        textView.setMaxLines(2);
                        textView.setGravity(android.view.Gravity.CENTER);
                    }
                }
            }
        });
    }

    private void setupLegacyViewPager() {
        legacyAdapter = new ViewPagerAdapter2(this);
        viewPager.setAdapter(legacyAdapter);

        new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            if (position == 0) {
                tab.setText("Romantic \uD83E\uDD70");
            } else if (position == 1) {
                tab.setText("Sad \uD83E\uDD72");
            } else if (position == 2) {
                tab.setText("Funny \uD83D\uDE04");
            }
        }).attach();
    }

    /**
     * Refresh the ViewPager with updated categories
     */
    public void refreshCategories() {
        if (useDynamicMode && dynamicAdapter != null) {
            List<Category> categories = dynamicDatabaseHelper.getAllCategories();
            dynamicAdapter.updateCategories(categories);
        }
    }

    /**
     * Switch between dynamic and legacy mode
     */
    public void setUseDynamicMode(boolean useDynamicMode) {
        this.useDynamicMode = useDynamicMode;
        setupViewPager();
    }

    /**
     * Helper method to find TextView within a tab view
     */
    private android.widget.TextView findTextViewInTab(android.view.View tabView) {
        if (tabView instanceof android.widget.TextView) {
            return (android.widget.TextView) tabView;
        }
        if (tabView instanceof android.view.ViewGroup) {
            android.view.ViewGroup viewGroup = (android.view.ViewGroup) tabView;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                android.view.View child = viewGroup.getChildAt(i);
                if (child instanceof android.widget.TextView) {
                    return (android.widget.TextView) child;
                }
                // Recursively search in nested ViewGroups
                if (child instanceof android.view.ViewGroup) {
                    android.widget.TextView textView = findTextViewInTab(child);
                    if (textView != null) {
                        return textView;
                    }
                }
            }
        }
        return null;
    }

    @Override
    public void onBackPressed() {
        startActivity(new Intent(Message_Activity.this, MainActivity.class));
        finish();
        super.onBackPressed();
    }





}
