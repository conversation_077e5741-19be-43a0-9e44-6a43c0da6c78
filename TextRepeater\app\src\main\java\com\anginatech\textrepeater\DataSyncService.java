package com.anginatech.textrepeater;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.anginatech.textrepeater.database.repository.RoomDataRepository;
import com.anginatech.textrepeater.models.ApiResponse;
import com.anginatech.textrepeater.models.SyncResponse;
import com.anginatech.textrepeater.network.ApiService;
import com.anginatech.textrepeater.network.ModernApiClient;
import com.anginatech.textrepeater.network.NetworkUtils;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Data Synchronization Service - MODERN ROOM DATABASE VERSION
 * Handles automatic background synchronization of categories and text content
 * Uses Room database for modern, efficient data storage and complete data replacement
 */
public class DataSyncService {
    private static final String TAG = "DataSyncService";

    private final Context context;
    private final RoomDataRepository roomRepository;
    private final ApiService apiService;
    private final SharedPreferences syncPrefs;
    
    // Sync state tracking
    private boolean isSyncing = false;
    private SyncCallback currentCallback;
    
    /**
     * Callback interface for sync operations
     */
    public interface SyncCallback {
        void onSyncStarted();
        void onSyncProgress(String message, int progress);
        void onSyncSuccess(SyncResponse data);
        void onSyncError(String error);
        void onSyncComplete();
    }
    
    public DataSyncService(Context context) {
        this.context = context;
        this.roomRepository = RoomDataRepository.getInstance(context);
        this.apiService = ModernApiClient.getInstance(context).getApiService();
        this.syncPrefs = context.getSharedPreferences("app_config", Context.MODE_PRIVATE);

        Log.d(TAG, "DataSyncService initialized with Room database");
    }
    
    /**
     * Check if sync is needed based on network availability and last sync time
     */
    public boolean isSyncNeeded() {
        if (!NetworkUtils.isNetworkAvailable(context)) {
            Log.d(TAG, "Network not available, sync not needed");
            return false;
        }
        
        if (!isSyncEnabled()) {
            Log.d(TAG, "Sync disabled, sync not needed");
            return false;
        }
        
        long lastSyncTime = getLastSyncTime();
        long currentTime = System.currentTimeMillis();
        long timeSinceLastSync = currentTime - lastSyncTime;
        
        boolean syncNeeded = timeSinceLastSync > (24 * 60 * 60 * 1000); // 24 hours
        Log.d(TAG, "Sync needed: " + syncNeeded + " (time since last sync: " + timeSinceLastSync + "ms)");
        
        return syncNeeded;
    }
    
    /**
     * Perform automatic sync if needed
     */
    public void performAutoSync(SyncCallback callback) {
        if (isSyncing) {
            Log.w(TAG, "Sync already in progress");
            if (callback != null) {
                callback.onSyncError("Sync already in progress");
            }
            return;
        }
        
        if (!isSyncNeeded()) {
            Log.d(TAG, "Auto sync not needed");
            if (callback != null) {
                callback.onSyncComplete();
            }
            return;
        }
        
        performSync(callback);
    }
    
    /**
     * Force sync regardless of cache status
     */
    public void performForceSync(SyncCallback callback) {
        if (isSyncing) {
            Log.w(TAG, "Sync already in progress");
            if (callback != null) {
                callback.onSyncError("Sync already in progress");
            }
            return;
        }
        
        if (!NetworkUtils.isNetworkAvailable(context)) {
            Log.w(TAG, "Network not available for force sync");
            if (callback != null) {
                callback.onSyncError("Network not available");
            }
            return;
        }
        
        performSync(callback);
    }
    
    /**
     * Perform the actual sync operation
     */
    private void performSync(SyncCallback callback) {
        isSyncing = true;
        currentCallback = callback;
        
        Log.d(TAG, "Starting data synchronization...");
        
        if (callback != null) {
            callback.onSyncStarted();
            callback.onSyncProgress("Connecting to server...", 10);
        }
        
        // Make API call to get sync data
        Call<ApiResponse<SyncResponse>> call = apiService.getSyncData("sync/data");
        
        call.enqueue(new Callback<ApiResponse<SyncResponse>>() {
            @Override
            public void onResponse(Call<ApiResponse<SyncResponse>> call, Response<ApiResponse<SyncResponse>> response) {
                if (response.isSuccessful() && response.body() != null) {
                    ApiResponse<SyncResponse> apiResponse = response.body();
                    
                    if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                        Log.d(TAG, "Sync data received successfully");
                        if (currentCallback != null) {
                            currentCallback.onSyncProgress("Processing data...", 50);
                        }
                        
                        // Process the sync data
                        processSyncData(apiResponse.getData());
                    } else {
                        String error = apiResponse.getMessage() != null ? apiResponse.getMessage() : "Unknown API error";
                        Log.e(TAG, "API error: " + error);
                        handleSyncError("Server error: " + error);
                    }
                } else {
                    String error = "HTTP " + response.code() + ": " + response.message();
                    Log.e(TAG, "HTTP error: " + error);
                    handleSyncError("Network error: " + error);
                }
            }
            
            @Override
            public void onFailure(Call<ApiResponse<SyncResponse>> call, Throwable t) {
                Log.e(TAG, "Sync request failed: " + t.getMessage(), t);
                handleSyncError("Connection failed: " + t.getMessage());
            }
        });
    }
    
    /**
     * Process the received sync data - COMPLETE DATA REPLACEMENT using Room Database
     * Ensures ONLY fresh data is visible after sync completion
     */
    private void processSyncData(SyncResponse syncData) {
        try {
            Log.d(TAG, "🔄 Processing FRESH sync data with Room database: " + syncData.toString());

            // Use Room repository for complete data replacement
            roomRepository.replaceAllDataWithFreshSync(syncData, new RoomDataRepository.DataReplacementCallback() {
                @Override
                public void onProgress(String message, int progress) {
                    Log.d(TAG, "Room sync progress: " + message + " (" + progress + "%)");
                    if (currentCallback != null) {
                        currentCallback.onSyncProgress(message, progress);
                    }
                }

                @Override
                public void onSuccess(int categoryCount, int messageCount) {
                    Log.d(TAG, "✅ Room database sync completed successfully - Categories: " + categoryCount + ", Messages: " + messageCount);

                    // Update sync timestamp
                    updateLastSyncTime();

                    if (currentCallback != null) {
                        currentCallback.onSyncProgress("Fresh data sync completed", 100);
                        currentCallback.onSyncSuccess(syncData);
                    }

                    completeSyncOperation();
                }

                @Override
                public void onError(String error) {
                    Log.e(TAG, "❌ Room database sync failed: " + error);
                    handleSyncError("Failed to save fresh data: " + error);
                }
            }).subscribe(
                () -> {
                    // Completable completed successfully - handled in onSuccess callback
                },
                throwable -> {
                    Log.e(TAG, "❌ Room sync operation failed: " + throwable.getMessage(), throwable);
                    handleSyncError("Room sync operation failed: " + throwable.getMessage());
                }
            );

        } catch (Exception e) {
            Log.e(TAG, "❌ Error processing sync data: " + e.getMessage(), e);
            handleSyncError("Failed to process sync data: " + e.getMessage());
        }
    }
    
    // Note: Data clearing, saving, and verification are now handled by RoomDataRepository
    // The Room repository provides atomic transactions and better error handling
    
    /**
     * Handle sync errors
     */
    private void handleSyncError(String error) {
        Log.e(TAG, "Sync error: " + error);
        
        if (currentCallback != null) {
            currentCallback.onSyncError(error);
        }
        
        completeSyncOperation();
    }
    
    /**
     * Complete sync operation and cleanup
     */
    private void completeSyncOperation() {
        isSyncing = false;
        
        if (currentCallback != null) {
            currentCallback.onSyncComplete();
            currentCallback = null;
        }
    }
    
    // Utility methods
    public boolean isSyncEnabled() {
        return syncPrefs.getBoolean("sync_enabled", true);
    }

    public void setSyncEnabled(boolean enabled) {
        syncPrefs.edit().putBoolean("sync_enabled", enabled).apply();
    }

    public long getLastSyncTime() {
        return syncPrefs.getLong("last_sync_time", 0);
    }

    private void updateLastSyncTime() {
        syncPrefs.edit().putLong("last_sync_time", System.currentTimeMillis()).apply();
    }
    
    public boolean isSyncing() {
        return isSyncing;
    }
}
