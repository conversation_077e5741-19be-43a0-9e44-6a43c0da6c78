-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Jun 04, 2025 at 08:10 PM
-- Server version: 10.4.27-MariaDB
-- PHP Version: 8.2.0

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `text_repeater_admin`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_login_history`
--

CREATE TABLE `admin_login_history` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `login_time` timestamp NOT NULL DEFAULT current_timestamp(),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `login_successful` tinyint(1) DEFAULT 1,
  `logout_time` timestamp NULL DEFAULT NULL,
  `session_duration` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_login_history`
--

INSERT INTO `admin_login_history` (`id`, `user_id`, `login_time`, `ip_address`, `user_agent`, `login_successful`, `logout_time`, `session_duration`) VALUES
(1, 1, '2025-05-27 07:02:01', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', 1, NULL, 0),
(2, 1, '2025-05-26 23:02:06', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 1, NULL, 0),
(3, 1, '2025-05-26 01:02:06', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0', 1, NULL, 0),
(4, 1, '2025-05-25 01:02:06', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 1, NULL, 0),
(5, 1, '2025-05-24 01:02:06', '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 1, NULL, 0),
(6, 1, '2025-05-27 08:04:51', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', 1, NULL, 0),
(7, 1, '2025-05-27 08:07:21', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.0.0', 1, NULL, 0),
(8, 1, '2025-05-27 08:49:48', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', 1, NULL, 0),
(9, 1, '2025-05-27 10:06:08', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', 1, NULL, 0),
(10, 1, '2025-05-27 10:20:02', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', 1, NULL, 0),
(11, 1, '2025-05-27 11:49:08', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:138.0) Gecko/20100101 Firefox/138.0', 1, NULL, 0);

-- --------------------------------------------------------

--
-- Table structure for table `admin_sessions`
--

CREATE TABLE `admin_sessions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `session_id` varchar(128) NOT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_activity` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `expires_at` timestamp NULL DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password` varchar(255) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `role` enum('super_admin','admin','moderator') DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT 1,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `status` enum('active','inactive','suspended') DEFAULT 'active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `email`, `password`, `full_name`, `role`, `is_active`, `last_login`, `created_at`, `updated_at`, `status`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$MMds5w7Cycxyx/mO./OEFutRtxbabI3grivWfiOefsXHEWB3W3mkC', 'System Administrator', 'super_admin', 1, '2025-05-27 11:49:08', '2025-05-27 05:39:29', '2025-05-27 11:49:08', 'active');

-- --------------------------------------------------------

--
-- Table structure for table `admob_config`
--

CREATE TABLE `admob_config` (
  `id` int(11) NOT NULL,
  `config_name` varchar(100) NOT NULL,
  `app_id` varchar(255) NOT NULL,
  `banner_ad_unit_id` varchar(255) DEFAULT NULL,
  `interstitial_ad_unit_id` varchar(255) DEFAULT NULL,
  `app_open_ad_unit_id` varchar(255) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `test_mode` tinyint(1) DEFAULT 0,
  `ad_frequency_minutes` int(11) DEFAULT 5,
  `max_ads_per_session` int(11) DEFAULT 10,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `admob_config`
--

INSERT INTO `admob_config` (`id`, `config_name`, `app_id`, `banner_ad_unit_id`, `interstitial_ad_unit_id`, `app_open_ad_unit_id`, `is_active`, `test_mode`, `ad_frequency_minutes`, `max_ads_per_session`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'Production Config', 'ca-app-pub-8559393431170327~6652981751', '', '', '', 0, 0, 5, 10, 1, '2025-05-27 05:39:29', '2025-05-27 07:56:49'),
(2, 'Admob', 'ca-app-pub-8559393431170327~6652981751', 'ca-app-pub-3940256099942544/9214589741', 'ca-app-pub-3940256099942544/1033173712', 'ca-app-pub-3940256099942544/9257395921', 1, 1, 5, 10, 1, '2025-05-27 07:56:40', '2025-05-27 07:56:56');

-- --------------------------------------------------------

--
-- Table structure for table `ad_analytics`
--

CREATE TABLE `ad_analytics` (
  `id` int(11) NOT NULL,
  `device_id` varchar(255) DEFAULT NULL,
  `ad_type` enum('banner','interstitial','app_open') NOT NULL,
  `ad_unit_id` varchar(255) DEFAULT NULL,
  `event_type` enum('impression','click','load','fail') NOT NULL,
  `revenue` decimal(10,6) DEFAULT 0.000000,
  `currency` varchar(3) DEFAULT 'USD',
  `event_timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `session_id` varchar(100) DEFAULT NULL,
  `app_version` varchar(20) DEFAULT NULL,
  `network` varchar(50) DEFAULT 'admob',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `placement` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `api_keys`
--

CREATE TABLE `api_keys` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `api_key` varchar(64) NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `rate_limit` int(11) DEFAULT 1000,
  `last_used` timestamp NULL DEFAULT NULL,
  `total_requests` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `api_rate_limits`
--

CREATE TABLE `api_rate_limits` (
  `id` int(11) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `hour_window` varchar(13) NOT NULL,
  `request_count` int(11) DEFAULT 1,
  `last_request` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `api_rate_limits`
--

INSERT INTO `api_rate_limits` (`id`, `ip_address`, `hour_window`, `request_count`, `last_request`) VALUES
(1, '*************', '2025-05-27 07', 2, '2025-05-27 07:36:43'),
(2, '*************', '2025-05-27 07', 2, '2025-05-27 07:46:32'),
(3, '*************', '2025-05-27 08', 27, '2025-05-27 08:54:09'),
(4, '*************', '2025-05-27 09', 12, '2025-05-27 09:22:37'),
(5, '*************', '2025-05-27 09', 1, '2025-05-27 09:11:00'),
(6, '*************', '2025-05-27 15', 1, '2025-05-27 09:39:46'),
(7, '*************', '2025-05-27 16', 13, '2025-05-27 10:59:47'),
(8, '*************', '2025-05-27 17', 24, '2025-05-27 11:59:11'),
(9, '*************', '2025-05-27 18', 13, '2025-05-27 12:26:59');

-- --------------------------------------------------------

--
-- Table structure for table `app_settings`
--

CREATE TABLE `app_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('string','integer','boolean','json') DEFAULT 'string',
  `description` text DEFAULT NULL,
  `is_public` tinyint(1) DEFAULT 0,
  `updated_by` int(11) DEFAULT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `app_settings`
--

INSERT INTO `app_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `description`, `is_public`, `updated_by`, `updated_at`) VALUES
(1, 'app_version', '3.9.1', 'string', 'Current app version', 1, NULL, '2025-05-27 05:39:29'),
(2, 'maintenance_mode', 'false', 'boolean', 'Enable maintenance mode', 1, NULL, '2025-05-27 05:39:29'),
(3, 'force_update', 'false', 'boolean', 'Force app update', 1, NULL, '2025-05-27 05:39:29'),
(4, 'min_supported_version', '3.0.0', 'string', 'Minimum supported app version', 1, NULL, '2025-05-27 05:39:29'),
(5, 'ad_refresh_interval', '30', 'integer', 'Ad refresh interval in seconds', 1, NULL, '2025-05-27 05:39:29'),
(6, 'notification_enabled', 'true', 'boolean', 'Enable push notifications', 1, NULL, '2025-05-27 05:39:29'),
(7, 'max_daily_ads', '50', 'integer', 'Maximum ads per day per user', 1, NULL, '2025-05-27 06:20:38'),
(8, 'app_name', 'Text Repeater', 'string', 'Application name', 1, NULL, '2025-05-27 06:20:38'),
(9, 'support_email', '<EMAIL>', 'string', 'Support email address', 1, NULL, '2025-05-27 06:20:38');

-- --------------------------------------------------------

--
-- Table structure for table `app_users`
--

CREATE TABLE `app_users` (
  `id` int(11) NOT NULL,
  `device_id` varchar(255) NOT NULL,
  `fcm_token` varchar(255) DEFAULT NULL,
  `app_version` varchar(20) DEFAULT NULL,
  `android_version` varchar(20) DEFAULT NULL,
  `device_model` varchar(100) DEFAULT NULL,
  `first_seen` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_seen` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `total_sessions` int(11) DEFAULT 1,
  `is_active` tinyint(1) DEFAULT 1,
  `screen_resolution` varchar(20) DEFAULT NULL,
  `language` varchar(10) DEFAULT 'en',
  `timezone` varchar(50) DEFAULT 'UTC',
  `notifications_enabled` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `device_brand` varchar(100) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `app_users`
--

INSERT INTO `app_users` (`id`, `device_id`, `fcm_token`, `app_version`, `android_version`, `device_model`, `first_seen`, `last_seen`, `total_sessions`, `is_active`, `screen_resolution`, `language`, `timezone`, `notifications_enabled`, `created_at`, `updated_at`, `device_brand`) VALUES
(1, 'simple_test_1748334007', 'test_token', '1.0.0', NULL, NULL, '2025-05-27 08:20:07', '2025-05-27 08:20:07', 1, 1, NULL, 'en', 'UTC', 1, '2025-05-27 08:20:07', '2025-05-27 08:20:07', NULL),
(2, 'simple_reg_test_1748334305', 'simple_test_token', '1.0.0', '13', 'Test Device', '2025-05-27 08:25:05', '2025-05-27 08:25:05', 1, 1, '', 'en', 'UTC', 1, '2025-05-27 08:25:05', '2025-05-27 08:25:05', ''),
(3, 'test_device_1748334372', 'test_fcm_token_123', '1.0.0', '13', 'Test Device Model', '2025-05-27 08:26:12', '2025-05-27 08:26:12', 1, 1, '1080x1920', 'en', 'UTC', 1, '2025-05-27 08:26:12', '2025-05-27 08:26:12', 'Test Brand'),
(4, 'minimal_test_1748334372', '', '', '', '', '2025-05-27 08:26:12', '2025-05-27 08:26:12', 1, 1, '', 'en', 'UTC', 1, '2025-05-27 08:26:12', '2025-05-27 08:26:12', ''),
(5, '741616b100b4f521', 'dKGpsaFvR9q5cnVdYLflR5:APA91bEqlFtuvne-jP-0jm6JCiA77Z2eDqJOSLEdsirDtZZdb2WB4TDMPhpgVyLUH107M5ocM2lPur5bDsHAHm02i2q92bLj8Xyn1Allrbsh_p-rikQAvK8', '', '', '', '2025-05-27 08:26:39', '2025-05-27 12:26:59', 7, 1, '', 'en', 'UTC', 1, '2025-05-27 08:26:39', '2025-05-27 12:26:59', '');

-- --------------------------------------------------------

--
-- Table structure for table `app_versions`
--

CREATE TABLE `app_versions` (
  `id` int(11) NOT NULL,
  `platform` enum('android','ios') NOT NULL,
  `latest_version` varchar(20) NOT NULL,
  `min_supported_version` varchar(20) NOT NULL,
  `force_update` tinyint(1) DEFAULT 0,
  `update_message` text DEFAULT NULL,
  `download_url` varchar(500) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `app_versions`
--

INSERT INTO `app_versions` (`id`, `platform`, `latest_version`, `min_supported_version`, `force_update`, `update_message`, `download_url`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'android', '3.9.1', '3.0.0', 0, 'Update to the latest version for better performance and new features!', 'https://play.google.com/store/apps/details?id=com.monirulvi.textrepeater', 1, '2025-05-27 06:20:38', '2025-05-27 06:20:38');

-- --------------------------------------------------------

--
-- Table structure for table `feature_flags`
--

CREATE TABLE `feature_flags` (
  `id` int(11) NOT NULL,
  `feature_key` varchar(100) NOT NULL,
  `is_enabled` tinyint(1) DEFAULT 0,
  `rollout_percentage` int(11) DEFAULT 0,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `feature_flags`
--

INSERT INTO `feature_flags` (`id`, `feature_key`, `is_enabled`, `rollout_percentage`, `description`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'admob_enabled', 1, 100, 'Enable AdMob advertisements', 1, '2025-05-27 06:20:38', '2025-05-27 06:20:38'),
(2, 'push_notifications', 1, 100, 'Enable push notifications', 1, '2025-05-27 06:20:38', '2025-05-27 06:20:38'),
(3, 'analytics_tracking', 1, 100, 'Enable analytics tracking', 1, '2025-05-27 06:20:38', '2025-05-27 06:20:38'),
(4, 'crash_reporting', 1, 100, 'Enable crash reporting', 1, '2025-05-27 06:20:38', '2025-05-27 06:20:38'),
(5, 'beta_features', 0, 0, 'Enable beta features', 1, '2025-05-27 06:20:38', '2025-05-27 06:20:38');

-- --------------------------------------------------------

--
-- Table structure for table `notifications`
--

CREATE TABLE `notifications` (
  `id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `image_url` varchar(500) DEFAULT NULL,
  `action_url` varchar(500) DEFAULT NULL,
  `target_type` enum('all','active_users','specific_users','device','version') DEFAULT 'all',
  `target_users` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`target_users`)),
  `status` enum('draft','scheduled','sent','failed') DEFAULT 'draft',
  `scheduled_at` timestamp NULL DEFAULT NULL,
  `sent_at` timestamp NULL DEFAULT NULL,
  `total_sent` int(11) DEFAULT 0,
  `total_delivered` int(11) DEFAULT 0,
  `total_clicked` int(11) DEFAULT 0,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `target_value` varchar(255) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notifications`
--

INSERT INTO `notifications` (`id`, `title`, `message`, `image_url`, `action_url`, `target_type`, `target_users`, `status`, `scheduled_at`, `sent_at`, `total_sent`, `total_delivered`, `total_clicked`, `created_by`, `created_at`, `updated_at`, `target_value`) VALUES
(1, 'Test', 'tell me', '', '', 'all', NULL, 'sent', NULL, NULL, 0, 0, 0, 1, '2025-05-27 08:07:56', '2025-05-27 08:07:56', NULL),
(4, 'Testing notification', 'testing', '', '', 'all', NULL, 'sent', NULL, '2025-05-27 09:04:46', 1, 0, 0, 1, '2025-05-27 09:04:43', '2025-05-27 09:04:46', NULL),
(5, 'Testing notification', 'tethis is test', '', '', 'active_users', NULL, 'sent', NULL, '2025-05-27 09:07:33', 1, 0, 0, 1, '2025-05-27 09:07:30', '2025-05-27 09:07:33', NULL),
(6, 'Testing notification', 'tell me', '', '', 'all', NULL, 'sent', NULL, '2025-05-27 09:09:14', 1, 0, 0, 1, '2025-05-27 09:09:11', '2025-05-27 09:09:14', NULL),
(7, 'Test Notification - 09:13:13', 'This is a test notification sent at 2025-05-27 09:13:13', '', '', 'all', NULL, 'sent', NULL, '2025-05-27 09:13:16', 1, 0, 0, 1, '2025-05-27 09:13:13', '2025-05-27 09:13:16', NULL),
(8, 'Testing notification', 'tell me', '', '', 'all', NULL, 'sent', NULL, '2025-05-27 09:15:43', 1, 1, 0, 1, '2025-05-27 09:15:37', '2025-05-27 09:15:43', NULL),
(9, 'Testing notification', 'tell me', '', '', 'all', NULL, 'sent', '2025-05-27 09:18:00', '2025-05-27 09:22:32', 1, 0, 0, 1, '2025-05-27 09:17:01', '2025-05-27 09:22:32', NULL),
(10, 'Testing notification', 'tell me', '', '', 'all', NULL, 'sent', '2025-05-27 09:24:00', '2025-05-27 09:25:50', 1, 0, 0, 1, '2025-05-27 09:23:12', '2025-05-27 09:25:50', NULL),
(11, 'test', 'tell', '', '', 'active_users', NULL, 'sent', '2025-05-27 09:08:00', '2025-05-27 09:39:40', 1, 0, 0, 1, '2025-05-27 09:27:12', '2025-05-27 09:39:40', NULL),
(12, 'Testing notification', 'tee', '', '', 'all', NULL, 'sent', '2025-05-27 09:34:00', '2025-05-27 09:39:43', 1, 0, 0, 1, '2025-05-27 09:33:06', '2025-05-27 09:39:43', NULL),
(13, 'Testing notification', 'tell me', '', '', 'all', NULL, 'sent', '2025-05-27 09:38:00', '2025-05-27 09:39:45', 1, 0, 0, 1, '2025-05-27 09:37:20', '2025-05-27 09:39:45', NULL),
(14, 'test', 'tethis is', '', '', 'all', NULL, 'sent', '2025-05-27 10:07:00', '2025-05-27 10:07:31', 1, 0, 0, 1, '2025-05-27 10:06:31', '2025-05-27 10:07:31', NULL),
(15, 'rewrew', 'erewrwer', '', '', 'all', NULL, 'sent', '2025-05-27 10:10:00', '2025-05-27 10:12:07', 1, 0, 0, 1, '2025-05-27 10:09:18', '2025-05-27 10:12:07', NULL),
(16, 'Testing notification', 'tell me', '', '', 'all', NULL, 'sent', NULL, '2025-05-27 10:20:16', 1, 0, 0, 1, '2025-05-27 10:20:13', '2025-05-27 10:20:16', NULL),
(17, 'Testing notification', 'testing before', 'https://i.ytimg.com/vi/fpnROYp3vRU/sddefault.jpg', 'https://i.ytimg.com/vi/fpnROYp3vRU/sddefault.jpg', 'all', NULL, 'sent', NULL, '2025-05-27 11:49:29', 1, 0, 0, 1, '2025-05-27 11:49:26', '2025-05-27 11:49:29', NULL),
(18, 'yrr', 'yrtr', '', '', 'all', NULL, 'sent', NULL, '2025-05-27 11:50:17', 1, 1, 0, 1, '2025-05-27 11:50:13', '2025-05-27 11:52:18', NULL),
(19, 'fgfdss', 'gdfsgds', '', '', 'all', NULL, 'sent', NULL, '2025-05-27 11:50:36', 1, 1, 0, 1, '2025-05-27 11:50:33', '2025-05-27 11:50:40', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `notification_delivery`
--

CREATE TABLE `notification_delivery` (
  `id` int(11) NOT NULL,
  `notification_id` int(11) DEFAULT NULL,
  `device_id` varchar(255) DEFAULT NULL,
  `status` enum('sent','delivered','clicked','failed') NOT NULL,
  `error_message` text DEFAULT NULL,
  `delivered_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `clicked_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `notification_delivery`
--

INSERT INTO `notification_delivery` (`id`, `notification_id`, `device_id`, `status`, `error_message`, `delivered_at`, `created_at`, `updated_at`, `clicked_at`) VALUES
(1, 4, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:04:46', '2025-05-27 09:04:46', '2025-05-27 09:04:46', NULL),
(2, 4, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:04:46', '2025-05-27 09:04:46', '2025-05-27 09:04:46', NULL),
(3, 4, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:04:46', '2025-05-27 09:04:46', '2025-05-27 09:04:46', NULL),
(4, 4, '741616b100b4f521', 'sent', NULL, '2025-05-27 09:04:46', '2025-05-27 09:04:46', '2025-05-27 09:04:46', NULL),
(5, 5, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:07:33', '2025-05-27 09:07:33', '2025-05-27 09:07:33', NULL),
(6, 5, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:07:33', '2025-05-27 09:07:33', '2025-05-27 09:07:33', NULL),
(7, 5, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:07:33', '2025-05-27 09:07:33', '2025-05-27 09:07:33', NULL),
(8, 5, '741616b100b4f521', 'sent', NULL, '2025-05-27 09:07:33', '2025-05-27 09:07:33', '2025-05-27 09:07:33', NULL),
(9, 6, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:09:14', '2025-05-27 09:09:14', '2025-05-27 09:09:14', NULL),
(10, 6, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:09:14', '2025-05-27 09:09:14', '2025-05-27 09:09:14', NULL),
(11, 6, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:09:14', '2025-05-27 09:09:14', '2025-05-27 09:09:14', NULL),
(12, 6, '741616b100b4f521', 'sent', NULL, '2025-05-27 09:09:14', '2025-05-27 09:09:14', '2025-05-27 09:09:14', NULL),
(13, 7, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:13:16', '2025-05-27 09:13:16', '2025-05-27 09:13:16', NULL),
(14, 7, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:13:16', '2025-05-27 09:13:16', '2025-05-27 09:13:16', NULL),
(15, 7, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:13:16', '2025-05-27 09:13:16', '2025-05-27 09:13:16', NULL),
(16, 7, '741616b100b4f521', 'sent', NULL, '2025-05-27 09:13:16', '2025-05-27 09:13:16', '2025-05-27 09:13:16', NULL),
(17, 8, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:15:43', '2025-05-27 09:15:43', '2025-05-27 09:15:43', NULL),
(18, 8, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:15:43', '2025-05-27 09:15:43', '2025-05-27 09:15:43', NULL),
(19, 8, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:15:43', '2025-05-27 09:15:43', '2025-05-27 09:15:43', NULL),
(20, 8, '741616b100b4f521', 'delivered', NULL, '2025-05-27 09:15:43', '2025-05-27 09:15:43', '2025-05-27 09:15:43', NULL),
(21, 9, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:22:32', '2025-05-27 09:22:32', '2025-05-27 09:22:32', NULL),
(22, 9, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:22:32', '2025-05-27 09:22:32', '2025-05-27 09:22:32', NULL),
(23, 9, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:22:32', '2025-05-27 09:22:32', '2025-05-27 09:22:32', NULL),
(24, 9, '741616b100b4f521', 'sent', NULL, '2025-05-27 09:22:32', '2025-05-27 09:22:32', '2025-05-27 09:22:32', NULL),
(25, 10, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:25:50', '2025-05-27 09:25:50', '2025-05-27 09:25:50', NULL),
(26, 10, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:25:50', '2025-05-27 09:25:50', '2025-05-27 09:25:50', NULL),
(27, 10, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:25:50', '2025-05-27 09:25:50', '2025-05-27 09:25:50', NULL),
(28, 10, '741616b100b4f521', 'sent', NULL, '2025-05-27 09:25:50', '2025-05-27 09:25:50', '2025-05-27 09:25:50', NULL),
(29, 11, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:39:40', '2025-05-27 09:39:40', '2025-05-27 09:39:40', NULL),
(30, 11, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:39:40', '2025-05-27 09:39:40', '2025-05-27 09:39:40', NULL),
(31, 11, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:39:40', '2025-05-27 09:39:40', '2025-05-27 09:39:40', NULL),
(32, 11, '741616b100b4f521', 'sent', NULL, '2025-05-27 09:39:40', '2025-05-27 09:39:40', '2025-05-27 09:39:40', NULL),
(33, 12, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:39:43', '2025-05-27 09:39:43', '2025-05-27 09:39:43', NULL),
(34, 12, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:39:43', '2025-05-27 09:39:43', '2025-05-27 09:39:43', NULL),
(35, 12, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:39:43', '2025-05-27 09:39:43', '2025-05-27 09:39:43', NULL),
(36, 12, '741616b100b4f521', 'sent', NULL, '2025-05-27 09:39:43', '2025-05-27 09:39:43', '2025-05-27 09:39:43', NULL),
(37, 13, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:39:45', '2025-05-27 09:39:45', '2025-05-27 09:39:45', NULL),
(38, 13, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:39:45', '2025-05-27 09:39:45', '2025-05-27 09:39:45', NULL),
(39, 13, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 09:39:45', '2025-05-27 09:39:45', '2025-05-27 09:39:45', NULL),
(40, 13, '741616b100b4f521', 'sent', NULL, '2025-05-27 09:39:45', '2025-05-27 09:39:45', '2025-05-27 09:39:45', NULL),
(41, 14, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 10:07:31', '2025-05-27 10:07:31', '2025-05-27 10:07:31', NULL),
(42, 14, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 10:07:31', '2025-05-27 10:07:31', '2025-05-27 10:07:31', NULL),
(43, 14, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 10:07:31', '2025-05-27 10:07:31', '2025-05-27 10:07:31', NULL),
(44, 14, '741616b100b4f521', 'sent', NULL, '2025-05-27 10:07:31', '2025-05-27 10:07:31', '2025-05-27 10:07:31', NULL),
(45, 15, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 10:12:07', '2025-05-27 10:12:07', '2025-05-27 10:12:07', NULL),
(46, 15, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 10:12:07', '2025-05-27 10:12:07', '2025-05-27 10:12:07', NULL),
(47, 15, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 10:12:07', '2025-05-27 10:12:07', '2025-05-27 10:12:07', NULL),
(48, 15, '741616b100b4f521', 'sent', NULL, '2025-05-27 10:12:07', '2025-05-27 10:12:07', '2025-05-27 10:12:07', NULL),
(49, 16, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 10:20:16', '2025-05-27 10:20:16', '2025-05-27 10:20:16', NULL),
(50, 16, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 10:20:16', '2025-05-27 10:20:16', '2025-05-27 10:20:16', NULL),
(51, 16, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 10:20:16', '2025-05-27 10:20:16', '2025-05-27 10:20:16', NULL),
(52, 16, '741616b100b4f521', 'sent', NULL, '2025-05-27 10:20:16', '2025-05-27 10:20:16', '2025-05-27 10:20:16', NULL),
(53, 17, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 11:49:29', '2025-05-27 11:49:29', '2025-05-27 11:49:29', NULL),
(54, 17, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 11:49:29', '2025-05-27 11:49:29', '2025-05-27 11:49:29', NULL),
(55, 17, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 11:49:29', '2025-05-27 11:49:29', '2025-05-27 11:49:29', NULL),
(56, 17, '741616b100b4f521', 'sent', NULL, '2025-05-27 11:49:29', '2025-05-27 11:49:29', '2025-05-27 11:49:29', NULL),
(57, 18, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 11:50:17', '2025-05-27 11:50:17', '2025-05-27 11:50:17', NULL),
(58, 18, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 11:50:17', '2025-05-27 11:50:17', '2025-05-27 11:50:17', NULL),
(59, 18, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 11:50:17', '2025-05-27 11:50:17', '2025-05-27 11:50:17', NULL),
(60, 18, '741616b100b4f521', 'delivered', NULL, '2025-05-27 11:52:18', '2025-05-27 11:50:17', '2025-05-27 11:52:18', NULL),
(61, 19, 'simple_test_1748334007', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 11:50:36', '2025-05-27 11:50:36', '2025-05-27 11:50:36', NULL),
(62, 19, 'simple_reg_test_1748334305', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 11:50:36', '2025-05-27 11:50:36', '2025-05-27 11:50:36', NULL),
(63, 19, 'test_device_1748334372', 'failed', '{\n  \"error\": {\n    \"code\": 400,\n    \"message\": \"The registration token is not a valid FCM registration token\",\n    \"status\": \"INVALID_ARGUMENT\",\n    \"details\": [\n      {\n        \"@type\": \"type.googleapis.com/google.firebase.fcm.v1.FcmError\",\n        \"errorCode\": \"INVALID_ARGUMENT\"\n      }\n    ]\n  }\n}\n', '2025-05-27 11:50:36', '2025-05-27 11:50:36', '2025-05-27 11:50:36', NULL),
(64, 19, '741616b100b4f521', 'delivered', NULL, '2025-05-27 11:50:40', '2025-05-27 11:50:36', '2025-05-27 11:50:40', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `system_logs`
--

CREATE TABLE `system_logs` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `table_name` varchar(50) DEFAULT NULL,
  `record_id` int(11) DEFAULT NULL,
  `old_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`old_values`)),
  `new_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`new_values`)),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- --------------------------------------------------------

--
-- Table structure for table `text_categories`
--

CREATE TABLE `text_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(50) NOT NULL,
  `display_name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `text_categories`
--

INSERT INTO `text_categories` (`id`, `name`, `display_name`, `description`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'romantics', 'Romantic Messages', 'Sweet and romantic text messages', 1, 1, '2025-05-27 07:14:08', '2025-05-27 08:42:30'),
(2, 'sad', 'Sad Messages', 'Emotional and sad text messages', 1, 2, '2025-05-27 07:14:08', '2025-05-27 07:14:08'),
(3, 'funny', 'Funny Messages', 'Humorous and funny text messages', 1, 3, '2025-05-27 07:14:08', '2025-05-27 07:14:08'),
(5, 'tests', 'test', 'test', 1, 0, '2025-05-27 08:40:14', '2025-05-27 10:43:09'),
(6, 'motivational', 'Motivational Messages', 'Inspiring and motivational text messages', 1, 4, '2025-05-27 10:57:58', '2025-05-27 10:57:58'),
(7, 'love', 'Love Messages', 'Deep love and affection messages', 1, 5, '2025-05-27 10:57:59', '2025-05-27 10:57:59'),
(8, 'friendship', 'Friendship Messages', 'Messages for friends and friendship', 1, 6, '2025-05-27 10:57:59', '2025-05-27 10:57:59');

-- --------------------------------------------------------

--
-- Table structure for table `text_content`
--

CREATE TABLE `text_content` (
  `id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_by` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `text_content`
--

INSERT INTO `text_content` (`id`, `category_id`, `message`, `is_active`, `sort_order`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 1, 'You are the reason my heart beats. ????????????', 1, 1, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(2, 1, 'Every love story is beautiful, but ours is my favorite. ????????✨', 1, 2, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(3, 1, 'I love you more than words can say. ????????????', 1, 3, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(4, 1, 'You make my world brighter. ????✨????', 1, 4, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(5, 1, 'Holding your hand is my favorite feeling. ????❤️????', 1, 5, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(6, 1, 'You\'re my today, my tomorrow, and my forever. ????⏳????', 1, 6, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(7, 1, 'Without you, my life feels incomplete. ????????????', 1, 7, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(8, 1, 'Your love is my greatest treasure. ????❤️????', 1, 8, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(9, 1, 'You’re the dream I never want to wake up from. ????????✨', 1, 9, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(10, 1, 'Loving you is the best decision I ever made. ????????????', 1, 10, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(11, 1, 'You are my happy place. ????????❤️', 1, 11, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(12, 1, 'Every time I see you, I fall in love again. ????????????', 1, 12, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(13, 1, 'I want to be the reason behind your smile. ????????✨', 1, 13, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(14, 1, 'My heart belongs to you, forever and always. ????????⏳  ', 1, 14, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(15, 1, 'You light up my soul like stars in the sky. ????????', 1, 15, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(16, 1, 'My heart beats only for you. ????????❤️????', 1, 16, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(17, 1, 'You\'re my favorite hello and hardest goodbye. ????❤️????', 1, 17, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(18, 1, 'Falling for you was the best accident ever. ????????????', 1, 18, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(19, 1, 'With you, every moment is magical. ✨????', 1, 19, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(20, 1, 'I love you more than chocolate. ????❤️????', 1, 20, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(21, 1, 'You\'re my sunshine on a cloudy day. ????️☀️????', 1, 21, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(22, 1, 'Your voice is my favorite sound. ????????????', 1, 22, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(23, 1, 'Every second with you is precious. ⏳????', 1, 23, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(24, 1, 'I’m much more \'me\' when I’m with you. ????????', 1, 24, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(25, 1, 'You\'re the peanut butter to my jelly. ????????❤️', 1, 25, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(26, 1, 'Being yours is my favorite feeling. ????????', 1, 26, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(27, 1, 'No matter what, you\'re my always. ⏳????', 1, 27, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(28, 1, 'You\'re my dream come true. ????????????', 1, 28, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(29, 1, 'My heart whispers your name 24/7. ????????????', 1, 29, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(30, 1, 'Forever looks beautiful with you. ????????', 1, 30, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(31, 1, 'You\'re not just special, you\'re my everything. ????❤️', 1, 31, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(32, 1, 'I found my paradise in your eyes. ????️????????', 1, 32, 1, '2025-05-27 07:14:14', '2025-05-27 07:14:14'),
(33, 1, 'Your love is my daily dose of happiness. ????❤️????', 1, 33, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(34, 2, 'Tears speak when words can\'t. ????????????', 1, 1, 1, '2025-05-27 07:14:15', '2025-05-27 07:18:33'),
(35, 2, 'It hurts when you realize you meant nothing to someone. ????????????', 1, 2, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(36, 2, 'Sometimes, the heart sees what the eyes can’t. ????????????', 1, 3, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(37, 2, 'The hardest part of loving is letting go. ????????⏳', 1, 4, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(38, 2, 'I\'m smiling, but deep inside, I\'m broken. ????????????', 1, 5, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(39, 2, 'You promised forever, but left in a moment. ????⏳????', 1, 6, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(40, 2, 'Love shouldn’t hurt this much. ????????????', 1, 7, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(41, 2, 'I miss the old \'us\'. ????????????', 1, 8, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(42, 2, 'Some people come into your life just to leave. ????????????️', 1, 9, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(43, 2, 'My heart is heavy, but I still smile. ????????????', 1, 10, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(44, 2, 'I gave you my heart, but you broke it. ????????????', 1, 11, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(45, 2, 'Falling in love was easy, but letting go is painful. ????⏳????', 1, 12, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(46, 2, 'Not all wounds are visible. ????????????', 1, 13, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(47, 2, 'You were my happy ending, but now you\'re just a memory. ????????????', 1, 14, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(48, 2, 'It\'s sad when the person who gave you memories becomes one. ????????????', 1, 15, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(49, 2, 'Smiling outside, crying inside. ????????????', 1, 16, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(50, 2, 'Broken, but still breathing. ????????‍????️', 1, 17, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(51, 2, 'Loneliness is the loudest cry. ????????', 1, 18, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(52, 2, 'I’m not okay, but I smile anyway. ????????', 1, 19, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(53, 2, 'Fake smiles hide real pain. ????????', 1, 20, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(54, 2, 'Love leaves the deepest scars. ????????????', 1, 21, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(55, 2, 'Caring too much always hurts. ????????', 1, 22, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(56, 2, 'Lost in memories I can’t relive. ????????????', 1, 23, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(57, 2, 'I wish I could forget you like you forgot me. ????????', 1, 24, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(58, 2, 'It hurts when you’re replaced by someone else. ????????????', 1, 25, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(59, 2, 'My tears say what my words can\'t. ????????', 1, 26, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(60, 2, 'I miss the old me—the happy me. ????????', 1, 27, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(61, 2, 'Being ignored hurts more than being hated. ????????', 1, 28, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(62, 2, 'You left, but your memories didn’t. ????????️', 1, 29, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(63, 2, 'It\'s hard to move on when the past feels better. ????️????', 1, 30, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(64, 2, 'I trusted you more than myself. ????????', 1, 31, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(65, 2, 'Pain changes people. ????⚡', 1, 32, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(66, 2, 'I broke my own heart loving you. ????????', 1, 33, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(67, 3, 'You stole my heart, but I’ll let you keep it. ❤️???????? ', 1, 1, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(68, 3, 'You’re like my WiFi—sometimes strong, sometimes lost ????????????', 1, 2, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(69, 3, 'Are you a magician? Because whenever I look at you, everyone else disappears! ????✨????', 1, 3, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(70, 3, 'I love you more than pizza… and that’s saying a lot! ????❤️????️', 1, 4, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(71, 3, 'You\'re like my favorite song, stuck in my head all day! ????????????️', 1, 5, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(72, 3, 'You and I go together like tea and biscuits. ????????????️', 1, 6, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(73, 3, 'Are you my charger? Because you give me energy! ????⚡????️', 1, 7, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(74, 3, 'I must be a snowflake, because I’ve fallen for you! ❄️????????️', 1, 8, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(75, 3, 'I love you like a fat kid loves cake! ????????????️', 1, 9, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(76, 3, 'You + Me = A whole lot of trouble! ????????????️', 1, 10, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(77, 3, 'Love is like WiFi… it’s great when it’s strong but annoying when it’s weak! ????????????️', 1, 11, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(78, 3, 'If you were a vegetable, you’d be a ‘cute-cumber’! ????????????️', 1, 12, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(79, 3, 'I love you, but I also love sleep. Let’s compromise! ????❤️????', 1, 13, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(80, 3, 'You\'re my favorite notification. ????????????', 1, 14, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(81, 3, 'Do you have a map? Because I got lost in your eyes. ????️????????', 1, 15, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(82, 3, 'You\'re the reason I smile at my phone like an idiot. ????????????', 1, 16, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(83, 3, 'You\'re my love and my biggest distraction. ❤️????????', 1, 17, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(84, 3, 'You\'re like coffee—hot, addictive, and necessary. ☕????????', 1, 18, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(85, 3, 'I’m not lazy, I’m just saving energy for loving you. ????????????', 1, 19, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(86, 3, 'You\'re so sweet, diabetes got jealous. ????????', 1, 20, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(87, 3, 'You must be tired—running through my mind all day. ????????????', 1, 21, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(88, 3, 'If kisses were snowflakes, I’d send you a blizzard. ❄️????????', 1, 22, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(89, 3, 'I love you more than my phone... and that’s serious! ????❤️????', 1, 23, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(90, 3, 'Let’s grow old and weird together. ????????????', 1, 24, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(91, 3, 'You\'re the cheese to my pizza. ????????❤️????', 1, 25, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(92, 3, 'My heart beats faster when I see you… or pizza. ????❤️????', 1, 26, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(93, 3, 'You drive me crazy—but I like the ride. ????????????', 1, 27, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(94, 3, 'You\'re like an app I can’t uninstall. ????❤️????', 1, 28, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(95, 3, 'You make me laugh even when I don’t want to smile. ????????❤️', 1, 29, 1, '2025-05-27 07:14:15', '2025-05-27 07:14:15'),
(96, 5, 'testsss', 1, 1, 1, '2025-05-27 08:40:28', '2025-05-27 10:43:36'),
(97, 6, 'You are stronger than you think! ????✨', 1, 1, NULL, '2025-05-27 10:57:58', '2025-05-27 10:57:58'),
(98, 6, 'Every day is a new opportunity to grow! ????????', 1, 2, NULL, '2025-05-27 10:57:58', '2025-05-27 10:57:58'),
(99, 6, 'Believe in yourself and magic will happen! ✨????', 1, 3, NULL, '2025-05-27 10:57:58', '2025-05-27 10:57:58'),
(100, 6, 'Success starts with a single step! ????????', 1, 4, NULL, '2025-05-27 10:57:58', '2025-05-27 10:57:58'),
(101, 6, 'You\'ve got this! Keep pushing forward! ????????', 1, 5, NULL, '2025-05-27 10:57:58', '2025-05-27 10:57:58'),
(102, 7, 'Love is not just a feeling, it\'s a choice we make every day! ❤️????', 1, 1, NULL, '2025-05-27 10:57:59', '2025-05-27 10:57:59'),
(103, 7, 'In your arms, I found my home! ????????', 1, 2, NULL, '2025-05-27 10:57:59', '2025-05-27 10:57:59'),
(104, 7, 'Love is the bridge between two hearts! ????????', 1, 3, NULL, '2025-05-27 10:57:59', '2025-05-27 10:57:59'),
(105, 7, 'You are my today and all of my tomorrows! ????⏰', 1, 4, NULL, '2025-05-27 10:57:59', '2025-05-27 10:57:59'),
(106, 7, 'True love stories never have endings! ????????', 1, 5, NULL, '2025-05-27 10:57:59', '2025-05-27 10:57:59'),
(107, 8, 'Friends are the family we choose for ourselves! ????????', 1, 1, NULL, '2025-05-27 10:57:59', '2025-05-27 10:57:59'),
(108, 8, 'A true friend is one soul in two bodies! ????‍♀️✨', 1, 2, NULL, '2025-05-27 10:57:59', '2025-05-27 10:57:59'),
(109, 8, 'Friendship is the only cement that will ever hold the world together! ????????', 1, 3, NULL, '2025-05-27 10:57:59', '2025-05-27 10:57:59'),
(110, 8, 'Good friends are like stars, you don\'t always see them but you know they\'re there! ⭐????', 1, 4, NULL, '2025-05-27 10:57:59', '2025-05-27 10:57:59'),
(111, 8, 'Friends make the good times better and the hard times easier! ????????', 1, 5, NULL, '2025-05-27 10:57:59', '2025-05-27 10:57:59');

-- --------------------------------------------------------

--
-- Table structure for table `user_sessions`
--

CREATE TABLE `user_sessions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `session_id` varchar(100) DEFAULT NULL,
  `app_version` varchar(20) DEFAULT NULL,
  `started_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `ended_at` timestamp NULL DEFAULT NULL,
  `duration_seconds` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

--
-- Dumping data for table `user_sessions`
--

INSERT INTO `user_sessions` (`id`, `user_id`, `session_id`, `app_version`, `started_at`, `ended_at`, `duration_seconds`) VALUES
(1, 3, '68357724df088', '1.0.0', '2025-05-27 08:26:12', NULL, 0),
(2, 4, '68357724e6603', '', '2025-05-27 08:26:12', NULL, 0),
(3, 5, '6835773f4a3fc', '3.9.1', '2025-05-27 08:26:39', NULL, 0),
(4, 5, '6835943b2ffe5', '3.9.1', '2025-05-27 10:30:19', NULL, 0),
(5, 5, '6835943b7b875', '', '2025-05-27 10:30:19', NULL, 0),
(6, 5, '683595cfb1c96', '3.9.1', '2025-05-27 10:37:03', NULL, 0),
(7, 5, '683595cfb843d', '', '2025-05-27 10:37:03', NULL, 0),
(8, 5, '6835aa32f3eea', '3.9.1', '2025-05-27 12:04:02', NULL, 0),
(9, 5, '6835aa3302b20', '', '2025-05-27 12:04:03', NULL, 0);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_login_history`
--
ALTER TABLE `admin_login_history`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_login_time` (`login_time`);

--
-- Indexes for table `admin_sessions`
--
ALTER TABLE `admin_sessions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `session_id` (`session_id`),
  ADD KEY `idx_session_id` (`session_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_expires_at` (`expires_at`);

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `admob_config`
--
ALTER TABLE `admob_config`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `ad_analytics`
--
ALTER TABLE `ad_analytics`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_device_date` (`device_id`,`event_timestamp`),
  ADD KEY `idx_ad_type_date` (`ad_type`,`event_timestamp`),
  ADD KEY `idx_ad_analytics_event_timestamp` (`event_timestamp`),
  ADD KEY `idx_ad_analytics_device_event` (`device_id`,`event_type`);

--
-- Indexes for table `api_keys`
--
ALTER TABLE `api_keys`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `api_key` (`api_key`);

--
-- Indexes for table `api_rate_limits`
--
ALTER TABLE `api_rate_limits`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_ip_hour` (`ip_address`,`hour_window`);

--
-- Indexes for table `app_settings`
--
ALTER TABLE `app_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`),
  ADD KEY `updated_by` (`updated_by`);

--
-- Indexes for table `app_users`
--
ALTER TABLE `app_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `device_id` (`device_id`),
  ADD KEY `idx_app_users_device_id` (`device_id`);

--
-- Indexes for table `app_versions`
--
ALTER TABLE `app_versions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `feature_flags`
--
ALTER TABLE `feature_flags`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `feature_key` (`feature_key`);

--
-- Indexes for table `notifications`
--
ALTER TABLE `notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_notifications_status` (`status`),
  ADD KEY `idx_notifications_created_at` (`created_at`);

--
-- Indexes for table `notification_delivery`
--
ALTER TABLE `notification_delivery`
  ADD PRIMARY KEY (`id`),
  ADD KEY `notification_id` (`notification_id`),
  ADD KEY `device_id` (`device_id`);

--
-- Indexes for table `system_logs`
--
ALTER TABLE `system_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_action` (`action`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `text_categories`
--
ALTER TABLE `text_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `text_content`
--
ALTER TABLE `text_content`
  ADD PRIMARY KEY (`id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_category_active` (`category_id`,`is_active`),
  ADD KEY `idx_sort_order` (`sort_order`);

--
-- Indexes for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_login_history`
--
ALTER TABLE `admin_login_history`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `admin_sessions`
--
ALTER TABLE `admin_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `admob_config`
--
ALTER TABLE `admob_config`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `ad_analytics`
--
ALTER TABLE `ad_analytics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `api_keys`
--
ALTER TABLE `api_keys`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `api_rate_limits`
--
ALTER TABLE `api_rate_limits`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `app_settings`
--
ALTER TABLE `app_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `app_users`
--
ALTER TABLE `app_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `app_versions`
--
ALTER TABLE `app_versions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `feature_flags`
--
ALTER TABLE `feature_flags`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `notifications`
--
ALTER TABLE `notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `notification_delivery`
--
ALTER TABLE `notification_delivery`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=65;

--
-- AUTO_INCREMENT for table `system_logs`
--
ALTER TABLE `system_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `text_categories`
--
ALTER TABLE `text_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `text_content`
--
ALTER TABLE `text_content`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=112;

--
-- AUTO_INCREMENT for table `user_sessions`
--
ALTER TABLE `user_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admin_login_history`
--
ALTER TABLE `admin_login_history`
  ADD CONSTRAINT `admin_login_history_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `admin_sessions`
--
ALTER TABLE `admin_sessions`
  ADD CONSTRAINT `admin_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `admob_config`
--
ALTER TABLE `admob_config`
  ADD CONSTRAINT `admob_config_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admin_users` (`id`);

--
-- Constraints for table `ad_analytics`
--
ALTER TABLE `ad_analytics`
  ADD CONSTRAINT `ad_analytics_ibfk_1` FOREIGN KEY (`device_id`) REFERENCES `app_users` (`device_id`);

--
-- Constraints for table `app_settings`
--
ALTER TABLE `app_settings`
  ADD CONSTRAINT `app_settings_ibfk_1` FOREIGN KEY (`updated_by`) REFERENCES `admin_users` (`id`);

--
-- Constraints for table `notifications`
--
ALTER TABLE `notifications`
  ADD CONSTRAINT `notifications_ibfk_1` FOREIGN KEY (`created_by`) REFERENCES `admin_users` (`id`);

--
-- Constraints for table `notification_delivery`
--
ALTER TABLE `notification_delivery`
  ADD CONSTRAINT `notification_delivery_ibfk_1` FOREIGN KEY (`notification_id`) REFERENCES `notifications` (`id`),
  ADD CONSTRAINT `notification_delivery_ibfk_2` FOREIGN KEY (`device_id`) REFERENCES `app_users` (`device_id`);

--
-- Constraints for table `system_logs`
--
ALTER TABLE `system_logs`
  ADD CONSTRAINT `system_logs_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `text_content`
--
ALTER TABLE `text_content`
  ADD CONSTRAINT `text_content_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `text_categories` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `text_content_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `user_sessions`
--
ALTER TABLE `user_sessions`
  ADD CONSTRAINT `user_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `app_users` (`id`);
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
