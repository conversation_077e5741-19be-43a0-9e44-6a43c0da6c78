package com.anginatech.textrepeater;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

public class DialogAdapter extends FragmentStateAdapter {

    public DialogAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                return new RomanticS_Fragment(); // Romantic messages
            case 1:
                return new Sad_Fragment();
            case 2:
                return new Funny_Fragment();
            default:
                return new RomanticS_Fragment();
        }
    }

    @Override
    public int getItemCount() {
        return 3;
    }
}
