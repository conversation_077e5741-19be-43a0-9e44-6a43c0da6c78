<?php
$page_title = 'App Settings';
require_once 'includes/header.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_settings'])) {
        $settings = $_POST['settings'];
        
        try {
            foreach ($settings as $key => $value) {
                // Update or insert setting
                $stmt = $conn->prepare("
                    INSERT INTO app_settings (setting_key, setting_value, updated_at) 
                    VALUES (:key, :value, NOW())
                    ON DUPLICATE KEY UPDATE 
                    setting_value = :value, updated_at = NOW()
                ");
                $stmt->bindParam(':key', $key);
                $stmt->bindParam(':value', $value);
                $stmt->execute();
            }
            
            $success_message = "Settings saved successfully.";
        } catch (Exception $e) {
            $error_message = "Error saving settings: " . $e->getMessage();
        }
    }
}

// Get current settings
$stmt = $conn->prepare("SELECT setting_key, setting_value FROM app_settings");
$stmt->execute();
$settings_data = $stmt->fetchAll();

// Convert to associative array
$settings = [];
foreach ($settings_data as $setting) {
    $settings[$setting['setting_key']] = $setting['setting_value'];
}

// Default values if settings don't exist
$default_settings = [
    'app_name' => 'Text Repeater',
    'app_version' => '1.0.0',
    'min_app_version' => '1.0.0',
    'force_update' => '0',
    'maintenance_mode' => '0',
    'maintenance_message' => 'The app is currently under maintenance. Please try again later.',
    'privacy_policy_url' => '',
    'terms_of_service_url' => '',
    'support_email' => '<EMAIL>',
    'rate_app_url' => '',
    'share_app_url' => '',
    'feedback_email' => '<EMAIL>',
    'max_text_length' => '10000',
    'enable_analytics' => '1',
    'enable_crash_reporting' => '1',
    'default_language' => 'en',
    'supported_languages' => 'en,es,fr,de,it,pt,ru,zh,ja,ko',
    'enable_notifications' => '1',
    'notification_sound' => '1',
    'notification_vibration' => '1'
];

// Merge with defaults
foreach ($default_settings as $key => $default_value) {
    if (!isset($settings[$key])) {
        $settings[$key] = $default_value;
    }
}
?>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<form method="POST">
    <div class="row">
        <!-- General Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">General Settings</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="app_name" class="form-label">App Name</label>
                        <input type="text" class="form-control" id="app_name" name="settings[app_name]" 
                               value="<?php echo htmlspecialchars($settings['app_name']); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="app_version" class="form-label">Current App Version</label>
                        <input type="text" class="form-control" id="app_version" name="settings[app_version]" 
                               value="<?php echo htmlspecialchars($settings['app_version']); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="min_app_version" class="form-label">Minimum Required Version</label>
                        <input type="text" class="form-control" id="min_app_version" name="settings[min_app_version]" 
                               value="<?php echo htmlspecialchars($settings['min_app_version']); ?>" required>
                        <div class="form-text">Users with older versions will be prompted to update</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="force_update" name="settings[force_update]" 
                                   value="1" <?php echo $settings['force_update'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="force_update">
                                Force Update
                            </label>
                            <div class="form-text">Force users to update before using the app</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="maintenance_mode" name="settings[maintenance_mode]" 
                                   value="1" <?php echo $settings['maintenance_mode'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="maintenance_mode">
                                Maintenance Mode
                            </label>
                            <div class="form-text">Prevent users from using the app</div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="maintenance_message" class="form-label">Maintenance Message</label>
                        <textarea class="form-control" id="maintenance_message" name="settings[maintenance_message]" 
                                  rows="3"><?php echo htmlspecialchars($settings['maintenance_message']); ?></textarea>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- App Configuration -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">App Configuration</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="max_text_length" class="form-label">Maximum Text Length</label>
                        <input type="number" class="form-control" id="max_text_length" name="settings[max_text_length]" 
                               value="<?php echo htmlspecialchars($settings['max_text_length']); ?>" min="100" max="50000" required>
                        <div class="form-text">Maximum characters allowed in text input</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="default_language" class="form-label">Default Language</label>
                        <select class="form-select" id="default_language" name="settings[default_language]" required>
                            <option value="en" <?php echo $settings['default_language'] === 'en' ? 'selected' : ''; ?>>English</option>
                            <option value="es" <?php echo $settings['default_language'] === 'es' ? 'selected' : ''; ?>>Spanish</option>
                            <option value="fr" <?php echo $settings['default_language'] === 'fr' ? 'selected' : ''; ?>>French</option>
                            <option value="de" <?php echo $settings['default_language'] === 'de' ? 'selected' : ''; ?>>German</option>
                            <option value="it" <?php echo $settings['default_language'] === 'it' ? 'selected' : ''; ?>>Italian</option>
                            <option value="pt" <?php echo $settings['default_language'] === 'pt' ? 'selected' : ''; ?>>Portuguese</option>
                            <option value="ru" <?php echo $settings['default_language'] === 'ru' ? 'selected' : ''; ?>>Russian</option>
                            <option value="zh" <?php echo $settings['default_language'] === 'zh' ? 'selected' : ''; ?>>Chinese</option>
                            <option value="ja" <?php echo $settings['default_language'] === 'ja' ? 'selected' : ''; ?>>Japanese</option>
                            <option value="ko" <?php echo $settings['default_language'] === 'ko' ? 'selected' : ''; ?>>Korean</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="supported_languages" class="form-label">Supported Languages</label>
                        <input type="text" class="form-control" id="supported_languages" name="settings[supported_languages]" 
                               value="<?php echo htmlspecialchars($settings['supported_languages']); ?>" required>
                        <div class="form-text">Comma-separated language codes (e.g., en,es,fr)</div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enable_analytics" name="settings[enable_analytics]" 
                                   value="1" <?php echo $settings['enable_analytics'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="enable_analytics">
                                Enable Analytics
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enable_crash_reporting" name="settings[enable_crash_reporting]" 
                                   value="1" <?php echo $settings['enable_crash_reporting'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="enable_crash_reporting">
                                Enable Crash Reporting
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- URLs and Links -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">URLs and Links</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="privacy_policy_url" class="form-label">Privacy Policy URL</label>
                        <input type="url" class="form-control" id="privacy_policy_url" name="settings[privacy_policy_url]" 
                               value="<?php echo htmlspecialchars($settings['privacy_policy_url']); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="terms_of_service_url" class="form-label">Terms of Service URL</label>
                        <input type="url" class="form-control" id="terms_of_service_url" name="settings[terms_of_service_url]" 
                               value="<?php echo htmlspecialchars($settings['terms_of_service_url']); ?>">
                    </div>
                    
                    <div class="mb-3">
                        <label for="rate_app_url" class="form-label">Rate App URL</label>
                        <input type="url" class="form-control" id="rate_app_url" name="settings[rate_app_url]" 
                               value="<?php echo htmlspecialchars($settings['rate_app_url']); ?>">
                        <div class="form-text">Google Play Store or App Store URL</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="share_app_url" class="form-label">Share App URL</label>
                        <input type="url" class="form-control" id="share_app_url" name="settings[share_app_url]" 
                               value="<?php echo htmlspecialchars($settings['share_app_url']); ?>">
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Contact and Support -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Contact and Support</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="support_email" class="form-label">Support Email</label>
                        <input type="email" class="form-control" id="support_email" name="settings[support_email]" 
                               value="<?php echo htmlspecialchars($settings['support_email']); ?>" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="feedback_email" class="form-label">Feedback Email</label>
                        <input type="email" class="form-control" id="feedback_email" name="settings[feedback_email]" 
                               value="<?php echo htmlspecialchars($settings['feedback_email']); ?>" required>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Notification Settings -->
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Notification Settings</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="enable_notifications" name="settings[enable_notifications]" 
                                   value="1" <?php echo $settings['enable_notifications'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="enable_notifications">
                                Enable Push Notifications
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="notification_sound" name="settings[notification_sound]" 
                                   value="1" <?php echo $settings['notification_sound'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="notification_sound">
                                Default Notification Sound
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="notification_vibration" name="settings[notification_vibration]" 
                                   value="1" <?php echo $settings['notification_vibration'] ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="notification_vibration">
                                Default Notification Vibration
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Save Button -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-body text-center">
                    <button type="submit" name="save_settings" class="btn btn-primary btn-lg">
                        <i class="fas fa-save me-2"></i>Save All Settings
                    </button>
                </div>
            </div>
        </div>
    </div>
</form>

<?php require_once 'includes/footer.php'; ?>
