<?php
require_once 'config/database.php';
require_once 'classes/Auth.php';
require_once 'classes/TextContentManager.php';

$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$textManager = new TextContentManager();
$user = $auth->getCurrentUser();

// Check if editing existing content
$edit_id = $_GET['id'] ?? 0;
$is_edit = $edit_id > 0;
$content_data = null;

if ($is_edit) {
    $content_data = $textManager->getTextContentById($edit_id);
    if (!$content_data) {
        header('Location: text-content.php?error=Content not found');
        exit();
    }
}

$message = '';
$message_type = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $category_id = $_POST['category_id'] ?? 0;
    $message_text = trim($_POST['message'] ?? '');
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    $sort_order = intval($_POST['sort_order'] ?? 0);

    // Validation
    $errors = [];

    if (empty($category_id)) {
        $errors[] = 'Please select a category';
    }

    if (empty($message_text)) {
        $errors[] = 'Message cannot be empty';
    }

    if (empty($errors)) {
        if ($is_edit) {
            $result = $textManager->updateTextContent($edit_id, $category_id, $message_text, $is_active, $sort_order);
        } else {
            $result = $textManager->addTextContent($category_id, $message_text, $is_active, $sort_order, $user['id']);
        }

        if ($result['success']) {
            header('Location: text-content.php?success=' . urlencode($result['message']));
            exit();
        } else {
            $message = $result['message'];
            $message_type = 'error';
        }
    } else {
        $message = implode('<br>', $errors);
        $message_type = 'error';
    }
}

// Get categories for dropdown
$categories = $textManager->getCategories(true);

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <?php echo $is_edit ? 'Edit Text Content' : 'Add New Text Content'; ?>
                    </h3>
                    <a href="text-content.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to List
                    </a>
                </div>

                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $message_type == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="category_id" class="form-label">Category *</label>
                                    <div class="input-group">
                                        <select name="category_id" id="category_id" class="form-select" required>
                                            <option value="">Select Category</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category['id']; ?>"
                                                        <?php echo ($content_data && $content_data['category_id'] == $category['id']) ||
                                                                  ($_POST['category_id'] ?? '') == $category['id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($category['display_name']); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <a href="category-form.php" class="btn btn-outline-secondary" title="Add New Category">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                    </div>
                                    <div class="invalid-feedback">
                                        Please select a category.
                                    </div>
                                    <div class="form-text">
                                        <a href="categories.php">Manage categories</a> or <a href="category-form.php">add a new category</a>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" name="sort_order" id="sort_order" class="form-control"
                                           value="<?php echo $content_data ? $content_data['sort_order'] : ($_POST['sort_order'] ?? 0); ?>"
                                           min="0">
                                    <div class="form-text">Lower numbers appear first</div>
                                </div>
                            </div>

                            <div class="col-md-3">
                                <div class="mb-3">
                                    <div class="form-check form-switch mt-4">
                                        <input type="checkbox" name="is_active" id="is_active" class="form-check-input"
                                               <?php echo ($content_data && $content_data['is_active']) ||
                                                         (!$content_data && !isset($_POST['is_active'])) ||
                                                         isset($_POST['is_active']) ? 'checked' : ''; ?>>
                                        <label for="is_active" class="form-check-label">Active</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea name="message" id="message" class="form-control" rows="4" required
                                      placeholder="Enter your message here..."><?php echo htmlspecialchars($content_data ? $content_data['message'] : ($_POST['message'] ?? '')); ?></textarea>
                            <div class="invalid-feedback">
                                Please enter a message.
                            </div>
                            <div class="form-text">
                                <span id="char-count">0</span> characters
                            </div>
                        </div>

                        <div class="mb-3">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Preview</h6>
                                </div>
                                <div class="card-body">
                                    <div id="message-preview" class="text-muted">
                                        Type your message to see preview...
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="text-content.php" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i>
                                <?php echo $is_edit ? 'Update Content' : 'Add Content'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Character counter and preview
document.addEventListener('DOMContentLoaded', function() {
    const messageTextarea = document.getElementById('message');
    const charCount = document.getElementById('char-count');
    const preview = document.getElementById('message-preview');

    function updateCounterAndPreview() {
        const text = messageTextarea.value;
        charCount.textContent = text.length;

        if (text.trim()) {
            preview.textContent = text;
            preview.classList.remove('text-muted');
        } else {
            preview.textContent = 'Type your message to see preview...';
            preview.classList.add('text-muted');
        }
    }

    messageTextarea.addEventListener('input', updateCounterAndPreview);

    // Initial update
    updateCounterAndPreview();
});
</script>

<?php include 'includes/footer.php'; ?>
