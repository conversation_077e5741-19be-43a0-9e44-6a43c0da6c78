# Background Notification Service Setup

This guide explains how to set up the background notification service that runs continuously without requiring a browser or PC scheduling.

## Features

✅ **No <PERSON>rows<PERSON> Required** - Runs as a standalone PHP process  
✅ **No External Scheduling** - Self-contained service  
✅ **Automatic Restart** - Recovers from errors automatically  
✅ **Memory Management** - Prevents memory leaks  
✅ **Detailed Logging** - Full activity tracking  
✅ **Web Monitor** - Easy management interface  

## Quick Start

### Method 1: Using Batch Files (Easiest)

1. **Start the service:**
   ```
   Double-click: start-notification-service.bat
   ```

2. **Check status:**
   ```
   Double-click: check-notification-service.bat
   ```

3. **Stop the service:**
   ```
   Double-click: stop-notification-service.bat
   ```

### Method 2: Using Command Line

1. **Open Command Prompt in the TextRepeaterAdminPanel folder**

2. **Start the service:**
   ```cmd
   C:\xampp\php\php.exe cron\background-notification-service.php start
   ```

3. **Check status:**
   ```cmd
   C:\xampp\php\php.exe cron\background-notification-service.php status
   ```

4. **Stop the service:**
   ```cmd
   C:\xampp\php\php.exe cron\background-notification-service.php stop
   ```

### Method 3: Using Web Interface

1. **Open your browser and go to:**
   ```
   http://localhost/monirulvitextrepeater/TextRepeaterAdminPanel/service-monitor.php
   ```

2. **Use the buttons to start/stop/restart the service**

3. **Monitor real-time logs and status**

## How It Works

1. **Continuous Operation**: The service runs in an infinite loop, checking for scheduled notifications every 60 seconds

2. **Smart Processing**: Only processes notifications that are due to be sent

3. **Error Recovery**: Automatically restarts if errors occur

4. **Memory Management**: Monitors memory usage and restarts if needed

5. **Logging**: Records all activities with timestamps and memory usage

## Configuration

You can modify these settings in `background-notification-service.php`:

```php
private $checkInterval = 60;        // Check every 60 seconds
private $maxMemoryUsage = 100 * 1024 * 1024; // 100MB memory limit
```

## Log Files

- **Service Log**: `cron/background-service.log`
- **PID File**: `cron/background-service.pid`

## Troubleshooting

### Service Won't Start
1. Check if XAMPP is running
2. Verify PHP path in batch files
3. Check file permissions

### Service Stops Unexpectedly
1. Check the log file for errors
2. Verify database connection
3. Check Firebase service account file

### High Memory Usage
1. The service automatically restarts if memory exceeds 100MB
2. Check for memory leaks in custom code
3. Monitor the logs for restart events

## Advanced Usage

### Running as Windows Service

To run as a proper Windows service, you can use tools like:
- NSSM (Non-Sucking Service Manager)
- WinSW (Windows Service Wrapper)

Example with NSSM:
```cmd
nssm install "TextRepeaterNotificationService" "C:\xampp\php\php.exe"
nssm set "TextRepeaterNotificationService" Arguments "C:\xampp\htdocs\monirulvitextrepeater\TextRepeaterAdminPanel\cron\background-notification-service.php start"
nssm start "TextRepeaterNotificationService"
```

### Auto-Start on Boot

Add to Windows startup folder or create a scheduled task:
```cmd
schtasks /create /tn "TextRepeaterNotificationService" /tr "C:\xampp\htdocs\monirulvitextrepeater\TextRepeaterAdminPanel\start-notification-service.bat" /sc onstart /ru SYSTEM
```

## Benefits Over Other Methods

| Method | Browser Required | PC Always On | External Dependencies | Reliability |
|--------|------------------|--------------|----------------------|-------------|
| **Background Service** | ❌ No | ✅ Yes | ❌ None | ⭐⭐⭐⭐⭐ |
| Cron Jobs | ❌ No | ✅ Yes | ✅ Cron service | ⭐⭐⭐⭐ |
| Task Scheduler | ❌ No | ✅ Yes | ✅ Windows Task Scheduler | ⭐⭐⭐ |
| Browser Automation | ✅ Yes | ✅ Yes | ✅ Browser + Extensions | ⭐⭐ |
| External Services | ❌ No | ❌ No | ✅ Third-party accounts | ⭐⭐⭐ |

## Support

If you encounter any issues:

1. Check the service monitor: `service-monitor.php`
2. Review log files in `cron/background-service.log`
3. Verify your Firebase configuration
4. Test manual notification sending first

The background service provides the most reliable way to send notifications automatically without external dependencies!
