<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/drawer_color_item"
    android:orientation="vertical"
    >


    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/bottomSheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        >

        <androidx.cardview.widget.CardView
            android:id="@+id/cardViewCopy"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:clickable="true"
            android:foreground="?attr/selectableItemBackground"
            android:layout_marginStart="16dp"
            android:backgroundTint="@color/sub_layout_color"
            app:cardCornerRadius="50dp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_gravity="center"
                android:src="@drawable/baseline_content_copy_24" />


        </androidx.cardview.widget.CardView>


        <androidx.cardview.widget.CardView
            android:id="@+id/cardViewShare"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginStart="26dp"
            android:clickable="true"
            android:foreground="?attr/selectableItemBackground"
            android:backgroundTint="@color/sub_layout_color"
            app:cardCornerRadius="50dp"
            app:cardElevation="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.182"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_gravity="center"
                android:src="@drawable/baseline_share_24" />


        </androidx.cardview.widget.CardView>


        <TextView
            android:id="@+id/smsItemText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/cardViewShare"
            app:layout_constraintVertical_bias="0.0"
            android:textSize="19sp"
            android:paddingBottom="10dp"
            android:layout_marginBottom="16dp"
            android:lineSpacingExtra="2dp"
            android:textColor="@color/primary_text"
            android:fontFamily="@font/alice"
            android:layout_marginTop="20dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="26dp"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>









</LinearLayout>