<?php
if (isset($_GET['download'])) {
    $file = 'Text_Repeater.apk'; // Replace with your actual app file path
    if (file_exists($file)) {
        header('Content-Description: File Transfer');
        header('Content-Type: application/vnd.android.package-archive');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Expires: 0');
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        header('Content-Length: ' . filesize($file));
        readfile($file);
        exit;
    } else {
        echo "File not found.";
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Text Repeater Download</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #6e8efb, #a777e3);
        }
        .container {
            text-align: center;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
        }
        .app-icon {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, #6e8efb, #a777e3);
            border-radius: 15px;
            margin-bottom: 20px;
            display: inline-block;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
        }
        p {
            color: #666;
            margin-bottom: 20px;
        }
        .download-btn {
            background-color: #00aaff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .download-btn:hover {
            background-color: #0088cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="app-icon"></div>
        <h1>Text Repeater</h1>
        <p>Download the app to repeat your text effortlessly!</p>
        <a href="?download=1">
            <button class="download-btn">Download Now</button>
        </a>
    </div>
</body>
</html>