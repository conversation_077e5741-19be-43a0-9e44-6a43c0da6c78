package com.anginatech.textrepeater;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.TextView;

public class EmojiGridAdapter extends BaseAdapter {
    private final Context context;
    private final String[] emojis;


    public EmojiGridAdapter(Context context, String[] emojis) {
        this.context = context;
        this.emojis = emojis;
    }

    @Override
    public int getCount() {
        return emojis.length;
    }

    @Override
    public Object getItem(int position) {
        return emojis[position];
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (convertView == null) {
            convertView = LayoutInflater.from(context).inflate(R.layout.enoji_item_layout, parent, false);
        }

        TextView emojiTextView = convertView.findViewById(R.id.emojiTextView);
        emojiTextView.setText(emojis[position]);

        return convertView;
    }
}