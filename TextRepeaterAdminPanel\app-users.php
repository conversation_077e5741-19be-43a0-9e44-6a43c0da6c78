<?php
$page_title = 'App Users';
require_once 'includes/header.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = Config::ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Search and filter parameters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Build WHERE clause
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(device_id LIKE :search OR device_model LIKE :search OR device_brand LIKE :search)";
    $params[':search'] = '%' . $search . '%';
}

if (!empty($status_filter)) {
    if ($status_filter === 'active') {
        $where_conditions[] = "last_seen >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
    } elseif ($status_filter === 'inactive') {
        $where_conditions[] = "last_seen < DATE_SUB(NOW(), INTERVAL 7 DAY)";
    }
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(created_at) >= :date_from";
    $params[':date_from'] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(created_at) <= :date_to";
    $params[':date_to'] = $date_to;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM app_users $where_clause";
$count_stmt = $conn->prepare($count_query);
foreach ($params as $key => $value) {
    $count_stmt->bindValue($key, $value);
}
$count_stmt->execute();
$total_records = $count_stmt->fetch()['total'];
$total_pages = ceil($total_records / $limit);

// Get users
$query = "
    SELECT
        device_id,
        app_version,
        android_version,
        device_model,
        device_brand,
        language,
        timezone,
        total_sessions,
        created_at,
        last_seen,
        CASE
            WHEN last_seen >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 'active'
            ELSE 'inactive'
        END as status
    FROM app_users
    $where_clause
    ORDER BY last_seen DESC
    LIMIT :limit OFFSET :offset
";

$stmt = $conn->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$users = $stmt->fetchAll();

// Get summary statistics
$stats_query = "
    SELECT
        COUNT(*) as total_users,
        COUNT(CASE WHEN last_seen >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 END) as active_users,
        COUNT(CASE WHEN last_seen >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN 1 END) as daily_active,
        COUNT(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as new_users_30d
    FROM app_users
";
$stats_stmt = $conn->prepare($stats_query);
$stats_stmt->execute();
$stats = $stats_stmt->fetch();
?>

<!-- Summary Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($stats['total_users']); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Active Users (7d)
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($stats['active_users']); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-check fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-info shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Daily Active Users
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($stats['daily_active']); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            New Users (30d)
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            <?php echo number_format($stats['new_users_30d']); ?>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-plus fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Filter Users</h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search"
                               placeholder="Device ID, model, or brand..." value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Users</option>
                            <option value="active" <?php echo $status_filter === 'active' ? 'selected' : ''; ?>>Active (7d)</option>
                            <option value="inactive" <?php echo $status_filter === 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="date_from" name="date_from"
                               value="<?php echo htmlspecialchars($date_from); ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="date_to" name="date_to"
                               value="<?php echo htmlspecialchars($date_to); ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>Filter
                            </button>
                            <a href="app-users.php" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh me-2"></i>Reset
                            </a>
                            <button type="button" class="btn btn-outline-success" onclick="exportUsers()">
                                <i class="fas fa-download me-2"></i>Export
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Users List -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">App Users</h6>
            </div>
            <div class="card-body">
                <?php if (empty($users)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No users found</h5>
                        <p class="text-muted">No users match your current filters.</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Device Info</th>
                                    <th>App Version</th>
                                    <th>Status</th>
                                    <th>Sessions</th>
                                    <th>First Seen</th>
                                    <th>Last Seen</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($users as $user): ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?php echo htmlspecialchars($user['device_model']); ?></div>
                                            <div class="text-muted small">
                                                <?php echo htmlspecialchars($user['device_brand']); ?> •
                                                Android <?php echo htmlspecialchars($user['android_version']); ?>
                                            </div>
                                            <div class="text-muted small">
                                                <code><?php echo htmlspecialchars(substr($user['device_id'], 0, 16)); ?>...</code>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-info"><?php echo htmlspecialchars($user['app_version']); ?></span>
                                        </td>
                                        <td>
                                            <?php
                                            $status_class = $user['status'] === 'active' ? 'success' : 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo $status_class; ?>">
                                                <?php echo ucfirst($user['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="fw-bold"><?php echo number_format($user['total_sessions']); ?></span>
                                        </td>
                                        <td>
                                            <div class="small">
                                                <?php echo date('M j, Y', strtotime($user['created_at'])); ?>
                                                <br><?php echo date('g:i A', strtotime($user['created_at'])); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="small">
                                                <?php echo date('M j, Y', strtotime($user['last_seen'])); ?>
                                                <br><?php echo date('g:i A', strtotime($user['last_seen'])); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-info"
                                                        onclick="viewUser('<?php echo htmlspecialchars($user['device_id']); ?>')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-outline-primary"
                                                        onclick="sendNotificationToUser('<?php echo htmlspecialchars($user['device_id']); ?>')">
                                                    <i class="fas fa-bell"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            Previous
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            Next
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- View User Modal -->
<div class="modal fade" id="viewUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">User Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="userDetails">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<style>
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}
.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}
.text-gray-800 {
    color: #5a5c69 !important;
}
.text-gray-300 {
    color: #dddfeb !important;
}
</style>

<?php
$page_scripts = "
<script>
function viewUser(deviceId) {
    // Show loading
    document.getElementById('userDetails').innerHTML = '<div class=\"text-center\"><div class=\"loading-spinner\"></div> Loading...</div>';

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('viewUserModal'));
    modal.show();

    // Load user details via AJAX
    makeAjaxRequest('api/get-user.php?device_id=' + encodeURIComponent(deviceId), 'GET', null,
        function(response) {
            if (response.success) {
                const user = response.data;
                let html = '<div class=\"row\">';
                html += '<div class=\"col-md-6\"><strong>Device ID:</strong><br><code>' + user.device_id + '</code></div>';
                html += '<div class=\"col-md-6\"><strong>App Version:</strong><br>' + user.app_version + '</div>';
                html += '</div><hr>';
                html += '<div class=\"row\">';
                html += '<div class=\"col-md-6\"><strong>Device Model:</strong><br>' + user.device_model + '</div>';
                html += '<div class=\"col-md-6\"><strong>Device Brand:</strong><br>' + user.device_brand + '</div>';
                html += '</div><hr>';
                html += '<div class=\"row\">';
                html += '<div class=\"col-md-6\"><strong>Android Version:</strong><br>' + user.android_version + '</div>';
                html += '<div class=\"col-md-6\"><strong>Language:</strong><br>' + user.language + '</div>';
                html += '</div><hr>';
                html += '<div class=\"row\">';
                html += '<div class=\"col-md-6\"><strong>Timezone:</strong><br>' + user.timezone + '</div>';
                html += '<div class=\"col-md-6\"><strong>Total Sessions:</strong><br>' + formatNumber(user.total_sessions) + '</div>';
                html += '</div><hr>';
                html += '<div class=\"row\">';
                html += '<div class=\"col-md-6\"><strong>First Seen:</strong><br>' + formatDate(user.created_at) + '</div>';
                html += '<div class=\"col-md-6\"><strong>Last Seen:</strong><br>' + formatDate(user.last_seen) + '</div>';
                html += '</div>';

                document.getElementById('userDetails').innerHTML = html;
            } else {
                document.getElementById('userDetails').innerHTML = '<div class=\"alert alert-danger\">Error loading user details.</div>';
            }
        },
        function(status, error) {
            document.getElementById('userDetails').innerHTML = '<div class=\"alert alert-danger\">Error loading user details: ' + error + '</div>';
        }
    );
}

function sendNotificationToUser(deviceId) {
    // Redirect to notifications page with pre-filled device ID
    window.location.href = 'notifications.php?target_type=device&target_value=' + encodeURIComponent(deviceId);
}

function exportUsers() {
    // Get current filter parameters
    const params = new URLSearchParams(window.location.search);
    params.set('export', '1');

    // Create download link
    const downloadUrl = 'api/export-users.php?' + params.toString();
    window.open(downloadUrl, '_blank');
}
</script>
";

require_once 'includes/footer.php';
?>