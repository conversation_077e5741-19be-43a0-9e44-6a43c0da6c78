<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    app:flow_horizontalAlign="center"
    android:background="@color/mother_layout_color"
    >



    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >



    <ImageView
        android:id="@+id/sliderImage"
        android:layout_width="match_parent"
        android:layout_height="250dp"
        android:layout_marginHorizontal="15dp"
        android:layout_marginStart="15dp"
        android:layout_marginEnd="15dp"
        android:scaleType="fitCenter"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.22" />

    <TextView
        android:id="@+id/sliderTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="30dp"
        android:layout_marginTop="50dp"
        android:fontFamily="@font/allerta"
        android:gravity="center"
        android:text="Crazy Text"
        android:textColor="@color/primary_text"
        android:textSize="23sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sliderImage"
        app:layout_constraintVertical_bias="0.0" />

    <TextView
        android:id="@+id/sliderDesc"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/description_3"
        android:textSize="15sp"
        android:gravity="center"
        android:layout_marginTop="12dp"
        android:fontFamily="@font/allerta"
        android:textColor="@color/secondary_text"
        android:lineSpacingExtra="5dp"
        android:layout_marginHorizontal="30dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.498"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sliderTitle"
        app:layout_constraintVertical_bias="0.0"
        android:layout_marginBottom="10dp"
        />


    </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>