<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Random_Text_Activity"
    android:background="@color/mother_layout_color"
    >


    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >



    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/random_MaterialToolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:navigationIcon="@drawable/baseline_arrow_back_ios_24"
        app:navigationIconTint="@color/toolbar_text_color"
        app:titleTextColor="@color/toolbar_text_color"
        app:title="Random Text"
        app:menu="@menu/settings_item"
        />


    <EditText
        android:id="@+id/random_editEnterText"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="30dp"
        android:background="@drawable/edittext_backround"
        android:drawableStart="@drawable/baseline_repeat_24"
        android:hint=" Enter limit"
        android:inputType="number"
        android:maxLength="5"
        android:paddingLeft="16dp"
        android:paddingEnd="10dp"
        android:textColor="@color/primary_text"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.402"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/random_MaterialToolbar"
        app:layout_constraintVertical_bias="0.0" />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/checkBoxAlphabet"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Alphabet"
        android:checked="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/random_editEnterText"
        app:layout_constraintVertical_bias="0.0"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        app:buttonTint="@color/love"
        android:textSize="16.5sp"
        android:fontFamily="@font/alice"
        />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/checkBoxDigit"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Digits"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/checkBoxAlphabet"
        app:layout_constraintTop_toBottomOf="@+id/random_editEnterText"
        app:layout_constraintVertical_bias="0.0"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        app:buttonTint="@color/love"
        android:textSize="16.5sp"
        android:fontFamily="@font/alice"
        />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/checkBoxEmoji"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Emoji"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/checkBoxDigit"
        app:layout_constraintTop_toBottomOf="@+id/random_editEnterText"
        app:layout_constraintVertical_bias="0.0"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        app:buttonTint="@color/love"
        android:textSize="16.5sp"
        android:fontFamily="@font/alice"
        />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/checkBoxSpecial"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        app:buttonTint="@color/love"
        android:text="Special Symbols"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/checkBoxAlphabet"
        app:layout_constraintVertical_bias="0.0"
        android:textSize="16.5sp"
        android:fontFamily="@font/alice"
        />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/checkBoxAscii"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="ASCII Characters"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/checkBoxSpecial"
        app:layout_constraintTop_toBottomOf="@+id/checkBoxDigit"
        app:layout_constraintVertical_bias="0.0"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="16dp"
        app:buttonTint="@color/love"
        android:textSize="16.5sp"
        android:fontFamily="@font/alice"
        />

    <Button
        android:id="@+id/random_ButtonGenerate"
        android:layout_width="170dp"
        android:layout_height="58dp"
        android:text="Generate"
        android:backgroundTint="@color/love"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/checkBoxSpecial"
        app:layout_constraintVertical_bias="0.0"
        android:layout_marginStart="16dp"
        android:layout_marginTop="8dp"
        android:textSize="18sp"
        android:textColor="@color/white"
        />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout14"
        android:layout_width="match_parent"
        android:layout_height="310dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="8dp"
        android:background="@color/mother_layout_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/random_ButtonGenerate"
        app:layout_constraintVertical_bias="0.0">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scrollbars="vertical"
            android:fadeScrollbars="false"
            android:scrollbarThumbVertical="@color/love"
            android:scrollbarSize="3dp"
            >

            <TextView
                android:id="@+id/random_Display"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/primary_text"
                android:textSize="16sp"
                android:layout_margin="4dp"
                android:fontFamily="sans-serif"
                />





        </androidx.core.widget.NestedScrollView>





    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/random_Copy_share_Layout"
        android:layout_width="match_parent"
        android:layout_height="58dp"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="75dp"
        android:background="@drawable/copy_share_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/constraintLayout14"
        app:layout_constraintVertical_bias="0.0"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:visibility="gone"
        >


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/random_LayoutCopy"
            android:layout_width="170dp"
            android:layout_height="match_parent"
            android:clickable="true"
            android:foreground="?attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0">


            <ImageView
                android:id="@+id/imageView10"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginStart="38dp"
                android:src="@drawable/baseline_content_copy_24"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView41"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:fontFamily="@font/allerta"
                android:text="Copy"
                android:textColor="@color/secondary_text"
                android:textSize="17sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/imageView10"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.508" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/random_LayoutShare"
            android:layout_width="170dp"
            android:layout_height="match_parent"
            android:clickable="true"
            android:foreground="?attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toEndOf="@+id/random_LayoutCopy"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imageView4"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginStart="36dp"
                android:src="@drawable/baseline_share_24"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:fontFamily="@font/allerta"
                android:text="Share"
                android:textColor="@color/secondary_text"
                android:textSize="17sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/imageView4"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.508" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <View
            android:layout_width="2dp"
            android:layout_height="match_parent"
            android:layout_marginVertical="10dp"
            android:background="@drawable/view_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/random_LayoutShare"
            app:layout_constraintStart_toEndOf="@+id/random_LayoutCopy"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>



        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>






</androidx.constraintlayout.widget.ConstraintLayout>