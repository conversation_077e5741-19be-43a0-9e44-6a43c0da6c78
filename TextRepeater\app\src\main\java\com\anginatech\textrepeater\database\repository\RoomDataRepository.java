package com.anginatech.textrepeater.database.repository;

import android.content.Context;
import android.util.Log;

import com.anginatech.textrepeater.database.TextRepeaterDatabase;
import com.anginatech.textrepeater.database.dao.CategoryDao;
import com.anginatech.textrepeater.database.dao.MessageDao;
import com.anginatech.textrepeater.database.entities.CategoryEntity;
import com.anginatech.textrepeater.database.entities.MessageEntity;
import com.anginatech.textrepeater.models.Category;
import com.anginatech.textrepeater.models.SyncResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import io.reactivex.rxjava3.android.schedulers.AndroidSchedulers;
import io.reactivex.rxjava3.core.Completable;
import io.reactivex.rxjava3.core.Flowable;
import io.reactivex.rxjava3.core.Single;
import io.reactivex.rxjava3.disposables.CompositeDisposable;
import io.reactivex.rxjava3.schedulers.Schedulers;

/**
 * Room-based Data Repository
 * Replaces the old SQLite database operations with modern Room database
 * Provides reactive data operations and complete data replacement functionality
 */
public class RoomDataRepository {
    
    private static final String TAG = "RoomDataRepository";
    
    private static RoomDataRepository instance;
    private final TextRepeaterDatabase database;
    private final CategoryDao categoryDao;
    private final MessageDao messageDao;
    private final CompositeDisposable disposables;
    
    private RoomDataRepository(Context context) {
        database = TextRepeaterDatabase.getDatabase(context);
        categoryDao = database.categoryDao();
        messageDao = database.messageDao();
        disposables = new CompositeDisposable();
        Log.d(TAG, "RoomDataRepository initialized");
    }
    
    public static synchronized RoomDataRepository getInstance(Context context) {
        if (instance == null) {
            instance = new RoomDataRepository(context.getApplicationContext());
        }
        return instance;
    }
    
    // ==================== CATEGORY OPERATIONS ====================
    
    /**
     * Get all active categories
     */
    public Flowable<List<CategoryEntity>> getAllCategories() {
        return categoryDao.getAllCategories()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * Get category by name
     */
    public Single<CategoryEntity> getCategoryByName(String name) {
        return categoryDao.getCategoryByName(name)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * Insert or update category
     */
    public Completable insertCategory(CategoryEntity category) {
        return categoryDao.insertCategory(category)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * Insert multiple categories
     */
    public Completable insertCategories(List<CategoryEntity> categories) {
        return categoryDao.insertCategories(categories)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    // ==================== MESSAGE OPERATIONS ====================
    
    /**
     * Get all messages for a category
     */
    public Flowable<List<MessageEntity>> getMessagesByCategory(String categoryName) {
        return messageDao.getMessagesByCategory(categoryName)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * Get all messages
     */
    public Flowable<List<MessageEntity>> getAllMessages() {
        return messageDao.getAllMessages()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * Insert or update message
     */
    public Completable insertMessage(MessageEntity message) {
        return messageDao.insertMessage(message)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * Insert multiple messages
     */
    public Completable insertMessages(List<MessageEntity> messages) {
        return messageDao.insertMessages(messages)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * Increment message usage count
     */
    public Completable incrementMessageUsage(int messageId) {
        long timestamp = System.currentTimeMillis();
        return messageDao.incrementMessageUsage(messageId, timestamp)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * Toggle favorite status
     */
    public Completable toggleFavorite(int messageId) {
        long timestamp = System.currentTimeMillis();
        return messageDao.toggleFavorite(messageId, timestamp)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * Get favorite messages
     */
    public Flowable<List<MessageEntity>> getFavoriteMessages() {
        return messageDao.getFavoriteMessages()
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * Get recently used messages
     */
    public Flowable<List<MessageEntity>> getRecentlyUsedMessages(int limit) {
        return messageDao.getRecentlyUsedMessages(limit)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * Search messages
     */
    public Flowable<List<MessageEntity>> searchMessages(String searchTerm) {
        return messageDao.searchMessages(searchTerm)
            .subscribeOn(Schedulers.io())
            .observeOn(AndroidSchedulers.mainThread());
    }
    
    // ==================== COMPLETE DATA REPLACEMENT ====================
    
    /**
     * COMPLETE DATA REPLACEMENT - Replace all data with fresh server data
     * This ensures ONLY current server data is visible after sync
     */
    public Completable replaceAllDataWithFreshSync(SyncResponse syncData, DataReplacementCallback callback) {
        return Completable.fromAction(() -> {
            Log.d(TAG, "🔄 Starting COMPLETE data replacement with fresh server data");
            
            if (callback != null) {
                callback.onProgress("Clearing old data...", 10);
            }
            
            // Step 1: Clear ALL existing data
            clearAllDataSync();
            
            if (callback != null) {
                callback.onProgress("Processing fresh categories...", 30);
            }
            
            // Step 2: Convert and insert fresh categories
            List<CategoryEntity> categoryEntities = convertCategoriesToEntities(syncData.getCategories());
            categoryDao.insertCategories(categoryEntities).blockingAwait();
            
            if (callback != null) {
                callback.onProgress("Processing fresh messages...", 60);
            }
            
            // Step 3: Convert and insert fresh messages
            List<MessageEntity> messageEntities = convertMessagesToEntities(syncData.getContent());
            messageDao.insertMessages(messageEntities).blockingAwait();
            
            if (callback != null) {
                callback.onProgress("Verifying data integrity...", 90);
            }
            
            // Step 4: Verify data integrity
            int categoryCount = categoryDao.getTotalCategoriesCount().blockingGet();
            int messageCount = messageDao.getTotalMessagesCount().blockingGet();
            
            Log.d(TAG, "✅ FRESH data replacement completed: " + categoryCount + " categories, " + messageCount + " messages");
            
            if (callback != null) {
                callback.onProgress("Fresh data sync completed", 100);
                callback.onSuccess(categoryCount, messageCount);
            }
            
        })
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread());
    }
    
    /**
     * Clear all data synchronously (for use in transactions)
     */
    private void clearAllDataSync() {
        try {
            Log.d(TAG, "Clearing all existing data...");
            messageDao.deleteAllMessages().blockingAwait();
            categoryDao.deleteAllCategories().blockingAwait();
            Log.d(TAG, "All existing data cleared successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error clearing data: " + e.getMessage(), e);
            throw new RuntimeException("Failed to clear existing data", e);
        }
    }
    
    /**
     * Convert API categories to Room entities
     */
    private List<CategoryEntity> convertCategoriesToEntities(List<Category> apiCategories) {
        List<CategoryEntity> entities = new ArrayList<>();
        
        if (apiCategories != null) {
            for (Category apiCategory : apiCategories) {
                CategoryEntity entity = new CategoryEntity();
                entity.setName(apiCategory.getName());
                entity.setDisplayName(apiCategory.getDisplayName());
                entity.setDescription(apiCategory.getDescription());
                entity.setSortOrder(apiCategory.getSortOrder());
                entity.setActive(true);
                
                entities.add(entity);
            }
        }
        
        Log.d(TAG, "Converted " + entities.size() + " categories to entities");
        return entities;
    }
    
    /**
     * Convert API messages to Room entities
     */
    private List<MessageEntity> convertMessagesToEntities(Map<String, List<SyncResponse.SyncTextMessage>> apiContent) {
        List<MessageEntity> entities = new ArrayList<>();
        
        if (apiContent != null) {
            for (Map.Entry<String, List<SyncResponse.SyncTextMessage>> entry : apiContent.entrySet()) {
                String categoryName = entry.getKey();
                List<SyncResponse.SyncTextMessage> messages = entry.getValue();
                
                if (messages != null) {
                    for (SyncResponse.SyncTextMessage apiMessage : messages) {
                        MessageEntity entity = new MessageEntity();
                        entity.setServerId(apiMessage.getId());
                        entity.setMessage(apiMessage.getMessage());
                        entity.setCategoryName(categoryName);
                        entity.setCategoryId(apiMessage.getCategoryId());
                        entity.setSortOrder(apiMessage.getSortOrder());
                        
                        entities.add(entity);
                    }
                }
            }
        }
        
        Log.d(TAG, "Converted " + entities.size() + " messages to entities");
        return entities;
    }
    
    // ==================== UTILITY METHODS ====================
    
    /**
     * Get database statistics
     */
    public void getDatabaseStats(TextRepeaterDatabase.DatabaseStatsCallback callback) {
        database.getDatabaseStats(callback);
    }
    
    /**
     * Check if database is empty
     */
    public void isDatabaseEmpty(TextRepeaterDatabase.DatabaseEmptyCallback callback) {
        database.isDatabaseEmpty(callback);
    }
    
    /**
     * Cleanup resources
     */
    public void cleanup() {
        disposables.clear();
        Log.d(TAG, "RoomDataRepository cleanup completed");
    }
    
    /**
     * Callback interface for data replacement progress
     */
    public interface DataReplacementCallback {
        void onProgress(String message, int progress);
        void onSuccess(int categoryCount, int messageCount);
        void onError(String error);
    }
}
