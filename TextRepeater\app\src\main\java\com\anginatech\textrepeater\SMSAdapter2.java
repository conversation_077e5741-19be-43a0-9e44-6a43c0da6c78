package com.anginatech.textrepeater;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

public class SMSAdapter2 extends RecyclerView.Adapter<SMSAdapter2.SMSViewHolder> {

    private final List<String> smsList;
    private final OnItemClickListener listener;

    public interface OnItemClickListener {
        void onItemClick(String sms, boolean isChecked);
    }

    public SMSAdapter2(List<String> smsList, OnItemClickListener listener) {
        this.smsList = smsList;
        this.listener = listener;
    }

    @NonNull
    @Override
    public SMSViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View itemView = LayoutInflater.from(parent.getContext()).inflate(R.layout.message_item, parent, false);
        return new SMSViewHolder(itemView);
    }

    @Override
    public void onBindViewHolder(@NonNull SMSViewHolder holder, int position) {
        String sms = smsList.get(position);
        holder.smsText.setText(sms);

        holder.itemView.setOnClickListener(v -> {
            boolean isChecked = !holder.checkBox.isChecked();
            holder.checkBox.setChecked(isChecked);
            listener.onItemClick(sms, isChecked);
        });

        holder.checkBox.setOnClickListener(v -> {
            boolean isChecked = holder.checkBox.isChecked();
            listener.onItemClick(sms, isChecked);
        });
    }

    @Override
    public int getItemCount() {
        return smsList.size();
    }

    public static class SMSViewHolder extends RecyclerView.ViewHolder {
        TextView smsText;
        AppCompatCheckBox checkBox;

        public SMSViewHolder(@NonNull View itemView) {
            super(itemView);
            smsText = itemView.findViewById(R.id.smsText);
            checkBox = itemView.findViewById(R.id.checkBoxSms);
        }
    }
}