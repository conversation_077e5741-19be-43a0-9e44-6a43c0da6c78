<?php
// Check if database config is already loaded, if not load it
if (!class_exists('Database')) {
    require_once __DIR__ . '/../config/database.php';
}

// Include Firebase v1 Service
require_once __DIR__ . '/FirebaseV1Service.php';

class NotificationManager {
    private $conn;
    private $notifications_table = "notifications";
    private $users_table = "app_users";
    private $delivery_table = "notification_delivery";
    private $firebaseService;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
        $this->firebaseService = new FirebaseV1Service();
    }

    /**
     * Create a new notification
     */
    public function createNotification($data) {
        try {
            $query = "INSERT INTO " . $this->notifications_table . "
                     (title, message, image_url, action_url, target_type, target_users,
                      status, scheduled_at, created_by)
                     VALUES
                     (:title, :message, :image_url, :action_url, :target_type, :target_users,
                      :status, :scheduled_at, :created_by)";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':title', $data['title']);
            $stmt->bindParam(':message', $data['message']);
            $stmt->bindParam(':image_url', $data['image_url']);
            $stmt->bindParam(':action_url', $data['action_url']);
            $stmt->bindParam(':target_type', $data['target_type']);
            $stmt->bindParam(':target_users', $data['target_users']);
            $stmt->bindParam(':status', $data['status']);
            $stmt->bindParam(':scheduled_at', $data['scheduled_at']);
            $stmt->bindParam(':created_by', $data['created_by']);

            if ($stmt->execute()) {
                $notification_id = $this->conn->lastInsertId();

                // If it's an immediate notification, send it now
                if ($data['status'] === 'sent') {
                    $this->sendNotification($notification_id);
                }

                return [
                    'success' => true,
                    'message' => 'Notification created successfully',
                    'id' => $notification_id
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to create notification'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error creating notification: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send notification via Firebase v1 API
     */
    public function sendNotification($notification_id) {
        try {
            // Get notification details
            $notification = $this->getNotificationById($notification_id);
            if (!$notification) {
                return ['success' => false, 'message' => 'Notification not found'];
            }

            // Get target users
            $users = $this->getTargetUsers($notification);
            if (empty($users)) {
                return ['success' => false, 'message' => 'No target users found'];
            }

            $fcm_tokens = array_filter(array_column($users, 'fcm_token'));
            if (empty($fcm_tokens)) {
                return ['success' => false, 'message' => 'No valid FCM tokens found'];
            }

            // Prepare data payload
            $data = [
                'notification_id' => (string)$notification_id,
                'action_url' => $notification['action_url'] ?? '',
                'image_url' => $notification['image_url'] ?? ''
            ];

            // Send via Firebase v1 API
            $result = $this->firebaseService->sendNotification(
                $fcm_tokens,
                $notification['title'],
                $notification['message'],
                $data,
                $notification['image_url'] ?? null
            );

            if ($result['success']) {
                // Update notification status
                $this->updateNotificationStatus($notification_id, 'sent', $result['sent_count']);

                // Record delivery attempts
                $this->recordDeliveryAttempts($notification_id, $users, $result);

                return [
                    'success' => true,
                    'message' => 'Notification sent successfully via Firebase v1 API',
                    'sent_count' => $result['sent_count'],
                    'failed_count' => $result['failed_count'],
                    'total_count' => $result['total_count']
                ];
            } else {
                $this->updateNotificationStatus($notification_id, 'failed');
                return $result;
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error sending notification: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Send notification to topic (using Firebase v1 API)
     */
    public function sendToTopic($topic, $title, $message, $data = [], $imageUrl = null) {
        try {
            $result = $this->firebaseService->sendToTopic($topic, $title, $message, $data, $imageUrl);
            return $result;
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error sending topic notification: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get target users based on notification settings
     */
    private function getTargetUsers($notification) {
        switch ($notification['target_type']) {
            case 'all':
                $query = "SELECT device_id, fcm_token FROM " . $this->users_table . "
                         WHERE fcm_token IS NOT NULL AND fcm_token != ''";
                $stmt = $this->conn->prepare($query);
                break;

            case 'active_users':
                $query = "SELECT device_id, fcm_token FROM " . $this->users_table . "
                         WHERE fcm_token IS NOT NULL AND fcm_token != ''
                         AND last_seen >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
                $stmt = $this->conn->prepare($query);
                break;

            case 'specific_users':
                $target_users = json_decode($notification['target_users'], true);
                if (empty($target_users)) {
                    return [];
                }

                $placeholders = str_repeat('?,', count($target_users) - 1) . '?';
                $query = "SELECT device_id, fcm_token FROM " . $this->users_table . "
                         WHERE device_id IN ($placeholders)
                         AND fcm_token IS NOT NULL AND fcm_token != ''";
                $stmt = $this->conn->prepare($query);
                foreach ($target_users as $index => $user_id) {
                    $stmt->bindValue($index + 1, $user_id);
                }
                break;

            default:
                return [];
        }

        $stmt->execute();
        return $stmt->fetchAll();
    }

    /**
     * Update notification status
     */
    private function updateNotificationStatus($notification_id, $status, $sent_count = 0) {
        $query = "UPDATE " . $this->notifications_table . "
                 SET status = :status, total_sent = :sent_count, sent_at = NOW()
                 WHERE id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':status', $status);
        $stmt->bindParam(':sent_count', $sent_count);
        $stmt->bindParam(':id', $notification_id);
        $stmt->execute();
    }

    /**
     * Record delivery attempts
     */
    private function recordDeliveryAttempts($notification_id, $users, $result) {
        // Create a map of tokens to device_ids for easier lookup
        $tokenToDeviceMap = [];
        foreach ($users as $user) {
            $tokenToDeviceMap[$user['fcm_token']] = $user['device_id'];
        }

        // If we have detailed results from Firebase v1 API, use them
        if (isset($result['results']) && is_array($result['results'])) {
            foreach ($result['results'] as $tokenResult) {
                $token = $tokenResult['token'];
                $status = $tokenResult['success'] ? 'sent' : 'failed';
                $deviceId = $tokenToDeviceMap[$token] ?? null;

                if ($deviceId) {
                    $query = "INSERT INTO " . $this->delivery_table . "
                             (notification_id, device_id, status, error_message)
                             VALUES (:notification_id, :device_id, :status, :error_message)";

                    $stmt = $this->conn->prepare($query);
                    $stmt->bindParam(':notification_id', $notification_id);
                    $stmt->bindParam(':device_id', $deviceId);
                    $stmt->bindParam(':status', $status);
                    $errorMessage = $tokenResult['success'] ? null : ($tokenResult['error'] ?? 'Unknown error');
                    $stmt->bindParam(':error_message', $errorMessage);
                    $stmt->execute();
                }
            }
        } else {
            // Fallback: record all as sent (legacy behavior)
            foreach ($users as $user) {
                $status = 'sent';

                $query = "INSERT INTO " . $this->delivery_table . "
                         (notification_id, device_id, status)
                         VALUES (:notification_id, :device_id, :status)";

                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':notification_id', $notification_id);
                $stmt->bindParam(':device_id', $user['device_id']);
                $stmt->bindParam(':status', $status);
                $stmt->execute();
            }
        }
    }

    /**
     * Get notification by ID
     */
    public function getNotificationById($id) {
        $query = "SELECT * FROM " . $this->notifications_table . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();

        return $stmt->fetch();
    }

    /**
     * Get all notifications with pagination
     */
    public function getAllNotifications($page = 1, $limit = 20) {
        $offset = ($page - 1) * $limit;

        $query = "SELECT n.*, au.full_name as created_by_name
                 FROM " . $this->notifications_table . " n
                 LEFT JOIN admin_users au ON n.created_by = au.id
                 ORDER BY n.created_at DESC
                 LIMIT :limit OFFSET :offset";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
        $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    /**
     * Get total notification count
     */
    public function getTotalNotificationCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->notifications_table;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetch()['total'];
    }

    /**
     * Get scheduled notifications that need to be sent
     */
    public function getScheduledNotifications() {
        $query = "SELECT * FROM " . $this->notifications_table . "
                 WHERE status = 'scheduled' AND scheduled_at <= NOW()
                 ORDER BY scheduled_at ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll();
    }

    /**
     * Process all scheduled notifications that are due
     */
    public function processScheduledNotifications() {
        $scheduledNotifications = $this->getScheduledNotifications();
        $results = [];

        foreach ($scheduledNotifications as $notification) {
            $result = $this->sendNotification($notification['id']);
            $results[] = [
                'notification_id' => $notification['id'],
                'title' => $notification['title'],
                'scheduled_at' => $notification['scheduled_at'],
                'result' => $result
            ];
        }

        return [
            'success' => true,
            'processed_count' => count($results),
            'results' => $results
        ];
    }

    /**
     * Delete notification
     */
    public function deleteNotification($id) {
        try {
            // Delete delivery records first
            $query = "DELETE FROM " . $this->delivery_table . " WHERE notification_id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            // Delete notification
            $query = "DELETE FROM " . $this->notifications_table . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Notification deleted successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to delete notification'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error deleting notification: ' . $e->getMessage()
            ];
        }
    }
}
?>
