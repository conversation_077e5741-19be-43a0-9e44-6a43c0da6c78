<?php
require_once __DIR__ . '/../config/database.php';

class TextContentManager {
    private $conn;
    private $categories_table = "text_categories";
    private $content_table = "text_content";

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Get all categories
     */
    public function getCategories($active_only = false) {
        try {
            $query = "SELECT * FROM " . $this->categories_table;
            if ($active_only) {
                $query .= " WHERE is_active = 1";
            }
            $query .= " ORDER BY sort_order ASC, display_name ASC";

            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting categories: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get category by ID
     */
    public function getCategoryById($id) {
        try {
            $query = "SELECT * FROM " . $this->categories_table . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Error getting category: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get all text content with pagination
     */
    public function getTextContent($category_id = null, $page = 1, $per_page = 20, $search = '') {
        try {
            $offset = ($page - 1) * $per_page;

            $query = "SELECT tc.*, cat.display_name as category_name, au.username as created_by_name
                     FROM " . $this->content_table . " tc
                     LEFT JOIN " . $this->categories_table . " cat ON tc.category_id = cat.id
                     LEFT JOIN admin_users au ON tc.created_by = au.id
                     WHERE 1=1";

            $params = [];

            if ($category_id) {
                $query .= " AND tc.category_id = :category_id";
                $params[':category_id'] = $category_id;
            }

            if (!empty($search)) {
                $query .= " AND tc.message LIKE :search";
                $params[':search'] = '%' . $search . '%';
            }

            $query .= " ORDER BY tc.sort_order ASC, tc.created_at DESC LIMIT :offset, :per_page";

            $stmt = $this->conn->prepare($query);

            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }

            $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
            $stmt->bindValue(':per_page', $per_page, PDO::PARAM_INT);

            $stmt->execute();
            return $stmt->fetchAll();
        } catch (Exception $e) {
            error_log("Error getting text content: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get total count for pagination
     */
    public function getTextContentCount($category_id = null, $search = '') {
        try {
            $query = "SELECT COUNT(*) as total FROM " . $this->content_table . " WHERE 1=1";
            $params = [];

            if ($category_id) {
                $query .= " AND category_id = :category_id";
                $params[':category_id'] = $category_id;
            }

            if (!empty($search)) {
                $query .= " AND message LIKE :search";
                $params[':search'] = '%' . $search . '%';
            }

            $stmt = $this->conn->prepare($query);

            foreach ($params as $key => $value) {
                $stmt->bindValue($key, $value);
            }

            $stmt->execute();
            $result = $stmt->fetch();
            return $result['total'];
        } catch (Exception $e) {
            error_log("Error getting text content count: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Get text content by ID
     */
    public function getTextContentById($id) {
        try {
            $query = "SELECT tc.*, cat.display_name as category_name
                     FROM " . $this->content_table . " tc
                     LEFT JOIN " . $this->categories_table . " cat ON tc.category_id = cat.id
                     WHERE tc.id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            return $stmt->fetch();
        } catch (Exception $e) {
            error_log("Error getting text content by ID: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Add new text content
     */
    public function addTextContent($category_id, $message, $is_active = true, $sort_order = 0, $created_by = null) {
        try {
            $query = "INSERT INTO " . $this->content_table . "
                     (category_id, message, is_active, sort_order, created_by)
                     VALUES (:category_id, :message, :is_active, :sort_order, :created_by)";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':category_id', $category_id);
            $stmt->bindParam(':message', $message);
            $stmt->bindParam(':is_active', $is_active, PDO::PARAM_BOOL);
            $stmt->bindParam(':sort_order', $sort_order);
            $stmt->bindParam(':created_by', $created_by);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Text content added successfully',
                    'id' => $this->conn->lastInsertId()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to add text content'
                ];
            }
        } catch (Exception $e) {
            error_log("Error adding text content: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update text content
     */
    public function updateTextContent($id, $category_id, $message, $is_active = true, $sort_order = 0) {
        try {
            $query = "UPDATE " . $this->content_table . "
                     SET category_id = :category_id, message = :message,
                         is_active = :is_active, sort_order = :sort_order
                     WHERE id = :id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':category_id', $category_id);
            $stmt->bindParam(':message', $message);
            $stmt->bindParam(':is_active', $is_active, PDO::PARAM_BOOL);
            $stmt->bindParam(':sort_order', $sort_order);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Text content updated successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to update text content'
                ];
            }
        } catch (Exception $e) {
            error_log("Error updating text content: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete text content
     */
    public function deleteTextContent($id) {
        try {
            $query = "DELETE FROM " . $this->content_table . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Text content deleted successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to delete text content'
                ];
            }
        } catch (Exception $e) {
            error_log("Error deleting text content: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Toggle active status
     */
    public function toggleActiveStatus($id) {
        try {
            $query = "UPDATE " . $this->content_table . "
                     SET is_active = NOT is_active
                     WHERE id = :id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Status updated successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to update status'
                ];
            }
        } catch (Exception $e) {
            error_log("Error toggling status: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Get content for JSON API (for mobile app) - Legacy format
     */
    public function getContentForAPI() {
        try {
            $categories = $this->getCategories(true);
            $result = [];

            // Map category names to ensure compatibility with Android app
            $categoryMapping = [
                'romantics' => 'romantic',
                'romantic' => 'romantic',
                'sad' => 'sad',
                'funny' => 'funny'
            ];

            foreach ($categories as $category) {
                // Skip categories that aren't in our mapping (like 'test')
                if (!isset($categoryMapping[$category['name']])) {
                    continue;
                }

                $query = "SELECT id, message FROM " . $this->content_table . "
                         WHERE category_id = :category_id AND is_active = 1
                         ORDER BY sort_order ASC, id ASC";

                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':category_id', $category['id']);
                $stmt->execute();

                $content = $stmt->fetchAll();

                // Use the mapped category name
                $mappedName = $categoryMapping[$category['name']];
                $result[$mappedName] = $content;
            }

            // Ensure all expected categories exist even if empty
            $expectedCategories = ['romantic', 'sad', 'funny'];
            foreach ($expectedCategories as $expectedCategory) {
                if (!isset($result[$expectedCategory])) {
                    $result[$expectedCategory] = [];
                }
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error getting content for API: " . $e->getMessage());
            return [
                'romantic' => [],
                'sad' => [],
                'funny' => []
            ];
        }
    }

    /**
     * Get dynamic content for JSON API (for mobile app) - New dynamic format
     */
    public function getDynamicContentForAPI() {
        try {
            $categories = $this->getCategories(true);
            $result = [
                'categories' => [],
                'content' => []
            ];

            foreach ($categories as $category) {
                // Add category metadata
                $result['categories'][] = [
                    'id' => $category['id'],
                    'name' => $category['name'],
                    'display_name' => $category['display_name'],
                    'description' => $category['description'],
                    'sort_order' => $category['sort_order']
                ];

                // Get content for this category
                $query = "SELECT id, message FROM " . $this->content_table . "
                         WHERE category_id = :category_id AND is_active = 1
                         ORDER BY sort_order ASC, id ASC";

                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':category_id', $category['id']);
                $stmt->execute();

                $content = $stmt->fetchAll();
                $result['content'][$category['name']] = $content;
            }

            return $result;
        } catch (Exception $e) {
            error_log("Error getting dynamic content for API: " . $e->getMessage());
            return [
                'categories' => [],
                'content' => []
            ];
        }
    }

    /**
     * Add new category
     */
    public function addCategory($name, $display_name, $description = '', $is_active = true, $sort_order = 0) {
        try {
            // Check if category name already exists
            $check_query = "SELECT id FROM " . $this->categories_table . " WHERE name = :name";
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->bindParam(':name', $name);
            $check_stmt->execute();

            if ($check_stmt->rowCount() > 0) {
                return [
                    'success' => false,
                    'message' => 'Category name already exists'
                ];
            }

            $query = "INSERT INTO " . $this->categories_table . "
                     (name, display_name, description, is_active, sort_order)
                     VALUES (:name, :display_name, :description, :is_active, :sort_order)";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':display_name', $display_name);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':is_active', $is_active, PDO::PARAM_BOOL);
            $stmt->bindParam(':sort_order', $sort_order);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Category added successfully',
                    'id' => $this->conn->lastInsertId()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to add category'
                ];
            }
        } catch (Exception $e) {
            error_log("Error adding category: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update category
     */
    public function updateCategory($id, $name, $display_name, $description = '', $is_active = true, $sort_order = 0) {
        try {
            // Check if category name already exists (excluding current category)
            $check_query = "SELECT id FROM " . $this->categories_table . " WHERE name = :name AND id != :id";
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->bindParam(':name', $name);
            $check_stmt->bindParam(':id', $id);
            $check_stmt->execute();

            if ($check_stmt->rowCount() > 0) {
                return [
                    'success' => false,
                    'message' => 'Category name already exists'
                ];
            }

            $query = "UPDATE " . $this->categories_table . "
                     SET name = :name, display_name = :display_name, description = :description,
                         is_active = :is_active, sort_order = :sort_order
                     WHERE id = :id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':display_name', $display_name);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':is_active', $is_active, PDO::PARAM_BOOL);
            $stmt->bindParam(':sort_order', $sort_order);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Category updated successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to update category'
                ];
            }
        } catch (Exception $e) {
            error_log("Error updating category: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete category with option to force delete (including content)
     */
    public function deleteCategory($id, $force_delete = false) {
        try {
            // Check if category has content
            $content_check = "SELECT COUNT(*) as count FROM " . $this->content_table . " WHERE category_id = :id";
            $content_stmt = $this->conn->prepare($content_check);
            $content_stmt->bindParam(':id', $id);
            $content_stmt->execute();
            $content_result = $content_stmt->fetch();

            if ($content_result['count'] > 0 && !$force_delete) {
                return [
                    'success' => false,
                    'message' => 'Cannot delete category that contains content. Please move or delete all content first.',
                    'has_content' => true,
                    'content_count' => $content_result['count']
                ];
            }

            // Begin transaction for safe deletion
            $this->conn->beginTransaction();

            try {
                // If force delete is enabled, delete all content first
                if ($force_delete && $content_result['count'] > 0) {
                    $delete_content = "DELETE FROM " . $this->content_table . " WHERE category_id = :id";
                    $content_delete_stmt = $this->conn->prepare($delete_content);
                    $content_delete_stmt->bindParam(':id', $id);
                    $content_delete_stmt->execute();
                }

                // Delete the category
                $query = "DELETE FROM " . $this->categories_table . " WHERE id = :id";
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':id', $id);
                $stmt->execute();

                // Commit transaction
                $this->conn->commit();

                $message = $force_delete && $content_result['count'] > 0
                    ? "Category and {$content_result['count']} content items deleted successfully"
                    : "Category deleted successfully";

                return [
                    'success' => true,
                    'message' => $message
                ];

            } catch (Exception $e) {
                // Rollback transaction on error
                $this->conn->rollback();
                throw $e;
            }

        } catch (Exception $e) {
            error_log("Error deleting category: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Toggle category active status
     */
    public function toggleCategoryStatus($id) {
        try {
            $query = "UPDATE " . $this->categories_table . "
                     SET is_active = NOT is_active
                     WHERE id = :id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Category status updated successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to update category status'
                ];
            }
        } catch (Exception $e) {
            error_log("Error toggling category status: " . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ];
        }
    }
}
?>
