package com.anginatech.textrepeater;

import android.app.ProgressDialog;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;
import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.google.android.material.appbar.MaterialToolbar;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;


public class Random_Text_Activity extends AppCompatActivity {


    MaterialToolbar random_MaterialToolbar;
    EditText random_EditEnterText;
    AppCompatCheckBox checkBoxAlphabet, checkBoxDigit, checkBoxEmoji, checkBoxSpecial, checkBoxAscii;
    Button random_ButtonGenerate;
    TextView random_Display;

    private ProgressDialog progressDialog;
    ConstraintLayout random_Copy_share_Layout, random_LayoutCopy, random_LayoutShare;


    private static final String[] EMOJIS = {
            "😀", "😃", "😄", "😁", "😆", "😅", "😂", "🤣", "😊", "😇", "🙂", "🙃", "😉", "😌",
            "😍", "🥰", "😘", "😗", "😙", "😚", "😋", "😛", "😝", "😜", "🤪", "🤨", "🧐", "🤓",
            "😎", "🥸", "🤩", "🥳", "😏", "😒", "😞", "😔", "😟", "😕", "🙁", "☹️", "😣", "😖",
            "😫", "😩", "🥺", "😢", "😭", "😤", "😠", "😡", "🤬", "🤯", "😳", "🥵", "🥶", "😱",
            "😨", "😰", "😥", "😓", "🤗", "🤔", "🤭", "🤫", "🤥", "😶", "😐", "😑", "😬", "🙄",
            "😯", "😦", "😧", "😮", "😲", "🥱", "😴", "🤤", "😪", "😵", "🤐", "🥴", "🤢", "🤮",
            "🤧", "😷", "🤒", "🤕", "🤑", "🤠", "😈", "👿", "👹", "👺", "🤡", "💩", "👻", "💀",
            "☠️", "👽", "👾", "🤖", "🎃", "❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎",
            "💔", "❣️", "💕", "💞", "💓", "💗", "💖", "💘", "💝", "💟", "✨", "🎉", "🎊", "🔥",
            "🌟", "💫", "💥", "💯", "✅", "⚡", "🌈", "🌙", "🎁", "🎈", "🔴", "🟠", "🟡", "🟢",
            "🔵", "🟣", "⚫", "⚪", "🟤", "🔺", "🔻", "🔸", "🔹", "🔶", "🔷", "💠", "🔘", "🔳",
            "🔲", "🏳️", "🏴", "🏴‍☠️", "🏁", "🚩", "🎌", "🏳️‍🌈"
    };

    private static final String SYMBOLS = "!@#$%^&*()-_=+[]{}|;:'\",.<>/?`~";
    private static final int MAX_LENGTH = 5000;

    private final Handler mainHandler = new Handler(Looper.getMainLooper());


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_random_text);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        SharedPreferences sharedPreferences = getSharedPreferences("nightModePrefs", MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }

        random_MaterialToolbar = findViewById(R.id.random_MaterialToolbar);
        random_EditEnterText = findViewById(R.id.random_editEnterText);
        checkBoxAlphabet = findViewById(R.id.checkBoxAlphabet);
        checkBoxDigit = findViewById(R.id.checkBoxDigit);
        checkBoxEmoji = findViewById(R.id.checkBoxEmoji);
        checkBoxSpecial = findViewById(R.id.checkBoxSpecial);
        checkBoxAscii = findViewById(R.id.checkBoxAscii);
        random_ButtonGenerate = findViewById(R.id.random_ButtonGenerate);
        random_Display = findViewById(R.id.random_Display);
        random_Copy_share_Layout = findViewById(R.id.random_Copy_share_Layout);
        random_LayoutCopy = findViewById(R.id.random_LayoutCopy);
        random_LayoutShare = findViewById(R.id.random_LayoutShare);



        random_MaterialToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(Random_Text_Activity.this, MainActivity.class));
                finish();
            }
        });
        random_MaterialToolbar.setTitleTextAppearance(Random_Text_Activity.this, R.style.RobotoBoldTextAppearance);


        random_MaterialToolbar.setOnMenuItemClickListener(new Toolbar.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {

                if (item.getItemId() == R.id.clear_Data) {
                    random_EditEnterText.setText("");
                    checkBoxAlphabet.setChecked(false);
                    checkBoxDigit.setChecked(false);
                    checkBoxEmoji.setChecked(false);
                    checkBoxSpecial.setChecked(false);
                    checkBoxAscii.setChecked(false);
                    random_Display.setText("");
                    random_Copy_share_Layout.setVisibility(View.GONE);
                }

                return true;
            }
        });


        setupProgressDialog();

        findViewById(R.id.random_ButtonGenerate).setOnClickListener(v -> {
            try {
                if(validateInput()) startGeneration();
            } catch (Exception e) {
                showError("Error: " + e.getMessage());
            }
        });


        random_LayoutCopy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                copyToClipboard(random_Display.getText().toString());
            }
        });

        random_LayoutShare.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                shareText(random_Display.getText().toString());
            }
        });



    }

    @Override
    public void onBackPressed() {

        startActivity(new Intent(Random_Text_Activity.this, MainActivity.class));
        finish();
        super.onBackPressed();
    }




    private void setupProgressDialog() {
        progressDialog = new ProgressDialog(this);
        progressDialog.setTitle("Generating Text");
        progressDialog.setMessage("Please wait...");
        progressDialog.setProgressStyle(ProgressDialog.STYLE_HORIZONTAL);
        progressDialog.setCancelable(false);
        progressDialog.setCanceledOnTouchOutside(false);
    }

    private boolean validateInput() {
        String input = random_EditEnterText.getText().toString().trim();

        if(input.isEmpty()) {
            showError("Please enter a repetition limit");
            return false;
        }

        try {
            int length = Integer.parseInt(input);
            if(length < 1 || length > MAX_LENGTH) {
                showError("Please enter between 1 and " + MAX_LENGTH);
                return false;
            }
        } catch (NumberFormatException e) {
            showError("Invalid number format");
            return false;
        }

        if(!checkBoxAlphabet.isChecked() && !checkBoxDigit.isChecked() &&
                !checkBoxEmoji.isChecked() && !checkBoxSpecial.isChecked() &&
                !checkBoxAscii.isChecked()) {
            showError("Select at least one option");
            return false;
        }

        return true;
    }

    private void startGeneration() {
        progressDialog.show();
        new Thread(() -> {
            try {
                List<String> charPool = buildCharacterPool();
                if(charPool.isEmpty()) {
                    showError("No valid characters selected");
                    return;
                }

                int length = Integer.parseInt(random_EditEnterText.getText().toString().trim());
                progressDialog.setMax(length);

                StringBuilder result = new StringBuilder();
                Random random = new Random();

                for(int i=0; i<length; i++) {
                    result.append(charPool.get(random.nextInt(charPool.size())));
                    updateProgress(i+1);
                }

                showResult(result.toString());

            } catch (Exception e) {
                showError("Generation failed: " + e.getMessage());
            } finally {
                dismissProgress();
            }
        }).start();
    }

    private List<String> buildCharacterPool() {
        List<String> pool = new ArrayList<>(1000);

        if(checkBoxAlphabet.isChecked()) {
            for(char c='A'; c<='Z'; c++) pool.add(String.valueOf(c));
            for(char c='a'; c<='z'; c++) pool.add(String.valueOf(c));
        }

        if(checkBoxDigit.isChecked()) {
            for(char c='0'; c<='9'; c++) pool.add(String.valueOf(c));
        }

        if(checkBoxEmoji.isChecked()) {
            for(String emoji : EMOJIS) pool.add(emoji);
        }

        if(checkBoxSpecial.isChecked()) {
            for(char c : SYMBOLS.toCharArray()) pool.add(String.valueOf(c));
        }

        if(checkBoxAscii.isChecked()) {
            for(int i=32; i<=126; i++) pool.add(String.valueOf((char)i));
        }

        return pool;
    }

    private void updateProgress(int progress) {
        mainHandler.post(() -> progressDialog.setProgress(progress));
    }

    private void showResult(String result) {
        mainHandler.post(() -> {
            random_Display.setText(result);
           // Toast.makeText(this, "Generation complete!", Toast.LENGTH_SHORT).show();
            random_Copy_share_Layout.setVisibility(View.VISIBLE);
        });
    }

    private void showError(String message) {
        mainHandler.post(() -> {
            Toast.makeText(this, message, Toast.LENGTH_LONG).show();
            random_Display.setText("");
        });
    }

    private void dismissProgress() {
        mainHandler.post(() -> {
            if(progressDialog.isShowing()) progressDialog.dismiss();
        });
    }

    @Override
    protected void onDestroy() {
        dismissProgress();
        super.onDestroy();
    }




    private void copyToClipboard(String text) {
        if (text.isEmpty()) {
            Toast.makeText(this, "No text to copy!", Toast.LENGTH_SHORT).show();
            return;
        }

        if (text.length()>100000) {
            Toast.makeText(this, "Text too long to copy (Max 100k characters)", Toast.LENGTH_LONG).show();
            return;
        }

        android.content.ClipboardManager clipboard =
                (android.content.ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
        android.content.ClipData clip = android.content.ClipData.newPlainText("Repeated Text", text);
        clipboard.setPrimaryClip(clip);

        Toast.makeText(this, "Text copied to clipboard!", Toast.LENGTH_SHORT).show();

    }


    private void shareText(String text) {
        if (text.isEmpty()) {
            Toast.makeText(this, "No text to share!", Toast.LENGTH_SHORT).show();
            return;
        }

        if (text.length()>50000){
            Toast.makeText(this, "Text too long to share (Max 50k characters)", Toast.LENGTH_LONG).show();
            return;
        }
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, text);
        startActivity(Intent.createChooser(shareIntent, "Share via"));


    }






}