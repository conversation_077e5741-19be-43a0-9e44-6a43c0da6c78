package com.anginatech.textrepeater;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;

import java.util.List;

public class StylishTextAdapter extends RecyclerView.Adapter<StylishTextAdapter.ViewHolder> {

    private final Context context;
    private final List<String> stylishTexts;

    public StylishTextAdapter(Context context, List<String> stylishTexts) {
        this.context = context;
        this.stylishTexts = stylishTexts;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_stylish_text, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        String text = stylishTexts.get(position);
        holder.textView.setText(text);
        int number = position+1;
        holder.textPositionStyle.setText(""+number+", ");
        holder.itemLayoutStyle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(context);
                View bottomSheetView = LayoutInflater.from(context).inflate(R.layout.sms_item_dailog, null);

                TextView smsItemText = bottomSheetView.findViewById(R.id.smsItemText);
                CardView cardViewCopy = bottomSheetView.findViewById(R.id.cardViewCopy);
                CardView cardViewShare = bottomSheetView.findViewById(R.id.cardViewShare);
                smsItemText.setText(""+text);

                cardViewCopy.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        ClipboardManager clipboard = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
                        ClipData clip = ClipData.newPlainText("Stylish Text", text);
                        clipboard.setPrimaryClip(clip);
                        Toast.makeText(context, "Copied to Clipboard", Toast.LENGTH_SHORT).show();
                        bottomSheetDialog.dismiss();

                    }


                });

                cardViewShare.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Intent shareIntent = new Intent(Intent.ACTION_SEND);
                        shareIntent.setType("text/plain");
                        shareIntent.putExtra(Intent.EXTRA_TEXT, text);
                        context.startActivity(Intent.createChooser(shareIntent, "Share Stylish Text"));
                        bottomSheetDialog.dismiss();

                    }
                });

                bottomSheetDialog.setContentView(bottomSheetView);
                bottomSheetDialog.show();

            }
        });


    }

    @Override
    public int getItemCount() {
        return stylishTexts.size();
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textView,textPositionStyle;
        ConstraintLayout itemLayoutStyle;


        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            textView = itemView.findViewById(R.id.textView);
            textPositionStyle = itemView.findViewById(R.id.textPositionStyle);
            itemLayoutStyle = itemView.findViewById(R.id.itemLayoutStyle);


        }
    }

}