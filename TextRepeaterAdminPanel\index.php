<?php
/**
 * Text Repeater Admin Panel - Index Page
 * Redirects to appropriate page based on authentication status
 */

require_once 'config/database.php';
require_once 'classes/Auth.php';

// Initialize configuration
Config::init();

$auth = new Auth();

// Check if user is logged in
if ($auth->isLoggedIn()) {
    // User is logged in, redirect to dashboard
    header('Location: dashboard.php');
    exit();
} else {
    // User is not logged in, redirect to login page
    header('Location: login.php');
    exit();
}
?>
