<?php
require_once 'config/database.php';
require_once 'classes/Auth.php';
require_once 'classes/TextContentManager.php';

$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$textManager = new TextContentManager();
$user = $auth->getCurrentUser();

// Check if editing existing category
$edit_id = $_GET['id'] ?? 0;
$is_edit = $edit_id > 0;
$category_data = null;

if ($is_edit) {
    $category_data = $textManager->getCategoryById($edit_id);
    if (!$category_data) {
        header('Location: categories.php?error=Category not found');
        exit();
    }
}

$message = '';
$message_type = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $name = trim($_POST['name'] ?? '');
    $display_name = trim($_POST['display_name'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $is_active = isset($_POST['is_active']) ? 1 : 0;
    $sort_order = intval($_POST['sort_order'] ?? 0);
    
    // Validation
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'Category name is required';
    } elseif (!preg_match('/^[a-z0-9_-]+$/', $name)) {
        $errors[] = 'Category name can only contain lowercase letters, numbers, hyphens, and underscores';
    }
    
    if (empty($display_name)) {
        $errors[] = 'Display name is required';
    }
    
    if (empty($errors)) {
        if ($is_edit) {
            $result = $textManager->updateCategory($edit_id, $name, $display_name, $description, $is_active, $sort_order);
        } else {
            $result = $textManager->addCategory($name, $display_name, $description, $is_active, $sort_order);
        }
        
        if ($result['success']) {
            header('Location: categories.php?success=' . urlencode($result['message']));
            exit();
        } else {
            $message = $result['message'];
            $message_type = 'error';
        }
    } else {
        $message = implode('<br>', $errors);
        $message_type = 'error';
    }
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <?php echo $is_edit ? 'Edit Category' : 'Add New Category'; ?>
                    </h3>
                    <a href="categories.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Categories
                    </a>
                </div>
                
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $message_type == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                            <?php echo $message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="name" class="form-label">Category Name *</label>
                                    <input type="text" name="name" id="name" class="form-control" 
                                           value="<?php echo htmlspecialchars($category_data ? $category_data['name'] : ($_POST['name'] ?? '')); ?>" 
                                           required pattern="[a-z0-9_-]+" 
                                           placeholder="e.g., romantic, funny, motivational">
                                    <div class="invalid-feedback">
                                        Please enter a valid category name (lowercase letters, numbers, hyphens, underscores only).
                                    </div>
                                    <div class="form-text">
                                        Used in API calls. Use lowercase letters, numbers, hyphens, and underscores only.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="display_name" class="form-label">Display Name *</label>
                                    <input type="text" name="display_name" id="display_name" class="form-control" 
                                           value="<?php echo htmlspecialchars($category_data ? $category_data['display_name'] : ($_POST['display_name'] ?? '')); ?>" 
                                           required placeholder="e.g., Romantic Messages">
                                    <div class="invalid-feedback">
                                        Please enter a display name.
                                    </div>
                                    <div class="form-text">
                                        Shown in admin panel and forms.
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea name="description" id="description" class="form-control" rows="3" 
                                      placeholder="Brief description of this category..."><?php echo htmlspecialchars($category_data ? $category_data['description'] : ($_POST['description'] ?? '')); ?></textarea>
                            <div class="form-text">
                                Optional description to help identify the category purpose.
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="sort_order" class="form-label">Sort Order</label>
                                    <input type="number" name="sort_order" id="sort_order" class="form-control" 
                                           value="<?php echo $category_data ? $category_data['sort_order'] : ($_POST['sort_order'] ?? 0); ?>" 
                                           min="0" max="999">
                                    <div class="form-text">Lower numbers appear first in lists</div>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <div class="form-check form-switch mt-4">
                                        <input type="checkbox" name="is_active" id="is_active" class="form-check-input" 
                                               <?php echo ($category_data && $category_data['is_active']) || 
                                                         (!$category_data && !isset($_POST['is_active'])) || 
                                                         isset($_POST['is_active']) ? 'checked' : ''; ?>>
                                        <label for="is_active" class="form-check-label">Active</label>
                                    </div>
                                    <div class="form-text">Only active categories appear in content forms and API</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="card">
                                <div class="card-header">
                                    <h6 class="mb-0">Preview</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <strong>API Name:</strong> <code id="preview-name">-</code>
                                        </div>
                                        <div class="col-md-6">
                                            <strong>Display Name:</strong> <span id="preview-display">-</span>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <strong>Description:</strong> <span id="preview-description">-</span>
                                    </div>
                                    <div class="mt-2">
                                        <strong>Status:</strong> 
                                        <span id="preview-status" class="badge bg-success">Active</span>
                                        <strong class="ms-3">Sort Order:</strong> 
                                        <span id="preview-sort">0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <?php if ($is_edit && $category_data): ?>
                            <div class="alert alert-warning">
                                <h6><i class="fas fa-exclamation-triangle"></i> Editing Category</h6>
                                <p class="mb-0">
                                    <strong>Created:</strong> <?php echo date('F j, Y \a\t g:i A', strtotime($category_data['created_at'])); ?><br>
                                    <strong>Last Updated:</strong> <?php echo date('F j, Y \a\t g:i A', strtotime($category_data['updated_at'])); ?>
                                </p>
                            </div>
                        <?php endif; ?>
                        
                        <div class="d-flex justify-content-between">
                            <a href="categories.php" class="btn btn-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 
                                <?php echo $is_edit ? 'Update Category' : 'Create Category'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Live preview
document.addEventListener('DOMContentLoaded', function() {
    const nameInput = document.getElementById('name');
    const displayInput = document.getElementById('display_name');
    const descriptionInput = document.getElementById('description');
    const activeInput = document.getElementById('is_active');
    const sortInput = document.getElementById('sort_order');
    
    const previewName = document.getElementById('preview-name');
    const previewDisplay = document.getElementById('preview-display');
    const previewDescription = document.getElementById('preview-description');
    const previewStatus = document.getElementById('preview-status');
    const previewSort = document.getElementById('preview-sort');
    
    function updatePreview() {
        previewName.textContent = nameInput.value || '-';
        previewDisplay.textContent = displayInput.value || '-';
        previewDescription.textContent = descriptionInput.value || 'No description';
        previewSort.textContent = sortInput.value || '0';
        
        if (activeInput.checked) {
            previewStatus.className = 'badge bg-success';
            previewStatus.textContent = 'Active';
        } else {
            previewStatus.className = 'badge bg-danger';
            previewStatus.textContent = 'Inactive';
        }
    }
    
    // Auto-generate name from display name
    displayInput.addEventListener('input', function() {
        if (!nameInput.value) {
            const autoName = this.value.toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '_')
                .replace(/_+/g, '_')
                .replace(/^_|_$/g, '');
            nameInput.value = autoName;
        }
        updatePreview();
    });
    
    nameInput.addEventListener('input', updatePreview);
    descriptionInput.addEventListener('input', updatePreview);
    activeInput.addEventListener('change', updatePreview);
    sortInput.addEventListener('input', updatePreview);
    
    // Initial update
    updatePreview();
});
</script>

<?php include 'includes/footer.php'; ?>
