{"logs": [{"outputFile": "com.anginatech.textrepeater.app-mergeDebugResources-55:/values-da/values-da.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\446fe71953247653d35c77c031840418\\transformed\\browser-1.8.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,167,266,373", "endColumns": "111,98,106,96", "endOffsets": "162,261,368,465"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6639,6960,7059,7166", "endColumns": "111,98,106,96", "endOffsets": "6746,7054,7161,7258"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\131f10e519d4cb41eac2bb9322c4fa1c\\transformed\\play-services-ads-24.3.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,245,296,351,415,484,591,653,759,859,986,1036,1091,1205,1287,1326,1408,1444,1477,1529,1616,1655", "endColumns": "45,50,54,63,68,106,61,105,99,126,49,54,113,81,38,81,35,32,51,86,38,55", "endOffsets": "244,295,350,414,483,590,652,758,858,985,1035,1090,1204,1286,1325,1407,1443,1476,1528,1615,1654,1710"}, "to": {"startLines": "133,134,135,136,137,138,139,140,141,142,143,144,145,146,148,149,150,151,152,153,154,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11923,11973,12028,12087,12155,12228,12339,12405,12515,12619,12750,12804,12863,12981,13147,13190,13276,13316,13353,13409,13500,13959", "endColumns": "49,54,58,67,72,110,65,109,103,130,53,58,117,85,42,85,39,36,55,90,42,59", "endOffsets": "11968,12023,12082,12150,12223,12334,12400,12510,12614,12745,12799,12858,12976,13062,13185,13271,13311,13348,13404,13495,13538,14014"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6a607bbfcc83cfb280a4c2f3d8ba8b9e\\transformed\\play-services-basement-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "122", "endOffsets": "317"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5432", "endColumns": "126", "endOffsets": "5554"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\a6be65feae4b33c1c7fd18a0c0ae1d8a\\transformed\\appcompat-1.7.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,299,415,500,600,713,791,867,958,1051,1144,1238,1332,1425,1520,1618,1709,1800,1879,1987,2094,2190,2303,2406,2507,2660,2757", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "200,294,410,495,595,708,786,862,953,1046,1139,1233,1327,1420,1515,1613,1704,1795,1874,1982,2089,2185,2298,2401,2502,2655,2752,2832"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "322,422,516,632,717,817,930,1008,1084,1175,1268,1361,1455,1549,1642,1737,1835,1926,2017,2096,2204,2311,2407,2520,2623,2724,2877,13543", "endColumns": "99,93,115,84,99,112,77,75,90,92,92,93,93,92,94,97,90,90,78,107,106,95,112,102,100,152,96,79", "endOffsets": "417,511,627,712,812,925,1003,1079,1170,1263,1356,1450,1544,1637,1732,1830,1921,2012,2091,2199,2306,2402,2515,2618,2719,2872,2969,13618"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\bc7f51ca89ffc0f741ddbaa1d183190a\\transformed\\material-1.12.0\\res\\values-da\\values-da.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,272,350,426,504,601,681,781,930,1008,1067,1131,1217,1290,1350,1437,1501,1563,1625,1693,1758,1812,1930,1988,2049,2105,2180,2306,2392,2469,2560,2644,2724,2865,2943,3023,3145,3231,3309,3365,3416,3482,3550,3624,3695,3770,3842,3920,3990,4063,4167,4251,4328,4416,4505,4579,4652,4737,4786,4864,4930,5010,5093,5155,5219,5282,5351,5459,5562,5663,5762,5822,5877,5957,6037,6115", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "267,345,421,499,596,676,776,925,1003,1062,1126,1212,1285,1345,1432,1496,1558,1620,1688,1753,1807,1925,1983,2044,2100,2175,2301,2387,2464,2555,2639,2719,2860,2938,3018,3140,3226,3304,3360,3411,3477,3545,3619,3690,3765,3837,3915,3985,4058,4162,4246,4323,4411,4500,4574,4647,4732,4781,4859,4925,5005,5088,5150,5214,5277,5346,5454,5557,5658,5757,5817,5872,5952,6032,6110,6187"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,147,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2974,3052,3128,3206,3303,4110,4210,4359,6751,6810,6874,7263,7336,7396,7483,7547,7609,7671,7739,7804,7858,7976,8034,8095,8151,8226,8352,8438,8515,8606,8690,8770,8911,8989,9069,9191,9277,9355,9411,9462,9528,9596,9670,9741,9816,9888,9966,10036,10109,10213,10297,10374,10462,10551,10625,10698,10783,10832,10910,10976,11056,11139,11201,11265,11328,11397,11505,11608,11709,11808,11868,13067,13623,13703,13781", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,147,156,157,158", "endColumns": "12,77,75,77,96,79,99,148,77,58,63,85,72,59,86,63,61,61,67,64,53,117,57,60,55,74,125,85,76,90,83,79,140,77,79,121,85,77,55,50,65,67,73,70,74,71,77,69,72,103,83,76,87,88,73,72,84,48,77,65,79,82,61,63,62,68,107,102,100,98,59,54,79,79,77,76", "endOffsets": "317,3047,3123,3201,3298,3378,4205,4354,4432,6805,6869,6955,7331,7391,7478,7542,7604,7666,7734,7799,7853,7971,8029,8090,8146,8221,8347,8433,8510,8601,8685,8765,8906,8984,9064,9186,9272,9350,9406,9457,9523,9591,9665,9736,9811,9883,9961,10031,10104,10208,10292,10369,10457,10546,10620,10693,10778,10827,10905,10971,11051,11134,11196,11260,11323,11392,11500,11603,11704,11803,11863,11918,13142,13698,13776,13853"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c778fdfe6002d7eef6060ec9b8f3c394\\transformed\\core-1.13.1\\res\\values-da\\values-da.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,253,350,448,555,664,782", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "146,248,345,443,550,659,777,878"}, "to": {"startLines": "38,39,40,41,42,43,44,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3383,3479,3581,3678,3776,3883,3992,13858", "endColumns": "95,101,96,97,106,108,117,100", "endOffsets": "3474,3576,3673,3771,3878,3987,4105,13954"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\917d0d7e0c096b9e28e4e727a3aa98ed\\transformed\\play-services-base-18.5.0\\res\\values-da\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,574,679,818,939,1055,1156,1308,1410,1568,1691,1832,2006,2068,2126", "endColumns": "101,155,122,104,138,120,115,100,151,101,157,122,140,173,61,57,73", "endOffsets": "294,450,573,678,817,938,1054,1155,1307,1409,1567,1690,1831,2005,2067,2125,2199"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4437,4543,4703,4830,4939,5082,5207,5327,5559,5715,5821,5983,6110,6255,6433,6499,6561", "endColumns": "105,159,126,108,142,124,119,104,155,105,161,126,144,177,65,61,77", "endOffsets": "4538,4698,4825,4934,5077,5202,5322,5427,5710,5816,5978,6105,6250,6428,6494,6556,6634"}}]}]}