<?php
/**
 * Get User Details API
 * Returns detailed information about a specific app user
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set JSON header
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Include required files
require_once '../config/database.php';

try {
    // Check if device_id parameter is provided
    if (!isset($_GET['device_id']) || empty($_GET['device_id'])) {
        http_response_code(400);
        echo json_encode([
            'success' => false,
            'error' => 'Device ID is required'
        ]);
        exit();
    }

    $device_id = $_GET['device_id'];

    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();

    if (!$conn) {
        throw new Exception('Database connection failed');
    }

    // Query to get user details
    $query = "
        SELECT
            device_id,
            fcm_token,
            app_version,
            android_version,
            device_model,
            device_brand,
            screen_resolution,
            language,
            timezone,
            total_sessions,
            notifications_enabled,
            created_at,
            last_seen,
            updated_at,
            CASE
                WHEN last_seen >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 'active'
                ELSE 'inactive'
            END as status
        FROM app_users
        WHERE device_id = :device_id
    ";

    $stmt = $conn->prepare($query);
    $stmt->bindParam(':device_id', $device_id);
    $stmt->execute();

    $user = $stmt->fetch();

    if (!$user) {
        http_response_code(404);
        echo json_encode([
            'success' => false,
            'error' => 'User not found'
        ]);
        exit();
    }

    // Get additional statistics for this user
    $stats_query = "
        SELECT
            COUNT(DISTINCT session_id) as total_sessions_logged,
            SUM(CASE WHEN event_type = 'impression' THEN 1 ELSE 0 END) as ad_impressions,
            SUM(CASE WHEN event_type = 'click' THEN 1 ELSE 0 END) as ad_clicks,
            SUM(revenue) as total_revenue
        FROM ad_analytics
        WHERE device_id = :device_id
    ";

    $stats_stmt = $conn->prepare($stats_query);
    $stats_stmt->bindParam(':device_id', $device_id);
    $stats_stmt->execute();
    $stats = $stats_stmt->fetch();

    // Get notification delivery stats
    $notification_stats_query = "
        SELECT
            COUNT(*) as notifications_sent,
            SUM(CASE WHEN status = 'delivered' THEN 1 ELSE 0 END) as notifications_delivered,
            SUM(CASE WHEN clicked_at IS NOT NULL THEN 1 ELSE 0 END) as notifications_clicked
        FROM notification_delivery
        WHERE device_id = :device_id
    ";

    $notification_stats_stmt = $conn->prepare($notification_stats_query);
    $notification_stats_stmt->bindParam(':device_id', $device_id);
    $notification_stats_stmt->execute();
    $notification_stats = $notification_stats_stmt->fetch();

    // Prepare response data
    $response_data = [
        'device_id' => $user['device_id'],
        'fcm_token' => $user['fcm_token'] ? substr($user['fcm_token'], 0, 20) . '...' : 'Not set',
        'app_version' => $user['app_version'] ?: 'Unknown',
        'android_version' => $user['android_version'] ?: 'Unknown',
        'device_model' => $user['device_model'] ?: 'Unknown',
        'device_brand' => $user['device_brand'] ?: 'Unknown',
        'screen_resolution' => $user['screen_resolution'] ?: 'Unknown',
        'language' => $user['language'] ?: 'en',
        'timezone' => $user['timezone'] ?: 'UTC',
        'total_sessions' => (int)$user['total_sessions'],
        'notifications_enabled' => (bool)$user['notifications_enabled'],
        'status' => $user['status'],
        'created_at' => $user['created_at'],
        'last_seen' => $user['last_seen'],
        'updated_at' => $user['updated_at'],
        'statistics' => [
            'sessions_logged' => (int)($stats['total_sessions_logged'] ?: 0),
            'ad_impressions' => (int)($stats['ad_impressions'] ?: 0),
            'ad_clicks' => (int)($stats['ad_clicks'] ?: 0),
            'total_revenue' => (float)($stats['total_revenue'] ?: 0),
            'notifications_sent' => (int)($notification_stats['notifications_sent'] ?: 0),
            'notifications_delivered' => (int)($notification_stats['notifications_delivered'] ?: 0),
            'notifications_clicked' => (int)($notification_stats['notifications_clicked'] ?: 0)
        ]
    ];

    // Return success response
    echo json_encode([
        'success' => true,
        'data' => $response_data
    ]);

} catch (Exception $e) {
    // Log error
    error_log("Get user API error: " . $e->getMessage());

    // Return error response
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error: ' . $e->getMessage()
    ]);
}
?>
