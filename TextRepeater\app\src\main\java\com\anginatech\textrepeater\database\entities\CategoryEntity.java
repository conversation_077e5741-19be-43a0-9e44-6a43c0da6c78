package com.anginatech.textrepeater.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.annotation.NonNull;

/**
 * Room Entity for Categories
 * Replaces the old SQLite category table with modern Room database
 */
@Entity(
    tableName = "categories",
    indices = {
        @Index(value = "name", unique = true),
        @Index(value = "sort_order")
    }
)
public class CategoryEntity {
    
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "id")
    private int id;
    
    @NonNull
    @ColumnInfo(name = "name")
    private String name;
    
    @NonNull
    @ColumnInfo(name = "display_name")
    private String displayName;
    
    @ColumnInfo(name = "description")
    private String description;
    
    @ColumnInfo(name = "sort_order")
    private int sortOrder;
    
    @ColumnInfo(name = "is_active")
    private boolean isActive;
    
    @ColumnInfo(name = "created_at")
    private long createdAt;
    
    @ColumnInfo(name = "updated_at")
    private long updatedAt;
    
    // Constructors
    public CategoryEntity() {
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
        this.isActive = true;
    }
    
    public CategoryEntity(@NonNull String name, @NonNull String displayName, String description, int sortOrder) {
        this();
        this.name = name;
        this.displayName = displayName;
        this.description = description;
        this.sortOrder = sortOrder;
    }
    
    // Getters and Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    @NonNull
    public String getName() {
        return name;
    }
    
    public void setName(@NonNull String name) {
        this.name = name;
        this.updatedAt = System.currentTimeMillis();
    }
    
    @NonNull
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(@NonNull String displayName) {
        this.displayName = displayName;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public int getSortOrder() {
        return sortOrder;
    }
    
    public void setSortOrder(int sortOrder) {
        this.sortOrder = sortOrder;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public boolean isActive() {
        return isActive;
    }
    
    public void setActive(boolean active) {
        isActive = active;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public long getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    /**
     * Get emoji for this category (for UI display)
     */
    public String getEmoji() {
        switch (name.toLowerCase()) {
            case "romantic":
            case "romantics":
                return "🥰";
            case "sad":
                return "🥲";
            case "funny":
                return "😄";
            case "motivational":
            case "motivation":
                return "💪";
            case "love":
                return "❤️";
            case "friendship":
                return "👫";
            case "inspirational":
            case "inspiration":
                return "✨";
            case "birthday":
                return "🎂";
            case "good morning":
            case "morning":
                return "🌅";
            case "good night":
            case "night":
                return "🌙";
            default:
                return "💬";
        }
    }
    
    /**
     * Get display name with emoji
     */
    public String getDisplayNameWithEmoji() {
        return getEmoji() + " " + displayName;
    }
    
    /**
     * Get compact display name for tabs
     */
    public String getCompactDisplayName() {
        String shortName = displayName;
        if (displayName.length() > 15) {
            switch (name.toLowerCase()) {
                case "motivational":
                case "motivation":
                    shortName = "Motivational";
                    break;
                case "friendship":
                    shortName = "Friendship";
                    break;
                case "romantic":
                case "romantics":
                    shortName = "Romantic";
                    break;
                default:
                    if (displayName.contains(" ")) {
                        String[] words = displayName.split(" ");
                        shortName = words[0];
                    } else {
                        shortName = displayName.length() > 12 ?
                            displayName.substring(0, 12) + "..." : displayName;
                    }
                    break;
            }
        }
        return getEmoji() + " " + shortName;
    }
    
    @Override
    public String toString() {
        return "CategoryEntity{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", displayName='" + displayName + '\'' +
                ", description='" + description + '\'' +
                ", sortOrder=" + sortOrder +
                ", isActive=" + isActive +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                '}';
    }
}
