package com.anginatech.textrepeater;

import static android.app.Activity.RESULT_CANCELED;

import android.app.Activity;
import android.content.Context;
import android.content.IntentSender;
import android.util.Log;

import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.snackbar.Snackbar;
import com.google.android.play.core.appupdate.AppUpdateManager;
import com.google.android.play.core.appupdate.AppUpdateManagerFactory;
import com.google.android.play.core.install.InstallStateUpdatedListener;
import com.google.android.play.core.install.model.AppUpdateType;
import com.google.android.play.core.install.model.InstallStatus;
import com.google.android.play.core.install.model.UpdateAvailability;

public class InAppUpdate {

    private static final String TAG = "InAppUpdate";

    private final Activity parentActivity;

    private final AppUpdateManager appUpdateManager;

    private final int appUpdateType = AppUpdateType.FLEXIBLE;
    private final int MY_REQUEST_CODE = 500;

    public InAppUpdate(Activity activity) {
        this.parentActivity = activity;
        appUpdateManager = AppUpdateManagerFactory.create(parentActivity);
    }

    InstallStateUpdatedListener stateUpdatedListener = installState -> {
        Log.d(TAG, "Install state updated: " + installState.installStatus());
        if (installState.installStatus() == InstallStatus.DOWNLOADED) {
            popupSnackBarForCompleteUpdate();
        }
    };

    public void checkForAppUpdate() {
        Log.d(TAG, "Checking for app update");
        appUpdateManager.getAppUpdateInfo().addOnSuccessListener(info -> {
            boolean isUpdateAvailable = info.updateAvailability() == UpdateAvailability.UPDATE_AVAILABLE;
            boolean isUpdateAllowed = info.isUpdateTypeAllowed(appUpdateType);

            Log.d(TAG, "Update available: " + isUpdateAvailable);
            Log.d(TAG, "Update allowed: " + isUpdateAllowed);

            if (isUpdateAvailable && isUpdateAllowed) {
                try {
                    appUpdateManager.startUpdateFlowForResult(
                            info,
                            appUpdateType,
                            parentActivity,
                            MY_REQUEST_CODE
                    );
                    Log.d(TAG, "Update flow started");
                } catch (IntentSender.SendIntentException e) {
                    Log.e(TAG, "Error starting update flow", e);
                }
            }
        });
        appUpdateManager.registerListener(stateUpdatedListener);
    }

    public void onActivityResult(int requestCode, int resultCode) {
        Log.d(TAG, "onActivityResult: requestCode=" + requestCode + ", resultCode=" + resultCode);
        if (requestCode == MY_REQUEST_CODE) {
            if (resultCode == RESULT_CANCELED) {
                showToast(parentActivity.getApplicationContext(), "Update canceled by user");
            } else if (resultCode != AppCompatActivity.RESULT_OK) {
                checkForAppUpdate();
            }
        }
    }

    private void popupSnackBarForCompleteUpdate() {
        Log.d(TAG, "Update downloaded, showing snackbar for complete update");
        Snackbar snackbar = Snackbar.make(
                parentActivity.findViewById(android.R.id.content),
                "An update has been downloaded and will be installed when the app restarts",
                Snackbar.LENGTH_INDEFINITE
        );
        snackbar.setAction("RESTART", view -> {
            Log.d(TAG, "Snackbar action clicked, completing update");
            appUpdateManager.completeUpdate();
        });
        snackbar.show();
    }

    public void onResume() {
        Log.d(TAG, "onResume");
        appUpdateManager.getAppUpdateInfo().addOnSuccessListener(appUpdateInfo -> {
            if (appUpdateInfo.installStatus() == InstallStatus.DOWNLOADED) {
                popupSnackBarForCompleteUpdate();
            }
        });
    }

    public void onDestroy() {
        Log.d(TAG, "onDestroy");
        appUpdateManager.unregisterListener(stateUpdatedListener);
    }

    private void showToast(Context context, String message) {
        Log.d(TAG, "Showing toast: " + message);
        Snackbar.make(parentActivity.findViewById(android.R.id.content), message, Snackbar.LENGTH_SHORT).show();
    }
}
