<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Message_Activity"
    android:background="@color/mother_layout_color"
    >


    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/messages_materialToolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:navigationIcon="@drawable/baseline_arrow_back_ios_24"
        app:navigationIconTint="@color/toolbar_text_color"
        app:titleTextColor="@color/toolbar_text_color"
        app:title="Messages" />


    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/mother_layout_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/messages_materialToolbar"
        app:layout_constraintVertical_bias="0.0"
        app:tabIndicatorColor="@color/love"
        app:tabIndicatorHeight="3dp"
        tools:layout_editor_absoluteX="0dp"
        app:tabTextColor="@color/primary_text"
        app:tabSelectedTextColor="@color/love"
        app:tabIndicatorGravity="bottom"
        app:tabPaddingStart="12dp"
        app:tabPaddingEnd="12dp"
        app:tabPaddingTop="12dp"
        app:tabPaddingBottom="12dp"
        app:tabTextAppearance="@style/MyTabLayoutTextAppearance"
        app:tabMode="scrollable"
        app:tabGravity="start"
        app:tabMinWidth="120dp"
        app:tabMaxWidth="200dp"
        app:tabContentStart="16dp"
        >

    </com.google.android.material.tabs.TabLayout>


    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPager"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout"
        app:layout_constraintVertical_bias="0.071"

        />




</androidx.constraintlayout.widget.ConstraintLayout>