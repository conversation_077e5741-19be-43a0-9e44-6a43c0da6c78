<?php
/**
 * Dynamic JSON API for Text Repeater App
 * Replaces the static funny_text.json file
 * URL: https://yourdomain.com/TextRepeaterAdminPanel/api/funny_text.json.php
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

// Include required files
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/TextContentManager.php';

try {
    $textManager = new TextContentManager();
    $content = $textManager->getContentForAPI();

    // Return JSON response
    echo json_encode($content, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // Log error and return empty structure
    error_log("Error in funny_text.json API: " . $e->getMessage());

    // Return empty structure to prevent app crashes
    $empty_response = [
        'romantic' => [],
        'sad' => [],
        'funny' => []
    ];

    echo json_encode($empty_response, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
}
?>
