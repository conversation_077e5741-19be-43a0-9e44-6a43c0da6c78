package com.anginatech.textrepeater;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.anginatech.textrepeater.models.Category;

import java.util.ArrayList;
import java.util.List;

/**
 * Dynamic ViewPager adapter that works with any number of categories
 */
public class DynamicViewPagerAdapter extends FragmentStateAdapter {

    private List<Category> categories;
    private boolean useLegacyDb;

    public DynamicViewPagerAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
        this.categories = new ArrayList<>();
        this.useLegacyDb = false;
    }

    public DynamicViewPagerAdapter(@NonNull FragmentActivity fragmentActivity, boolean useLegacyDb) {
        super(fragmentActivity);
        this.categories = new ArrayList<>();
        this.useLegacyDb = useLegacyDb;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        if (position < categories.size()) {
            Category category = categories.get(position);
            return DynamicCategoryFragment.newInstance(category, useLegacyDb);
        }
        
        // Fallback - should not happen
        Category defaultCategory = new Category(0, "default", "Default", "Default category", 0);
        return DynamicCategoryFragment.newInstance(defaultCategory, useLegacyDb);
    }

    @Override
    public int getItemCount() {
        return categories.size();
    }

    /**
     * Update categories and refresh adapter
     */
    public void updateCategories(List<Category> newCategories) {
        this.categories.clear();
        this.categories.addAll(newCategories);
        notifyDataSetChanged();
    }

    /**
     * Add a single category
     */
    public void addCategory(Category category) {
        this.categories.add(category);
        notifyItemInserted(categories.size() - 1);
    }

    /**
     * Get category at position
     */
    public Category getCategoryAt(int position) {
        if (position >= 0 && position < categories.size()) {
            return categories.get(position);
        }
        return null;
    }

    /**
     * Get all categories
     */
    public List<Category> getCategories() {
        return new ArrayList<>(categories);
    }

    /**
     * Clear all categories
     */
    public void clearCategories() {
        int size = categories.size();
        categories.clear();
        notifyItemRangeRemoved(0, size);
    }

    /**
     * Check if adapter has categories
     */
    public boolean hasCategories() {
        return !categories.isEmpty();
    }

    /**
     * Set whether to use legacy database
     */
    public void setUseLegacyDb(boolean useLegacyDb) {
        this.useLegacyDb = useLegacyDb;
    }

    /**
     * Get position of category by name
     */
    public int getCategoryPosition(String categoryName) {
        for (int i = 0; i < categories.size(); i++) {
            if (categories.get(i).getName().equals(categoryName)) {
                return i;
            }
        }
        return -1;
    }
}
