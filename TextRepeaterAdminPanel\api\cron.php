<?php
/**
 * Cron API Endpoint
 * Simple API endpoint for external cron services to process scheduled notifications
 * 
 * Usage:
 * GET /api/cron.php?action=process_notifications
 * GET /api/cron.php?action=process_notifications&key=your_secret_key
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Include required files
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/NotificationManager.php';

// Optional security key (set this in your config if you want to secure the endpoint)
$CRON_SECRET_KEY = 'textrepeater_cron_2024'; // Change this to a secure random string

/**
 * Send JSON response
 */
function sendResponse($data, $status_code = 200) {
    http_response_code($status_code);
    echo json_encode($data, JSON_PRETTY_PRINT);
    exit;
}

/**
 * Log cron activity
 */
function logCronActivity($message) {
    $log_file = __DIR__ . '/../cron/cron-api.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
}

try {
    // Get request parameters
    $action = $_GET['action'] ?? '';
    $provided_key = $_GET['key'] ?? '';
    
    // Optional key validation (uncomment if you want to secure the endpoint)
    /*
    if (!empty($CRON_SECRET_KEY) && $provided_key !== $CRON_SECRET_KEY) {
        logCronActivity("Unauthorized access attempt with key: $provided_key");
        sendResponse([
            'success' => false,
            'message' => 'Unauthorized access. Invalid or missing key.',
            'timestamp' => date('Y-m-d H:i:s')
        ], 401);
    }
    */
    
    switch ($action) {
        case 'process_notifications':
            logCronActivity("Processing scheduled notifications via API");
            
            $notificationManager = new NotificationManager();
            $scheduledNotifications = $notificationManager->getScheduledNotifications();
            
            if (empty($scheduledNotifications)) {
                logCronActivity("No scheduled notifications found to process");
                sendResponse([
                    'success' => true,
                    'message' => 'No scheduled notifications to process',
                    'processed' => 0,
                    'timestamp' => date('Y-m-d H:i:s')
                ]);
            }
            
            $processed = 0;
            $successful = 0;
            $failed = 0;
            $results = [];
            
            foreach ($scheduledNotifications as $notification) {
                $notificationId = $notification['id'];
                $title = $notification['title'];
                
                try {
                    $result = $notificationManager->sendNotification($notificationId);
                    
                    if ($result['success']) {
                        $successful++;
                        logCronActivity("Successfully sent notification ID: $notificationId to " . $result['sent_count'] . " users");
                        
                        $results[] = [
                            'id' => $notificationId,
                            'title' => $title,
                            'status' => 'sent',
                            'sent_count' => $result['sent_count'],
                            'failed_count' => $result['failed_count'] ?? 0
                        ];
                    } else {
                        $failed++;
                        logCronActivity("Failed to send notification ID: $notificationId - " . $result['message']);
                        
                        $results[] = [
                            'id' => $notificationId,
                            'title' => $title,
                            'status' => 'failed',
                            'error' => $result['message']
                        ];
                    }
                    
                } catch (Exception $e) {
                    $failed++;
                    $errorMessage = "Exception sending notification ID: $notificationId - " . $e->getMessage();
                    logCronActivity($errorMessage);
                    
                    $results[] = [
                        'id' => $notificationId,
                        'title' => $title,
                        'status' => 'error',
                        'error' => $errorMessage
                    ];
                }
                
                $processed++;
            }
            
            logCronActivity("Processing complete: $processed processed, $successful successful, $failed failed");
            
            sendResponse([
                'success' => true,
                'message' => "Processed $processed scheduled notifications",
                'processed' => $processed,
                'successful' => $successful,
                'failed' => $failed,
                'results' => $results,
                'timestamp' => date('Y-m-d H:i:s')
            ]);
            break;
            
        case 'status':
            // Get status information
            $notificationManager = new NotificationManager();
            $scheduledNotifications = $notificationManager->getScheduledNotifications();
            
            // Get total scheduled notifications
            $database = new Database();
            $conn = $database->getConnection();
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM notifications WHERE status = 'scheduled'");
            $stmt->execute();
            $totalScheduled = $stmt->fetch()['count'];
            
            sendResponse([
                'success' => true,
                'status' => 'online',
                'current_time' => date('Y-m-d H:i:s'),
                'due_now' => count($scheduledNotifications),
                'total_scheduled' => $totalScheduled,
                'endpoint_url' => 'http://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'],
                'available_actions' => [
                    'process_notifications' => 'Process all due scheduled notifications',
                    'status' => 'Get current status information'
                ]
            ]);
            break;
            
        case '':
        default:
            sendResponse([
                'success' => false,
                'message' => 'No action specified. Available actions: process_notifications, status',
                'usage' => [
                    'process_notifications' => $_SERVER['REQUEST_URI'] . '?action=process_notifications',
                    'status' => $_SERVER['REQUEST_URI'] . '?action=status'
                ],
                'timestamp' => date('Y-m-d H:i:s')
            ], 400);
            break;
    }
    
} catch (Exception $e) {
    $errorMessage = "Fatal error in cron API: " . $e->getMessage();
    logCronActivity($errorMessage);
    
    sendResponse([
        'success' => false,
        'message' => $errorMessage,
        'timestamp' => date('Y-m-d H:i:s')
    ], 500);
}
?>
