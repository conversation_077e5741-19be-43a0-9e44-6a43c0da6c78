package com.anginatech.textrepeater;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.android.volley.DefaultRetryPolicy;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;

import org.json.JSONException;
import org.json.JSONObject;

/**
 * API Client for Text Repeater App
 * Handles all API communications with the admin panel
 */
public class ApiClient {
    private static final String TAG = "ApiClient";
    private static ApiClient instance;
    private RequestQueue requestQueue;
    private Context context;
    
    private ApiClient(Context context) {
        this.context = context.getApplicationContext();
        this.requestQueue = Volley.newRequestQueue(this.context);
    }
    
    public static synchronized ApiClient getInstance(Context context) {
        if (instance == null) {
            instance = new ApiClient(context);
        }
        return instance;
    }
    
    /**
     * Fetch app configuration from server
     */
    public void fetchAppConfig(ConfigCallback callback) {
        String url = Config.buildApiUrl(Config.ENDPOINT_CONFIG);
        
        JsonObjectRequest request = new JsonObjectRequest(
            Request.Method.GET, url, null,
            response -> {
                try {
                    if (response.has("success") && response.getBoolean("success")) {
                        JSONObject data = response.getJSONObject("data");
                        saveAppConfig(data);
                        callback.onSuccess(data);
                    } else {
                        String message = response.optString("message", "Unknown error");
                        callback.onError("API Error: " + message);
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing app config: " + e.getMessage());
                    callback.onError("Parse error: " + e.getMessage());
                }
            },
            error -> {
                Log.e(TAG, "Error fetching app config: " + error.getMessage());
                callback.onError("Network error: " + error.getMessage());
            }
        );
        
        request.setRetryPolicy(new DefaultRetryPolicy(
            Config.API_TIMEOUT,
            Config.API_RETRY_COUNT,
            DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
        ));
        
        requestQueue.add(request);
    }
    
    /**
     * Fetch AdMob configuration from server
     */
    public void fetchAdMobConfig(AdMobConfigCallback callback) {
        String url = Config.buildApiUrl(Config.ENDPOINT_ADS_CONFIG);
        
        JsonObjectRequest request = new JsonObjectRequest(
            Request.Method.GET, url, null,
            response -> {
                try {
                    if (response.has("success") && response.getBoolean("success")) {
                        JSONObject data = response.getJSONObject("data");
                        saveAdMobConfig(data);
                        callback.onSuccess(data);
                    } else {
                        String message = response.optString("message", "No active AdMob configuration");
                        callback.onError("AdMob Config Error: " + message);
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing AdMob config: " + e.getMessage());
                    callback.onError("Parse error: " + e.getMessage());
                }
            },
            error -> {
                Log.e(TAG, "Error fetching AdMob config: " + error.getMessage());
                callback.onError("Network error: " + error.getMessage());
            }
        );
        
        request.setRetryPolicy(new DefaultRetryPolicy(
            Config.API_TIMEOUT,
            Config.API_RETRY_COUNT,
            DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
        ));
        
        requestQueue.add(request);
    }
    
    /**
     * Fetch app settings from server
     */
    public void fetchAppSettings(SettingsCallback callback) {
        String url = Config.buildApiUrl(Config.ENDPOINT_SETTINGS);
        
        JsonObjectRequest request = new JsonObjectRequest(
            Request.Method.GET, url, null,
            response -> {
                try {
                    if (response.has("success") && response.getBoolean("success")) {
                        JSONObject data = response.getJSONObject("data");
                        saveAppSettings(data);
                        callback.onSuccess(data);
                    } else {
                        String message = response.optString("message", "Unknown error");
                        callback.onError("Settings Error: " + message);
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing app settings: " + e.getMessage());
                    callback.onError("Parse error: " + e.getMessage());
                }
            },
            error -> {
                Log.e(TAG, "Error fetching app settings: " + error.getMessage());
                callback.onError("Network error: " + error.getMessage());
            }
        );
        
        request.setRetryPolicy(new DefaultRetryPolicy(
            Config.API_TIMEOUT,
            Config.API_RETRY_COUNT,
            DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
        ));
        
        requestQueue.add(request);
    }
    
    /**
     * Check maintenance status
     */
    public void checkMaintenanceStatus(MaintenanceCallback callback) {
        String url = Config.buildApiUrl(Config.ENDPOINT_MAINTENANCE);
        
        JsonObjectRequest request = new JsonObjectRequest(
            Request.Method.GET, url, null,
            response -> {
                try {
                    if (response.has("success") && response.getBoolean("success")) {
                        JSONObject data = response.getJSONObject("data");
                        boolean maintenanceMode = data.optBoolean("maintenance_mode", false);
                        String message = data.optString("message", "");
                        callback.onResult(maintenanceMode, message);
                    } else {
                        callback.onResult(false, "");
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing maintenance status: " + e.getMessage());
                    callback.onResult(false, "");
                }
            },
            error -> {
                Log.e(TAG, "Error checking maintenance status: " + error.getMessage());
                callback.onResult(false, "");
            }
        );
        
        requestQueue.add(request);
    }
    
    /**
     * Save app configuration to SharedPreferences
     */
    private void saveAppConfig(JSONObject config) {
        SharedPreferences prefs = context.getSharedPreferences(Config.PREFS_APP_CONFIG, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        try {
            if (config.has("app")) {
                JSONObject app = config.getJSONObject("app");
                editor.putString("app_name", app.optString("name", Config.APP_NAME));
                editor.putString("app_version", app.optString("version", Config.APP_VERSION));
                editor.putString("min_version", app.optString("min_version", "1.0.0"));
            }
            
            if (config.has("features")) {
                JSONObject features = config.getJSONObject("features");
                editor.putBoolean(Config.PREF_ADMOB_ENABLED, features.optBoolean("admob_enabled", Config.DEFAULT_ADMOB_ENABLED));
            }
            
            editor.putLong("config_last_updated", System.currentTimeMillis());
            editor.apply();
            
        } catch (JSONException e) {
            Log.e(TAG, "Error saving app config: " + e.getMessage());
        }
    }
    
    /**
     * Save AdMob configuration to SharedPreferences
     */
    private void saveAdMobConfig(JSONObject config) {
        SharedPreferences prefs = context.getSharedPreferences(Config.PREFS_ADMOB_CONFIG, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        editor.putString(Config.PREF_BANNER_AD_ID, config.optString("banner_id", ""));
        editor.putString(Config.PREF_INTERSTITIAL_AD_ID, config.optString("interstitial_id", ""));
        editor.putString(Config.PREF_APP_OPEN_AD_ID, config.optString("app_open_id", ""));
        editor.putInt(Config.PREF_AD_FREQUENCY, config.optInt("ad_frequency_minutes", Config.DEFAULT_AD_FREQUENCY));
        editor.putInt(Config.PREF_MAX_ADS_PER_SESSION, config.optInt("max_ads_per_session", Config.DEFAULT_MAX_ADS_PER_SESSION));
        editor.putBoolean(Config.PREF_TEST_MODE, config.optInt("test_mode", 0) == 1);
        editor.putBoolean(Config.PREF_ADMOB_ENABLED, config.optInt("is_active", 1) == 1);
        editor.putLong("admob_config_last_updated", System.currentTimeMillis());
        editor.apply();
        
        Log.d(TAG, "AdMob config saved - App Open ID: " + config.optString("app_open_id", ""));
    }
    
    /**
     * Save app settings to SharedPreferences
     */
    private void saveAppSettings(JSONObject settings) {
        SharedPreferences prefs = context.getSharedPreferences(Config.PREFS_APP_CONFIG, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        
        // Save individual settings
        for (String key : new String[]{"app_version", "maintenance_mode", "force_update", "min_supported_version"}) {
            if (settings.has(key)) {
                try {
                    JSONObject setting = settings.getJSONObject(key);
                    String type = setting.optString("type", "string");
                    
                    switch (type) {
                        case "boolean":
                            editor.putBoolean(key, setting.optBoolean("value", false));
                            break;
                        case "integer":
                            editor.putInt(key, setting.optInt("value", 0));
                            break;
                        default:
                            editor.putString(key, setting.optString("value", ""));
                            break;
                    }
                } catch (JSONException e) {
                    Log.e(TAG, "Error parsing setting: " + key);
                }
            }
        }
        
        editor.putLong("settings_last_updated", System.currentTimeMillis());
        editor.apply();
    }
    
    // Callback interfaces
    public interface ConfigCallback {
        void onSuccess(JSONObject config);
        void onError(String error);
    }
    
    public interface AdMobConfigCallback {
        void onSuccess(JSONObject config);
        void onError(String error);
    }
    
    public interface SettingsCallback {
        void onSuccess(JSONObject settings);
        void onError(String error);
    }
    
    public interface MaintenanceCallback {
        void onResult(boolean isMaintenanceMode, String message);
    }
}
