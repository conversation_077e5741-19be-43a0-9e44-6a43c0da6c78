package com.anginatech.textrepeater;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;

import java.util.List;

public class EmojiAdapter extends RecyclerView.Adapter<EmojiAdapter.EmojiViewHolder> {

    private List<String> emojiList;
    private Context context;

    public EmojiAdapter(Context context, List<String> emojiList) {
        this.context = context;
        this.emojiList = emojiList;
    }

    @NonNull
    @Override
    public EmojiViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.emoji_art_layout, parent, false);
        return new EmojiViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull EmojiViewHolder holder, int position) {

        int positionText = position+1;

        String emojiArt = emojiList.get(position);
        holder.textView.setText(emojiArt);
        holder.textPosition.setText(""+positionText);

        holder.itemLayout.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(context);
                View bottomSheetView = LayoutInflater.from(context).inflate(R.layout.sms_item_dailog, null);

                TextView smsItemText = bottomSheetView.findViewById(R.id.smsItemText);
                CardView cardViewCopy = bottomSheetView.findViewById(R.id.cardViewCopy);
                CardView cardViewShare = bottomSheetView.findViewById(R.id.cardViewShare);
                smsItemText.setText(""+emojiArt);

                cardViewCopy.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        ClipboardManager clipboard = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
                        ClipData clip = ClipData.newPlainText("Copied Text", emojiArt);
                        clipboard.setPrimaryClip(clip);

                        Toast.makeText(context, "Text copied to clipboard!", Toast.LENGTH_SHORT).show();
                        bottomSheetDialog.dismiss();

                    }


                });

                cardViewShare.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Intent shareIntent = new Intent(Intent.ACTION_SEND);
                        shareIntent.setType("text/plain");
                        shareIntent.putExtra(Intent.EXTRA_TEXT, emojiArt);
                        context.startActivity(Intent.createChooser(shareIntent, "Share SMS via"));
                        bottomSheetDialog.dismiss();

                    }
                });

                bottomSheetDialog.setContentView(bottomSheetView);
                bottomSheetDialog.show();

            }
        });

    }

    @Override
    public int getItemCount() {
        return emojiList.size();
    }

    static class EmojiViewHolder extends RecyclerView.ViewHolder {
        TextView textView,textPosition;
        ConstraintLayout itemLayout;

        public EmojiViewHolder(@NonNull View itemView) {
            super(itemView);
            textView = itemView.findViewById(R.id.text1);
            textPosition = itemView.findViewById(R.id.textPosition);
            itemLayout = itemView.findViewById(R.id.smsItemClick);
        }
    }
}
