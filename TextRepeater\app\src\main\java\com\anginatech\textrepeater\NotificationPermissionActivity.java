package com.anginatech.textrepeater;

import android.os.Bundle;
import android.util.Log;
import android.widget.Button;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Example Activity showing how to integrate notification permission handling
 * You can integrate this code into your existing activities
 */
public class NotificationPermissionActivity extends AppCompatActivity {

    private static final String TAG = "NotificationPermission";
    private TextView statusText;
    private Button requestButton;
    private Button settingsButton;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // Use the dedicated notification permission layout
        setContentView(R.layout.activity_notification_permission);

        initViews();
        updatePermissionStatus();

        // Check and request notification permission on app start
        MyFirebaseMessagingService.checkAndRequestNotificationPermission(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        updatePermissionStatus();
    }

    private void initViews() {
        statusText = findViewById(R.id.status_text);
        requestButton = findViewById(R.id.request_button);
        settingsButton = findViewById(R.id.settings_button);

        requestButton.setOnClickListener(v -> {
            Log.d(TAG, "Request button clicked");
            NotificationPermissionHelper.requestNotificationPermission(this);
        });

        settingsButton.setOnClickListener(v -> {
            Log.d(TAG, "Settings button clicked");
            NotificationPermissionHelper.openNotificationSettings(this);
        });
    }

    private void updatePermissionStatus() {
        String status = NotificationPermissionHelper.getPermissionStatusSummary(this);
        statusText.setText("Status: " + status);

        boolean isGranted = NotificationPermissionHelper.isNotificationPermissionGranted(this);
        requestButton.setEnabled(!isGranted && NotificationPermissionHelper.shouldRequestPermission(this));

        Log.d(TAG, "Permission status updated: " + status);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        // Handle notification permission result
        NotificationPermissionHelper.handlePermissionResult(this, requestCode, permissions, grantResults);

        // Update UI
        updatePermissionStatus();
    }


}

/**
 * INTEGRATION GUIDE:
 *
 * 1. Add to your existing Activity's onCreate():
 *    MyFirebaseMessagingService.checkAndRequestNotificationPermission(this);
 *
 * 2. Add to your existing Activity's onRequestPermissionsResult():
 *    NotificationPermissionHelper.handlePermissionResult(this, requestCode, permissions, grantResults);
 *
 * 3. Optional - Add permission status check in onResume():
 *    boolean isGranted = NotificationPermissionHelper.isNotificationPermissionGranted(this);
 *    // Update UI based on permission status
 *
 * 4. Optional - Add settings button to open notification settings:
 *    NotificationPermissionHelper.openNotificationSettings(this);
 *
 * EXAMPLE INTEGRATION IN MainActivity:
 *
 * @Override
 * protected void onCreate(Bundle savedInstanceState) {
 *     super.onCreate(savedInstanceState);
 *     setContentView(R.layout.activity_main);
 *
 *     // Your existing code...
 *
 *     // Add notification permission check
 *     MyFirebaseMessagingService.checkAndRequestNotificationPermission(this);
 * }
 *
 * @Override
 * public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
 *     super.onRequestPermissionsResult(requestCode, permissions, grantResults);
 *     NotificationPermissionHelper.handlePermissionResult(this, requestCode, permissions, grantResults);
 * }
 */
