<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.VIBRATE" />
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />

    <!-- Notification permission for Android 13+ (API 33+) -->
    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />

    <supports-screens
        android:anyDensity="true"
        android:resizeable="true"
        />

    <application
        android:name=".MyApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.TextRepeater"
        android:usesCleartextTraffic="true"
        tools:targetApi="34">
        <activity
            android:name=".Decoration_Text_Activity"
            android:exported="false"
            android:configChanges="uiMode|screenSize|orientation"
            android:windowSoftInputMode="adjustPan"
            />
        <activity
            android:name=".Stylish_Font_Activity"
            android:configChanges="uiMode|screenSize|orientation"
            android:exported="false"
            android:windowSoftInputMode="adjustPan"
            />
        <activity
            android:name=".Text_to_Imoji_Activity"
            android:configChanges="uiMode|screenSize|orientation"
            android:exported="false"
            android:windowSoftInputMode="adjustPan"
            />
        <activity
            android:name=".Blank_Text_Activity"
            android:configChanges="uiMode|screenSize|orientation"
            android:exported="false"
            android:windowSoftInputMode="adjustPan"
            />
        <activity
            android:name=".Emoji_Art"
            android:configChanges="uiMode|screenSize|orientation"
            android:exported="false"
            android:windowSoftInputMode="adjustPan"
            />
        <activity
            android:name=".Text_Repeat"
            android:configChanges="uiMode|screenSize|orientation"
            android:exported="false"
            android:windowSoftInputMode="adjustPan"
            />
        <activity
            android:name=".Random_Text_Activity"
            android:configChanges="uiMode|screenSize|orientation"
            android:exported="false"
            android:windowSoftInputMode="adjustPan"
            />
        <activity
            android:name=".Message_Activity"
            android:configChanges="uiMode|screenSize|orientation"
            android:exported="false"
            android:windowSoftInputMode="adjustPan"
            />
        <activity
            android:name=".Settings_Activity"
            android:configChanges="uiMode|screenSize|orientation"
            android:exported="false"
            android:windowSoftInputMode="adjustPan"
            />
        <activity
            android:name=".Navigation_Activity"
            android:configChanges="uiMode|screenSize|orientation"
            android:exported="false"
            android:windowSoftInputMode="adjustPan"
            />
        <activity
            android:name=".Splash_Screen"
            android:configChanges="uiMode|screenSize|orientation"
            android:exported="true"
            android:windowSoftInputMode="adjustPan"
            >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".MainActivity"
            android:configChanges="uiMode|screenSize|orientation"
            android:exported="true"
            android:windowSoftInputMode="adjustPan"
            />


        <meta-data
            android:name="preloaded_fonts"
            android:resource="@array/preloaded_fonts" />
        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="@string/ADMOB_APP_ID"/>

        <property
            android:name="android.adservices.AD_SERVICES_CONFIG"
            android:resource="@xml/gma_ad_services_config"
            tools:replace="android:resource"
            />

        <!-- Firebase Cloud Messaging Service -->
        <service
            android:name=".MyFirebaseMessagingService"
            android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>

        <!-- Firebase Cloud Messaging default notification icon -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_launcher_foreground" />

        <!-- Firebase Cloud Messaging default notification color -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_color"
            android:resource="@color/love" />

        <!-- Firebase Cloud Messaging default notification channel -->
        <meta-data
            android:name="com.google.firebase.messaging.default_notification_channel_id"
            android:value="text_repeater_notifications" />

    </application>

</manifest>