package com.anginatech.textrepeater.models;

import java.io.Serializable;

/**
 * Category model for dynamic text categories
 */
public class Category implements Serializable {
    private static final long serialVersionUID = 1L;
    private int id;
    private String name;
    private String displayName;
    private String description;
    private int sortOrder;

    public Category() {
    }

    public Category(int id, String name, String displayName, String description, int sortOrder) {
        this.id = id;
        this.name = name;
        this.displayName = displayName;
        this.description = description;
        this.sortOrder = sortOrder;
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(int sortOrder) {
        this.sortOrder = sortOrder;
    }

    /**
     * Get table name for this category (for backward compatibility)
     */
    public String getTableName() {
        // Map category names to table names for backward compatibility
        switch (name.toLowerCase()) {
            case "romantic":
            case "romantics":
                return "romanticTable";
            case "sad":
                return "sadTable";
            case "funny":
                return "funnyTable";
            default:
                // For new categories, use a sanitized table name
                return name.toLowerCase().replaceAll("[^a-zA-Z0-9]", "") + "Table";
        }
    }

    /**
     * Get emoji for this category
     */
    public String getEmoji() {
        switch (name.toLowerCase()) {
            case "romantic":
            case "romantics":
                return "🥰";
            case "sad":
                return "🥲";
            case "funny":
                return "😄";
            case "motivational":
            case "motivation":
                return "💪";
            case "love":
                return "❤️";
            case "friendship":
                return "👫";
            case "inspirational":
            case "inspiration":
                return "✨";
            case "birthday":
                return "🎂";
            case "good morning":
            case "morning":
                return "🌅";
            case "good night":
            case "night":
                return "🌙";
            default:
                return "💬";
        }
    }

    /**
     * Get display name with emoji
     */
    public String getDisplayNameWithEmoji() {
        return getEmoji() + " " + displayName;
    }

    /**
     * Get compact display name for tabs (shorter version)
     */
    public String getCompactDisplayName() {
        // For tabs, use shorter names if display name is too long
        String shortName = displayName;
        if (displayName.length() > 15) {
            switch (name.toLowerCase()) {
                case "motivational":
                case "motivation":
                    shortName = "Motivational";
                    break;
                case "friendship":
                    shortName = "Friendship";
                    break;
                case "romantic":
                case "romantics":
                    shortName = "Romantic";
                    break;
                default:
                    // Truncate long names but keep them readable
                    if (displayName.contains(" ")) {
                        String[] words = displayName.split(" ");
                        shortName = words[0];
                    } else {
                        shortName = displayName.length() > 12 ?
                            displayName.substring(0, 12) + "..." : displayName;
                    }
                    break;
            }
        }
        return getEmoji() + " " + shortName;
    }

    @Override
    public String toString() {
        return "Category{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", displayName='" + displayName + '\'' +
                ", description='" + description + '\'' +
                ", sortOrder=" + sortOrder +
                '}';
    }
}
