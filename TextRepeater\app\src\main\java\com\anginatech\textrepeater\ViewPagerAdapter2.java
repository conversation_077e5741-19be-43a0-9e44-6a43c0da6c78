package com.anginatech.textrepeater;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

public class ViewPagerAdapter2 extends FragmentStateAdapter {

    public ViewPagerAdapter2(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                return new RomanticS_Fragment(); // Romantic messages
            case 1:
                return new Sad_Fragment();        // Sad messages
            case 2:
                return new Funny_Fragment();      // Funny messages
            default:
                return new RomanticS_Fragment();
        }
    }

    @Override
    public int getItemCount() {
        return 3; // Number of tabs
    }
}