package com.anginatech.textrepeater.models;

import com.google.gson.annotations.SerializedName;

/**
 * User Registration Response Model
 */
public class UserRegistrationResponse {
    
    @SerializedName("user_id")
    private String userId;
    
    @SerializedName("status")
    private String status;
    
    @SerializedName("message")
    private String message;
    
    @SerializedName("is_new_user")
    private boolean isNewUser;

    // Constructors
    public UserRegistrationResponse() {}

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public boolean isNewUser() {
        return isNewUser;
    }

    public void setNewUser(boolean newUser) {
        isNewUser = newUser;
    }
}
