#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 1048576 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=14604, tid=13432
#
# JRE version: OpenJDK Runtime Environment (21.0.6) (build 21.0.6+-13368085-b895.109)
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\agents\gradle-instrumentation-agent-8.13.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.13

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Thu Jun  5 04:54:07 2025 Bangladesh Standard Time elapsed time: 16.060467 seconds (0d 0h 0m 16s)

---------------  T H R E A D  ---------------

Current thread (0x000001eeff01df90):  WorkerThread "GC Thread#1"    [id=13432, stack(0x000000e48d900000,0x000000e48da00000) (1024K)]

Stack: [0x000000e48d900000,0x000000e48da00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d0639]
V  [jvm.dll+0x85eb03]
V  [jvm.dll+0x86105e]
V  [jvm.dll+0x861743]
V  [jvm.dll+0x27e6e6]
V  [jvm.dll+0x6ccfd5]
V  [jvm.dll+0x6c0a8a]
V  [jvm.dll+0x35537b]
V  [jvm.dll+0x35cfd6]
V  [jvm.dll+0x3aec8b]
V  [jvm.dll+0x3af344]
V  [jvm.dll+0x3af40d]
V  [jvm.dll+0x3291ec]
V  [jvm.dll+0x328f48]
V  [jvm.dll+0x317b26]
V  [jvm.dll+0x3189ba]
V  [jvm.dll+0x318e67]
V  [jvm.dll+0x3188b7]
V  [jvm.dll+0x35720a]
V  [jvm.dll+0x357586]
V  [jvm.dll+0x3586ca]
V  [jvm.dll+0x358344]
V  [jvm.dll+0x36c8d9]
V  [jvm.dll+0x36cf9a]
V  [jvm.dll+0x36d097]
V  [jvm.dll+0x36df7b]
V  [jvm.dll+0x88c7e4]
V  [jvm.dll+0x806ed8]
V  [jvm.dll+0x6cef0d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001ee8629d680, length=146, elements={
0x000001eee73ae950, 0x000001eeff15f320, 0x000001eeff1605c0, 0x000001eeff1661f0,
0x000001eeff168910, 0x000001eeff1693e0, 0x000001eeff18e3c0, 0x000001eeff1b8cf0,
0x000001eeff1bc430, 0x000001eeff5fd690, 0x000001eee738ea90, 0x000001eefed9df30,
0x000001ee856b9e10, 0x000001ee840c5960, 0x000001ee864ccaf0, 0x000001ee864cafb0,
0x000001ee864cb680, 0x000001ee864cd890, 0x000001ee864cd1c0, 0x000001ee864cdf60,
0x000001ee864ca8e0, 0x000001ee86959a80, 0x000001ee869541f0, 0x000001ee86958ce0,
0x000001ee86957f40, 0x000001ee86957870, 0x000001ee869526b0, 0x000001ee86954f90,
0x000001ee86953b20, 0x000001ee86952d80, 0x000001ee869571a0, 0x000001ee864cbd50,
0x000001ee864cc420, 0x000001ee8a7cf4d0, 0x000001ee86955d30, 0x000001ee8b6589c0,
0x000001ee8a11f0f0, 0x000001ee8a118ac0, 0x000001ee8a7ce730, 0x000001ee8a7d2480,
0x000001ee8a7cc520, 0x000001ee8a7d0940, 0x000001ee8a7d0270, 0x000001ee8a7cb780,
0x000001ee8a7cb0b0, 0x000001ee8a7cfba0, 0x000001ee8a7d1010, 0x000001ee8a7d16e0,
0x000001ee86958610, 0x000001ee873c7550, 0x000001ee873cdb80, 0x000001ee873c9090,
0x000001ee873c67b0, 0x000001ee873c6e80, 0x000001ee873c9e30, 0x000001ee873cb2a0,
0x000001ee873cc040, 0x000001ee873cd4b0, 0x000001ee873cc710, 0x000001ee8a7ccbf0,
0x000001ee8a11c140, 0x000001ee8a11e350, 0x000001ee8a119f30, 0x000001ee851bfef0,
0x000001ee851c2100, 0x000001ee8c01f5e0, 0x000001ee8a11dc80, 0x000001ee8a119860,
0x000001ee8a11ea20, 0x000001ee8a11c810, 0x000001ee8a11acd0, 0x000001ee8a11f7c0,
0x000001ee8a11b3a0, 0x000001ee8a11fe90, 0x000001ee8bf4daa0, 0x000001ee8bf4b890,
0x000001ee8bf4aaf0, 0x000001ee8bf4b1c0, 0x000001ee8bf4bf60, 0x000001ee8bf48210,
0x000001ee8bf4e170, 0x000001ee86953450, 0x000001ee869593b0, 0x000001ee873ca500,
0x000001ee8c375d80, 0x000001ee8c3756b0, 0x000001ee8c374240, 0x000001ee8c374910,
0x000001ee8c372030, 0x000001ee8c36f750, 0x000001ee8c372700, 0x000001ee8c3704f0,
0x000001ee8c374fe0, 0x000001ee8c373b70, 0x000001ee8c3734a0, 0x000001ee8c370bc0,
0x000001ee8c372dd0, 0x000001ee851c49e0, 0x000001ee851bdce0, 0x000001ee851be3b0,
0x000001ee851c05c0, 0x000001ee851c27d0, 0x000001ee851c0c90, 0x000001ee851bf820,
0x000001ee851c1a30, 0x000001ee851c4310, 0x000001ee851c2ea0, 0x000001ee851c3570,
0x000001ee851c3c40, 0x000001ee8bf4cd00, 0x000001ee8bf4d3d0, 0x000001ee8bf488e0,
0x000001ee86f5e0e0, 0x000001ee8b65c240, 0x000001ee86f5d340, 0x000001ee8c371960,
0x000001ee8c01ef10, 0x000001ee89cb0610, 0x000001ee89cb2820, 0x000001ee89cb0ce0,
0x000001ee89cb2150, 0x000001ee89cb2ef0, 0x000001ee89cb1a80, 0x000001ee89cadd30,
0x000001ee89caff40, 0x000001ee89cb13b0, 0x000001ee89caf1a0, 0x000001ee89caf870,
0x000001ee89cb3c90, 0x000001ee89cb4360, 0x000001ee89cacf90, 0x000001ee89cae400,
0x000001ee89caead0, 0x000001ee89cb4a30, 0x000001ee89cad660, 0x000001ee8a7e8290,
0x000001ee8a7e52e0, 0x000001ee8a7e6e20, 0x000001ee8a7e37a0, 0x000001ee86f58180,
0x000001ee86f58f20, 0x000001ee86f528f0, 0x000001ee86f56d10, 0x000001ee86f5b800,
0x000001ee86f5f550, 0x000001ee86f59cc0
}

Java Threads: ( => current thread )
  0x000001eee73ae950 JavaThread "main"                              [_thread_blocked, id=17168, stack(0x000000e48c500000,0x000000e48c600000) (1024K)]
  0x000001eeff15f320 JavaThread "Reference Handler"          daemon [_thread_blocked, id=18156, stack(0x000000e48cd00000,0x000000e48ce00000) (1024K)]
  0x000001eeff1605c0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=11880, stack(0x000000e48ce00000,0x000000e48cf00000) (1024K)]
  0x000001eeff1661f0 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=4844, stack(0x000000e48cf00000,0x000000e48d000000) (1024K)]
  0x000001eeff168910 JavaThread "Attach Listener"            daemon [_thread_blocked, id=5724, stack(0x000000e48d000000,0x000000e48d100000) (1024K)]
  0x000001eeff1693e0 JavaThread "Service Thread"             daemon [_thread_blocked, id=14468, stack(0x000000e48d100000,0x000000e48d200000) (1024K)]
  0x000001eeff18e3c0 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=19248, stack(0x000000e48d200000,0x000000e48d300000) (1024K)]
  0x000001eeff1b8cf0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=19132, stack(0x000000e48d300000,0x000000e48d400000) (1024K)]
  0x000001eeff1bc430 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=11228, stack(0x000000e48d400000,0x000000e48d500000) (1024K)]
  0x000001eeff5fd690 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=4324, stack(0x000000e48d500000,0x000000e48d600000) (1024K)]
  0x000001eee738ea90 JavaThread "Notification Thread"        daemon [_thread_blocked, id=18888, stack(0x000000e48d600000,0x000000e48d700000) (1024K)]
  0x000001eefed9df30 JavaThread "Daemon health stats"               [_thread_blocked, id=11016, stack(0x000000e48e000000,0x000000e48e100000) (1024K)]
  0x000001ee856b9e10 JavaThread "Incoming local TCP Connector on port 54832"        [_thread_in_native, id=10740, stack(0x000000e48e100000,0x000000e48e200000) (1024K)]
  0x000001ee840c5960 JavaThread "Daemon periodic checks"            [_thread_blocked, id=3044, stack(0x000000e48e200000,0x000000e48e300000) (1024K)]
  0x000001ee864ccaf0 JavaThread "Daemon"                            [_thread_blocked, id=11760, stack(0x000000e48e300000,0x000000e48e400000) (1024K)]
  0x000001ee864cafb0 JavaThread "Handler for socket connection from /127.0.0.1:54832 to /127.0.0.1:54833"        [_thread_in_native, id=7880, stack(0x000000e48e400000,0x000000e48e500000) (1024K)]
  0x000001ee864cb680 JavaThread "Cancel handler"                    [_thread_blocked, id=2260, stack(0x000000e48e500000,0x000000e48e600000) (1024K)]
  0x000001ee864cd890 JavaThread "Daemon worker"                     [_thread_blocked, id=17192, stack(0x000000e48e600000,0x000000e48e700000) (1024K)]
  0x000001ee864cd1c0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:54832 to /127.0.0.1:54833"        [_thread_blocked, id=10836, stack(0x000000e48e700000,0x000000e48e800000) (1024K)]
  0x000001ee864cdf60 JavaThread "Stdin handler"                     [_thread_blocked, id=6756, stack(0x000000e48e800000,0x000000e48e900000) (1024K)]
  0x000001ee864ca8e0 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=11800, stack(0x000000e48e900000,0x000000e48ea00000) (1024K)]
  0x000001ee86959a80 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=12824, stack(0x000000e48eb00000,0x000000e48ec00000) (1024K)]
  0x000001ee869541f0 JavaThread "File lock request listener"        [_thread_in_native, id=19172, stack(0x000000e48ec00000,0x000000e48ed00000) (1024K)]
  0x000001ee86958ce0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.13\fileHashes)"        [_thread_blocked, id=15196, stack(0x000000e48ed00000,0x000000e48ee00000) (1024K)]
  0x000001ee86957f40 JavaThread "Cache worker for file hash cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\8.13\fileHashes)"        [_thread_blocked, id=5028, stack(0x000000e48ee00000,0x000000e48ef00000) (1024K)]
  0x000001ee86957870 JavaThread "Cache worker for Build Output Cleanup Cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\buildOutputCleanup)"        [_thread_blocked, id=264, stack(0x000000e48ef00000,0x000000e48f000000) (1024K)]
  0x000001ee869526b0 JavaThread "File watcher server"        daemon [_thread_blocked, id=18968, stack(0x000000e48f000000,0x000000e48f100000) (1024K)]
  0x000001ee86954f90 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=11356, stack(0x000000e48f100000,0x000000e48f200000) (1024K)]
  0x000001ee86953b20 JavaThread "jar transforms"                    [_thread_blocked, id=2916, stack(0x000000e48f200000,0x000000e48f300000) (1024K)]
  0x000001ee86952d80 JavaThread "Cache worker for checksums cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\8.13\checksums)"        [_thread_blocked, id=18772, stack(0x000000e48f300000,0x000000e48f400000) (1024K)]
  0x000001ee869571a0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.13\fileContent)"        [_thread_blocked, id=5792, stack(0x000000e48f400000,0x000000e48f500000) (1024K)]
  0x000001ee864cbd50 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.13\md-supplier)"        [_thread_blocked, id=19016, stack(0x000000e48f500000,0x000000e48f600000) (1024K)]
  0x000001ee864cc420 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.13\md-rule)"        [_thread_blocked, id=1700, stack(0x000000e48f600000,0x000000e48f700000) (1024K)]
  0x000001ee8a7cf4d0 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=16644, stack(0x000000e48f700000,0x000000e48f800000) (1024K)]
  0x000001ee86955d30 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=9740, stack(0x000000e48f800000,0x000000e48f900000) (1024K)]
  0x000001ee8b6589c0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=18544, stack(0x000000e48d700000,0x000000e48d800000) (1024K)]
  0x000001ee8a11f0f0 JavaThread "Unconstrained build operations"        [_thread_blocked, id=2328, stack(0x000000e48d800000,0x000000e48d900000) (1024K)]
  0x000001ee8a118ac0 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=14856, stack(0x000000e48f900000,0x000000e48fa00000) (1024K)]
  0x000001ee8a7ce730 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=17036, stack(0x000000e48fa00000,0x000000e48fb00000) (1024K)]
  0x000001ee8a7d2480 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=4608, stack(0x000000e48fb00000,0x000000e48fc00000) (1024K)]
  0x000001ee8a7cc520 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=14532, stack(0x000000e48fc00000,0x000000e48fd00000) (1024K)]
  0x000001ee8a7d0940 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=5676, stack(0x000000e48fd00000,0x000000e48fe00000) (1024K)]
  0x000001ee8a7d0270 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=14472, stack(0x000000e48fe00000,0x000000e48ff00000) (1024K)]
  0x000001ee8a7cb780 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=19348, stack(0x000000e48ff00000,0x000000e490000000) (1024K)]
  0x000001ee8a7cb0b0 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=18708, stack(0x000000e490000000,0x000000e490100000) (1024K)]
  0x000001ee8a7cfba0 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=10020, stack(0x000000e490100000,0x000000e490200000) (1024K)]
  0x000001ee8a7d1010 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=15836, stack(0x000000e490200000,0x000000e490300000) (1024K)]
  0x000001ee8a7d16e0 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=1476, stack(0x000000e490300000,0x000000e490400000) (1024K)]
  0x000001ee86958610 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=11540, stack(0x000000e490400000,0x000000e490500000) (1024K)]
  0x000001ee873c7550 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=18612, stack(0x000000e490500000,0x000000e490600000) (1024K)]
  0x000001ee873cdb80 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=5672, stack(0x000000e490700000,0x000000e490800000) (1024K)]
  0x000001ee873c9090 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=7628, stack(0x000000e490800000,0x000000e490900000) (1024K)]
  0x000001ee873c67b0 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=15620, stack(0x000000e490900000,0x000000e490a00000) (1024K)]
  0x000001ee873c6e80 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=17640, stack(0x000000e490a00000,0x000000e490b00000) (1024K)]
  0x000001ee873c9e30 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=5012, stack(0x000000e490b00000,0x000000e490c00000) (1024K)]
  0x000001ee873cb2a0 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=18072, stack(0x000000e490c00000,0x000000e490d00000) (1024K)]
  0x000001ee873cc040 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=11156, stack(0x000000e490d00000,0x000000e490e00000) (1024K)]
  0x000001ee873cd4b0 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=15976, stack(0x000000e490e00000,0x000000e490f00000) (1024K)]
  0x000001ee873cc710 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=19344, stack(0x000000e490f00000,0x000000e491000000) (1024K)]
  0x000001ee8a7ccbf0 JavaThread "build event listener"              [_thread_blocked, id=2076, stack(0x000000e491000000,0x000000e491100000) (1024K)]
  0x000001ee8a11c140 JavaThread "Memory manager"                    [_thread_blocked, id=17532, stack(0x000000e491100000,0x000000e491200000) (1024K)]
  0x000001ee8a11e350 JavaThread "Problems report writer"            [_thread_blocked, id=11656, stack(0x000000e491200000,0x000000e491300000) (1024K)]
  0x000001ee8a119f30 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=1128, stack(0x000000e491300000,0x000000e491400000) (1024K)]
  0x000001ee851bfef0 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=14276, stack(0x000000e491400000,0x000000e491500000) (1024K)]
  0x000001ee851c2100 JavaThread "build event listener"              [_thread_blocked, id=17952, stack(0x000000e491500000,0x000000e491600000) (1024K)]
  0x000001ee8c01f5e0 JavaThread "included builds"                   [_thread_blocked, id=18604, stack(0x000000e491600000,0x000000e491700000) (1024K)]
  0x000001ee8a11dc80 JavaThread "Execution worker"                  [_thread_blocked, id=15332, stack(0x000000e491700000,0x000000e491800000) (1024K)]
  0x000001ee8a119860 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=10188, stack(0x000000e491800000,0x000000e491900000) (1024K)]
  0x000001ee8a11ea20 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=13428, stack(0x000000e491900000,0x000000e491a00000) (1024K)]
  0x000001ee8a11c810 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=16260, stack(0x000000e491a00000,0x000000e491b00000) (1024K)]
  0x000001ee8a11acd0 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=8852, stack(0x000000e491b00000,0x000000e491c00000) (1024K)]
  0x000001ee8a11f7c0 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=5872, stack(0x000000e491c00000,0x000000e491d00000) (1024K)]
  0x000001ee8a11b3a0 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=9532, stack(0x000000e491d00000,0x000000e491e00000) (1024K)]
  0x000001ee8a11fe90 JavaThread "Cache worker for execution history cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\8.13\executionHistory)"        [_thread_blocked, id=17696, stack(0x000000e491e00000,0x000000e491f00000) (1024K)]
  0x000001ee8bf4daa0 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=6480, stack(0x000000e491f00000,0x000000e492000000) (1024K)]
  0x000001ee8bf4b890 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=16892, stack(0x000000e492000000,0x000000e492100000) (1024K)]
  0x000001ee8bf4aaf0 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=14608, stack(0x000000e492100000,0x000000e492200000) (1024K)]
  0x000001ee8bf4b1c0 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=16164, stack(0x000000e492200000,0x000000e492300000) (1024K)]
  0x000001ee8bf4bf60 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=15660, stack(0x000000e492300000,0x000000e492400000) (1024K)]
  0x000001ee8bf48210 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=1688, stack(0x000000e492400000,0x000000e492500000) (1024K)]
  0x000001ee8bf4e170 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=17700, stack(0x000000e492500000,0x000000e492600000) (1024K)]
  0x000001ee86953450 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=17444, stack(0x000000e492600000,0x000000e492700000) (1024K)]
  0x000001ee869593b0 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=2992, stack(0x000000e492700000,0x000000e492800000) (1024K)]
  0x000001ee873ca500 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=10528, stack(0x000000e492800000,0x000000e492900000) (1024K)]
  0x000001ee8c375d80 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=1792, stack(0x000000e492900000,0x000000e492a00000) (1024K)]
  0x000001ee8c3756b0 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=7692, stack(0x000000e492a00000,0x000000e492b00000) (1024K)]
  0x000001ee8c374240 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=1752, stack(0x000000e492b00000,0x000000e492c00000) (1024K)]
  0x000001ee8c374910 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=10348, stack(0x000000e492c00000,0x000000e492d00000) (1024K)]
  0x000001ee8c372030 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=12884, stack(0x000000e492d00000,0x000000e492e00000) (1024K)]
  0x000001ee8c36f750 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=8700, stack(0x000000e492e00000,0x000000e492f00000) (1024K)]
  0x000001ee8c372700 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=18248, stack(0x000000e492f00000,0x000000e493000000) (1024K)]
  0x000001ee8c3704f0 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=1500, stack(0x000000e493000000,0x000000e493100000) (1024K)]
  0x000001ee8c374fe0 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=7284, stack(0x000000e493100000,0x000000e493200000) (1024K)]
  0x000001ee8c373b70 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=10208, stack(0x000000e493200000,0x000000e493300000) (1024K)]
  0x000001ee8c3734a0 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=14072, stack(0x000000e493300000,0x000000e493400000) (1024K)]
  0x000001ee8c370bc0 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=11812, stack(0x000000e493400000,0x000000e493500000) (1024K)]
  0x000001ee8c372dd0 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=7676, stack(0x000000e493500000,0x000000e493600000) (1024K)]
  0x000001ee851c49e0 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=15784, stack(0x000000e493600000,0x000000e493700000) (1024K)]
  0x000001ee851bdce0 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=15032, stack(0x000000e493700000,0x000000e493800000) (1024K)]
  0x000001ee851be3b0 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=15748, stack(0x000000e493800000,0x000000e493900000) (1024K)]
  0x000001ee851c05c0 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=17848, stack(0x000000e493900000,0x000000e493a00000) (1024K)]
  0x000001ee851c27d0 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=11244, stack(0x000000e493a00000,0x000000e493b00000) (1024K)]
  0x000001ee851c0c90 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=16412, stack(0x000000e493b00000,0x000000e493c00000) (1024K)]
  0x000001ee851bf820 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=15552, stack(0x000000e493c00000,0x000000e493d00000) (1024K)]
  0x000001ee851c1a30 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=11428, stack(0x000000e493d00000,0x000000e493e00000) (1024K)]
  0x000001ee851c4310 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=18536, stack(0x000000e493e00000,0x000000e493f00000) (1024K)]
  0x000001ee851c2ea0 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=1312, stack(0x000000e493f00000,0x000000e494000000) (1024K)]
  0x000001ee851c3570 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=7764, stack(0x000000e494000000,0x000000e494100000) (1024K)]
  0x000001ee851c3c40 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=9264, stack(0x000000e494100000,0x000000e494200000) (1024K)]
  0x000001ee8bf4cd00 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=3544, stack(0x000000e494200000,0x000000e494300000) (1024K)]
  0x000001ee8bf4d3d0 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=1032, stack(0x000000e494300000,0x000000e494400000) (1024K)]
  0x000001ee8bf488e0 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=17180, stack(0x000000e494400000,0x000000e494500000) (1024K)]
  0x000001ee86f5e0e0 JavaThread "WorkerExecutor Queue"              [_thread_blocked, id=12916, stack(0x000000e490600000,0x000000e490700000) (1024K)]
  0x000001ee8b65c240 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=19000, stack(0x000000e494500000,0x000000e494600000) (1024K)]
  0x000001ee86f5d340 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=19168, stack(0x000000e494600000,0x000000e494700000) (1024K)]
  0x000001ee8c371960 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=3604, stack(0x000000e494700000,0x000000e494800000) (1024K)]
  0x000001ee8c01ef10 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=19324, stack(0x000000e494800000,0x000000e494900000) (1024K)]
  0x000001ee89cb0610 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=18076, stack(0x000000e494900000,0x000000e494a00000) (1024K)]
  0x000001ee89cb2820 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=18056, stack(0x000000e494a00000,0x000000e494b00000) (1024K)]
  0x000001ee89cb0ce0 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=9544, stack(0x000000e494b00000,0x000000e494c00000) (1024K)]
  0x000001ee89cb2150 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=17004, stack(0x000000e494c00000,0x000000e494d00000) (1024K)]
  0x000001ee89cb2ef0 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=3052, stack(0x000000e494d00000,0x000000e494e00000) (1024K)]
  0x000001ee89cb1a80 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=7324, stack(0x000000e494e00000,0x000000e494f00000) (1024K)]
  0x000001ee89cadd30 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=12856, stack(0x000000e494f00000,0x000000e495000000) (1024K)]
  0x000001ee89caff40 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=7888, stack(0x000000e495000000,0x000000e495100000) (1024K)]
  0x000001ee89cb13b0 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=18644, stack(0x000000e495100000,0x000000e495200000) (1024K)]
  0x000001ee89caf1a0 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=7336, stack(0x000000e495200000,0x000000e495300000) (1024K)]
  0x000001ee89caf870 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=7224, stack(0x000000e495300000,0x000000e495400000) (1024K)]
  0x000001ee89cb3c90 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=17808, stack(0x000000e495400000,0x000000e495500000) (1024K)]
  0x000001ee89cb4360 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=14616, stack(0x000000e495500000,0x000000e495600000) (1024K)]
  0x000001ee89cacf90 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=18880, stack(0x000000e495600000,0x000000e495700000) (1024K)]
  0x000001ee89cae400 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=3436, stack(0x000000e495700000,0x000000e495800000) (1024K)]
  0x000001ee89caead0 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=10844, stack(0x000000e495800000,0x000000e495900000) (1024K)]
  0x000001ee89cb4a30 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=7568, stack(0x000000e495900000,0x000000e495a00000) (1024K)]
  0x000001ee89cad660 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=15908, stack(0x000000e495a00000,0x000000e495b00000) (1024K)]
  0x000001ee8a7e8290 JavaThread "WorkerExecutor Queue Thread 2"        [_thread_blocked, id=15848, stack(0x000000e495b00000,0x000000e495c00000) (1024K)]
  0x000001ee8a7e52e0 JavaThread "WorkerExecutor Queue Thread 3"        [_thread_blocked, id=7108, stack(0x000000e495c00000,0x000000e495d00000) (1024K)]
  0x000001ee8a7e6e20 JavaThread "WorkerExecutor Queue Thread 4"        [_thread_blocked, id=16448, stack(0x000000e495d00000,0x000000e495e00000) (1024K)]
  0x000001ee8a7e37a0 JavaThread "WorkerExecutor Queue Thread 5"        [_thread_blocked, id=14552, stack(0x000000e495e00000,0x000000e495f00000) (1024K)]
  0x000001ee86f58180 JavaThread "WorkerExecutor Queue Thread 6"        [_thread_blocked, id=10192, stack(0x000000e495f00000,0x000000e496000000) (1024K)]
  0x000001ee86f58f20 JavaThread "WorkerExecutor Queue Thread 7"        [_thread_blocked, id=17092, stack(0x000000e496000000,0x000000e496100000) (1024K)]
  0x000001ee86f528f0 JavaThread "WorkerExecutor Queue Thread 8"        [_thread_blocked, id=12360, stack(0x000000e496100000,0x000000e496200000) (1024K)]
  0x000001ee86f56d10 JavaThread "pool-4-thread-1"                   [_thread_blocked, id=16276, stack(0x000000e496200000,0x000000e496300000) (1024K)]
  0x000001ee86f5b800 JavaThread "WorkerExecutor Queue Thread 9"        [_thread_blocked, id=18564, stack(0x000000e496300000,0x000000e496400000) (1024K)]
  0x000001ee86f5f550 JavaThread "stderr"                            [_thread_in_native, id=16036, stack(0x000000e496400000,0x000000e496500000) (1024K)]
  0x000001ee86f59cc0 JavaThread "stdout"                            [_thread_in_native, id=8956, stack(0x000000e496500000,0x000000e496600000) (1024K)]
Total: 146

Other Threads:
  0x000001eefee2d8c0 VMThread "VM Thread"                           [id=4828, stack(0x000000e48cc00000,0x000000e48cd00000) (1024K)]
  0x000001eefeb7bae0 WatcherThread "VM Periodic Task Thread"        [id=7624, stack(0x000000e48cb00000,0x000000e48cc00000) (1024K)]
  0x000001eefbfda170 WorkerThread "GC Thread#0"                     [id=12972, stack(0x000000e48c600000,0x000000e48c700000) (1024K)]
=>0x000001eeff01df90 WorkerThread "GC Thread#1"                     [id=13432, stack(0x000000e48d900000,0x000000e48da00000) (1024K)]
  0x000001ee84a05ba0 WorkerThread "GC Thread#2"                     [id=15732, stack(0x000000e48da00000,0x000000e48db00000) (1024K)]
  0x000001ee848e6b30 WorkerThread "GC Thread#3"                     [id=16028, stack(0x000000e48db00000,0x000000e48dc00000) (1024K)]
  0x000001eeff2ff280 WorkerThread "GC Thread#4"                     [id=5636, stack(0x000000e48dc00000,0x000000e48dd00000) (1024K)]
  0x000001ee848e6f10 WorkerThread "GC Thread#5"                     [id=5432, stack(0x000000e48dd00000,0x000000e48de00000) (1024K)]
  0x000001ee848e72f0 WorkerThread "GC Thread#6"                     [id=5732, stack(0x000000e48de00000,0x000000e48df00000) (1024K)]
  0x000001ee8498ee70 WorkerThread "GC Thread#7"                     [id=17752, stack(0x000000e48df00000,0x000000e48e000000) (1024K)]
  0x000001eefbfec1c0 ConcurrentGCThread "G1 Main Marker"            [id=18464, stack(0x000000e48c700000,0x000000e48c800000) (1024K)]
  0x000001eefbfed350 WorkerThread "G1 Conc#0"                       [id=18984, stack(0x000000e48c800000,0x000000e48c900000) (1024K)]
  0x000001ee85672d30 WorkerThread "G1 Conc#1"                       [id=17684, stack(0x000000e48ea00000,0x000000e48eb00000) (1024K)]
  0x000001eefea40090 ConcurrentGCThread "G1 Refine#0"               [id=16756, stack(0x000000e48c900000,0x000000e48ca00000) (1024K)]
  0x000001eefea40ac0 ConcurrentGCThread "G1 Service"                [id=15168, stack(0x000000e48ca00000,0x000000e48cb00000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0  16108 14057       4       org.gradle.internal.Try$Success::ifSuccessfulOrElse (11 bytes)
C2 CompilerThread1  16108 14672       4       org.gradle.tooling.internal.provider.runner.ProjectConfigurationTracker::finished (56 bytes)
C2 CompilerThread2  16108 14704 %     4       com.android.tools.r8.dex.C::<init> @ 189 (248 bytes)
Total: 3

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff9b542bca0] FreeList_lock - owner thread: 0x000001eeff01df90
[0x00007ff9b542e3a0] Threads_lock - owner thread: 0x000001eefee2d8c0
[0x00007ff9b542e4a0] Heap_lock - owner thread: 0x000001ee8c36f750

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: **********
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 262144K, used 249122K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 39 young (39936K), 8 survivors (8192K)
 Metaspace       used 117180K, committed 119424K, reserved 1179648K
  class space    used 15577K, committed 16704K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080100000| PB 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080300000| Untracked 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080400000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000| PB 0x0000000080500000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%|HS|  |TAMS 0x0000000080700000| PB 0x0000000080600000| Complete 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000| PB 0x0000000080700000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080800000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080900000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080a00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080b00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080c00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080d00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080e00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000080f00000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081000000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081100000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081200000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081300000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081400000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000| PB 0x0000000081500000| Untracked 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%| O|  |TAMS 0x0000000081700000| PB 0x0000000081600000| Untracked 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%| O|  |TAMS 0x0000000081800000| PB 0x0000000081700000| Untracked 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%| O|  |TAMS 0x0000000081900000| PB 0x0000000081800000| Untracked 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%| O|  |TAMS 0x0000000081a00000| PB 0x0000000081900000| Untracked 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%| O|  |TAMS 0x0000000081b00000| PB 0x0000000081a00000| Untracked 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%| O|  |TAMS 0x0000000081c00000| PB 0x0000000081b00000| Untracked 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%| O|  |TAMS 0x0000000081d00000| PB 0x0000000081c00000| Untracked 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%| O|  |TAMS 0x0000000081e00000| PB 0x0000000081d00000| Untracked 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%| O|  |TAMS 0x0000000081f00000| PB 0x0000000081e00000| Untracked 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%| O|  |TAMS 0x0000000082000000| PB 0x0000000081f00000| Untracked 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%| O|  |TAMS 0x0000000082100000| PB 0x0000000082000000| Untracked 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%| O|  |TAMS 0x0000000082200000| PB 0x0000000082100000| Untracked 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000| PB 0x0000000082200000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082300000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082400000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082500000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082600000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082800000| PB 0x0000000082700000| Untracked 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082800000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082900000| Untracked 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082b00000| PB 0x0000000082a00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082b00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000| PB 0x0000000082c00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082e00000| PB 0x0000000082d00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%|HS|  |TAMS 0x0000000082f00000| PB 0x0000000082e00000| Complete 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%|HS|  |TAMS 0x0000000083000000| PB 0x0000000082f00000| Complete 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%|HS|  |TAMS 0x0000000083100000| PB 0x0000000083000000| Complete 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%|HS|  |TAMS 0x0000000083200000| PB 0x0000000083100000| Complete 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%|HS|  |TAMS 0x0000000083300000| PB 0x0000000083200000| Complete 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%|HS|  |TAMS 0x0000000083400000| PB 0x0000000083300000| Complete 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%|HS|  |TAMS 0x0000000083500000| PB 0x0000000083400000| Complete 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%| O|  |TAMS 0x0000000083600000| PB 0x0000000083500000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%| O|  |TAMS 0x0000000083700000| PB 0x0000000083600000| Untracked 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083800000| PB 0x0000000083700000| Untracked 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083900000| PB 0x0000000083800000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083a00000| PB 0x0000000083900000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%| O|  |TAMS 0x0000000083b00000| PB 0x0000000083a00000| Untracked 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%| O|  |TAMS 0x0000000083c00000| PB 0x0000000083b00000| Untracked 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%| O|  |TAMS 0x0000000083d00000| PB 0x0000000083c00000| Untracked 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%| O|  |TAMS 0x0000000083e00000| PB 0x0000000083d00000| Untracked 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%| O|  |TAMS 0x0000000083f00000| PB 0x0000000083e00000| Untracked 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%| O|  |TAMS 0x0000000084000000| PB 0x0000000083f00000| Untracked 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000| PB 0x0000000084000000| Untracked 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000| PB 0x0000000084100000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084200000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084400000| PB 0x0000000084300000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084400000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084500000| Untracked 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084700000| PB 0x0000000084600000| Untracked 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%| O|  |TAMS 0x0000000084800000| PB 0x0000000084700000| Untracked 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%| O|  |TAMS 0x0000000084900000| PB 0x0000000084800000| Untracked 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%| O|  |TAMS 0x0000000084a00000| PB 0x0000000084900000| Untracked 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084a00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084b00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084c00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084d00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000| PB 0x0000000084e00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000| PB 0x0000000084f00000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000| PB 0x0000000085000000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085100000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085300000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085400000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085600000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085800000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085a00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085b00000| Untracked 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085c00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085d00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085e00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000085f00000| Untracked 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086600000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086600000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086700000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086800000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086900000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%|HS|  |TAMS 0x0000000086b00000| PB 0x0000000086a00000| Complete 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%|HC|  |TAMS 0x0000000086c00000| PB 0x0000000086b00000| Complete 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%|HS|  |TAMS 0x0000000086d00000| PB 0x0000000086c00000| Complete 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%|HS|  |TAMS 0x0000000086e00000| PB 0x0000000086d00000| Complete 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%|HS|  |TAMS 0x0000000086f00000| PB 0x0000000086e00000| Complete 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%|HS|  |TAMS 0x0000000087000000| PB 0x0000000086f00000| Complete 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%|HC|  |TAMS 0x0000000087100000| PB 0x0000000087000000| Complete 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%|HC|  |TAMS 0x0000000087200000| PB 0x0000000087100000| Complete 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%|HC|  |TAMS 0x0000000087300000| PB 0x0000000087200000| Complete 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%|HS|  |TAMS 0x0000000087400000| PB 0x0000000087300000| Complete 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%|HS|  |TAMS 0x0000000087500000| PB 0x0000000087400000| Complete 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%|HS|  |TAMS 0x0000000087600000| PB 0x0000000087500000| Complete 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Untracked 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%|HS|  |TAMS 0x0000000087a00000| PB 0x0000000087900000| Complete 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%|HC|  |TAMS 0x0000000087b00000| PB 0x0000000087a00000| Complete 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%|HS|  |TAMS 0x0000000087c00000| PB 0x0000000087b00000| Complete 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%|HC|  |TAMS 0x0000000087d00000| PB 0x0000000087c00000| Complete 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%|HS|  |TAMS 0x0000000087e00000| PB 0x0000000087d00000| Complete 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%|HS|  |TAMS 0x0000000087f00000| PB 0x0000000087e00000| Complete 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%| O|  |TAMS 0x0000000087f00000| PB 0x0000000087f00000| Untracked 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%| O|  |TAMS 0x0000000088000000| PB 0x0000000088000000| Untracked 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%| O|  |TAMS 0x0000000088100000| PB 0x0000000088100000| Untracked 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%|HS|  |TAMS 0x0000000088200000| PB 0x0000000088200000| Complete 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%|HS|  |TAMS 0x0000000088300000| PB 0x0000000088300000| Complete 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%|HC|  |TAMS 0x0000000088400000| PB 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%|HS|  |TAMS 0x0000000088600000| PB 0x0000000088500000| Complete 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%|HC|  |TAMS 0x0000000088700000| PB 0x0000000088600000| Complete 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%|HC|  |TAMS 0x0000000088800000| PB 0x0000000088700000| Complete 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%|HS|  |TAMS 0x0000000088900000| PB 0x0000000088800000| Complete 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%|HC|  |TAMS 0x0000000088a00000| PB 0x0000000088900000| Complete 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%|HC|  |TAMS 0x0000000088b00000| PB 0x0000000088a00000| Complete 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088c00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088d00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%|HS|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Complete 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%|HC|  |TAMS 0x0000000088e00000| PB 0x0000000088e00000| Complete 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%|HS|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Complete 
| 144|0x0000000089000000, 0x0000000089100000, 0x0000000089100000|100%|HS|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Complete 
| 145|0x0000000089100000, 0x0000000089200000, 0x0000000089200000|100%|HS|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Complete 
| 146|0x0000000089200000, 0x0000000089300000, 0x0000000089300000|100%|HS|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Complete 
| 147|0x0000000089300000, 0x0000000089400000, 0x0000000089400000|100%|HS|  |TAMS 0x0000000089300000| PB 0x0000000089300000| Complete 
| 148|0x0000000089400000, 0x0000000089500000, 0x0000000089500000|100%|HS|  |TAMS 0x0000000089400000| PB 0x0000000089400000| Complete 
| 149|0x0000000089500000, 0x0000000089600000, 0x0000000089600000|100%|HS|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Complete 
| 150|0x0000000089600000, 0x0000000089700000, 0x0000000089700000|100%|HS|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Complete 
| 151|0x0000000089700000, 0x0000000089800000, 0x0000000089800000|100%|HC|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Complete 
| 152|0x0000000089800000, 0x0000000089900000, 0x0000000089900000|100%|HS|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Complete 
| 153|0x0000000089900000, 0x0000000089a00000, 0x0000000089a00000|100%|HC|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Complete 
| 154|0x0000000089a00000, 0x0000000089b00000, 0x0000000089b00000|100%|HC|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Complete 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%|HS|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Complete 
| 156|0x0000000089c00000, 0x0000000089d00000, 0x0000000089d00000|100%|HC|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Complete 
| 157|0x0000000089d00000, 0x0000000089e00000, 0x0000000089e00000|100%|HC|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Complete 
| 158|0x0000000089e00000, 0x0000000089f00000, 0x0000000089f00000|100%|HC|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Complete 
| 159|0x0000000089f00000, 0x000000008a000000, 0x000000008a000000|100%|HS|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Complete 
| 160|0x000000008a000000, 0x000000008a100000, 0x000000008a100000|100%|HS|  |TAMS 0x000000008a000000| PB 0x000000008a000000| Complete 
| 161|0x000000008a100000, 0x000000008a200000, 0x000000008a200000|100%|HS|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Complete 
| 162|0x000000008a200000, 0x000000008a300000, 0x000000008a300000|100%|HS|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Complete 
| 163|0x000000008a300000, 0x000000008a400000, 0x000000008a400000|100%|HS|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Complete 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%|HS|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Complete 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%|HS|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Complete 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%|HS|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Complete 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%|HC|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Complete 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%|HS|  |TAMS 0x000000008a800000| PB 0x000000008a800000| Complete 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%|HC|  |TAMS 0x000000008a900000| PB 0x000000008a900000| Complete 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%|HS|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Complete 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%|HS|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Complete 
| 172|0x000000008ac00000, 0x000000008ad00000, 0x000000008ad00000|100%|HC|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Complete 
| 173|0x000000008ad00000, 0x000000008ae00000, 0x000000008ae00000|100%|HS|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Complete 
| 174|0x000000008ae00000, 0x000000008af00000, 0x000000008af00000|100%|HC|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Complete 
| 175|0x000000008af00000, 0x000000008b000000, 0x000000008b000000|100%|HS|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Complete 
| 176|0x000000008b000000, 0x000000008b100000, 0x000000008b100000|100%|HS|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Complete 
| 177|0x000000008b100000, 0x000000008b200000, 0x000000008b200000|100%|HS|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Complete 
| 178|0x000000008b200000, 0x000000008b300000, 0x000000008b300000|100%|HS|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Complete 
| 179|0x000000008b300000, 0x000000008b400000, 0x000000008b400000|100%|HS|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Complete 
| 180|0x000000008b400000, 0x000000008b44a050, 0x000000008b500000| 28%| E|CS|TAMS 0x000000008b400000| PB 0x000000008b400000| Complete 
| 181|0x000000008b500000, 0x000000008b600000, 0x000000008b600000|100%| E|CS|TAMS 0x000000008b500000| PB 0x000000008b500000| Complete 
| 182|0x000000008b600000, 0x000000008b700000, 0x000000008b700000|100%| E|CS|TAMS 0x000000008b600000| PB 0x000000008b600000| Complete 
| 183|0x000000008b700000, 0x000000008b800000, 0x000000008b800000|100%| E|CS|TAMS 0x000000008b700000| PB 0x000000008b700000| Complete 
| 184|0x000000008b800000, 0x000000008b900000, 0x000000008b900000|100%|HS|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Complete 
| 185|0x000000008b900000, 0x000000008ba00000, 0x000000008ba00000|100%|HC|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Complete 
| 186|0x000000008ba00000, 0x000000008bb00000, 0x000000008bb00000|100%|HS|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Complete 
| 187|0x000000008bb00000, 0x000000008bbfe958, 0x000000008bc00000| 99%| E|CS|TAMS 0x000000008bb00000| PB 0x000000008bb00000| Complete 
| 188|0x000000008bc00000, 0x000000008bd00000, 0x000000008bd00000|100%| E|CS|TAMS 0x000000008bc00000| PB 0x000000008bc00000| Complete 
| 189|0x000000008bd00000, 0x000000008be00000, 0x000000008be00000|100%| E|CS|TAMS 0x000000008bd00000| PB 0x000000008bd00000| Complete 
| 190|0x000000008be00000, 0x000000008bf00000, 0x000000008bf00000|100%| E|CS|TAMS 0x000000008be00000| PB 0x000000008be00000| Complete 
| 191|0x000000008bf00000, 0x000000008c000000, 0x000000008c000000|100%| E|CS|TAMS 0x000000008bf00000| PB 0x000000008bf00000| Complete 
| 192|0x000000008c000000, 0x000000008c100000, 0x000000008c100000|100%| E|CS|TAMS 0x000000008c000000| PB 0x000000008c000000| Complete 
| 193|0x000000008c100000, 0x000000008c200000, 0x000000008c200000|100%| E|CS|TAMS 0x000000008c100000| PB 0x000000008c100000| Complete 
| 194|0x000000008c200000, 0x000000008c300000, 0x000000008c300000|100%| E|CS|TAMS 0x000000008c200000| PB 0x000000008c200000| Complete 
| 195|0x000000008c300000, 0x000000008c400000, 0x000000008c400000|100%| E|CS|TAMS 0x000000008c300000| PB 0x000000008c300000| Complete 
| 196|0x000000008c400000, 0x000000008c500000, 0x000000008c500000|100%| E|CS|TAMS 0x000000008c400000| PB 0x000000008c400000| Complete 
| 197|0x000000008c500000, 0x000000008c600000, 0x000000008c600000|100%| E|CS|TAMS 0x000000008c500000| PB 0x000000008c500000| Complete 
| 198|0x000000008c600000, 0x000000008c700000, 0x000000008c700000|100%| E|CS|TAMS 0x000000008c600000| PB 0x000000008c600000| Complete 
| 199|0x000000008c700000, 0x000000008c800000, 0x000000008c800000|100%| E|CS|TAMS 0x000000008c700000| PB 0x000000008c700000| Complete 
| 200|0x000000008c800000, 0x000000008c900000, 0x000000008c900000|100%| E|CS|TAMS 0x000000008c800000| PB 0x000000008c800000| Complete 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| E|CS|TAMS 0x000000008c900000| PB 0x000000008c900000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| E|CS|TAMS 0x000000008ca00000| PB 0x000000008ca00000| Complete 
| 203|0x000000008cb00000, 0x000000008cc00000, 0x000000008cc00000|100%| E|CS|TAMS 0x000000008cb00000| PB 0x000000008cb00000| Complete 
| 204|0x000000008cc00000, 0x000000008cd00000, 0x000000008cd00000|100%| E|CS|TAMS 0x000000008cc00000| PB 0x000000008cc00000| Complete 
| 205|0x000000008cd00000, 0x000000008ce00000, 0x000000008ce00000|100%| E|CS|TAMS 0x000000008cd00000| PB 0x000000008cd00000| Complete 
| 206|0x000000008ce00000, 0x000000008cf00000, 0x000000008cf00000|100%| E|CS|TAMS 0x000000008ce00000| PB 0x000000008ce00000| Complete 
| 207|0x000000008cf00000, 0x000000008d000000, 0x000000008d000000|100%| E|CS|TAMS 0x000000008cf00000| PB 0x000000008cf00000| Complete 
| 208|0x000000008d000000, 0x000000008d100000, 0x000000008d100000|100%| E|CS|TAMS 0x000000008d000000| PB 0x000000008d000000| Complete 
| 209|0x000000008d100000, 0x000000008d200000, 0x000000008d200000|100%| E|CS|TAMS 0x000000008d100000| PB 0x000000008d100000| Complete 
| 210|0x000000008d200000, 0x000000008d300000, 0x000000008d300000|100%| E|CS|TAMS 0x000000008d200000| PB 0x000000008d200000| Complete 
| 211|0x000000008d300000, 0x000000008d400000, 0x000000008d400000|100%| E|CS|TAMS 0x000000008d300000| PB 0x000000008d300000| Complete 
| 212|0x000000008d400000, 0x000000008d500000, 0x000000008d500000|100%| E|CS|TAMS 0x000000008d400000| PB 0x000000008d400000| Complete 
| 213|0x000000008d500000, 0x000000008d600000, 0x000000008d600000|100%| E|CS|TAMS 0x000000008d500000| PB 0x000000008d500000| Complete 
| 214|0x000000008d600000, 0x000000008d700000, 0x000000008d700000|100%| E|CS|TAMS 0x000000008d600000| PB 0x000000008d600000| Complete 
| 215|0x000000008d700000, 0x000000008d800000, 0x000000008d800000|100%|HS|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Complete 
| 216|0x000000008d800000, 0x000000008d900000, 0x000000008d900000|100%|HC|  |TAMS 0x000000008d800000| PB 0x000000008d800000| Complete 
| 217|0x000000008d900000, 0x000000008da00000, 0x000000008da00000|100%|HC|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Complete 
| 218|0x000000008da00000, 0x000000008db00000, 0x000000008db00000|100%|HS|  |TAMS 0x000000008da00000| PB 0x000000008da00000| Complete 
| 219|0x000000008db00000, 0x000000008dc00000, 0x000000008dc00000|100%|HC|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Complete 
| 220|0x000000008dc00000, 0x000000008dd00000, 0x000000008dd00000|100%|HC|  |TAMS 0x000000008dc00000| PB 0x000000008dc00000| Complete 
| 221|0x000000008dd00000, 0x000000008de00000, 0x000000008de00000|100%|HS|  |TAMS 0x000000008dd00000| PB 0x000000008dd00000| Complete 
| 222|0x000000008de00000, 0x000000008df00000, 0x000000008df00000|100%|HC|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Complete 
| 223|0x000000008df00000, 0x000000008e000000, 0x000000008e000000|100%|HS|  |TAMS 0x000000008df00000| PB 0x000000008df00000| Complete 
| 224|0x000000008e000000, 0x000000008e100000, 0x000000008e100000|100%|HC|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Complete 
| 225|0x000000008e100000, 0x000000008e200000, 0x000000008e200000|100%|HC|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Complete 
| 226|0x000000008e200000, 0x000000008e300000, 0x000000008e300000|100%|HS|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Complete 
| 227|0x000000008e300000, 0x000000008e400000, 0x000000008e400000|100%|HC|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Complete 
| 228|0x000000008e400000, 0x000000008e500000, 0x000000008e500000|100%|HC|  |TAMS 0x000000008e400000| PB 0x000000008e400000| Complete 
| 229|0x000000008e500000, 0x000000008e600000, 0x000000008e600000|100%| S|  |TAMS 0x000000008e500000| PB 0x000000008e500000| Complete 
| 230|0x000000008e600000, 0x000000008e700000, 0x000000008e700000|100%| S|  |TAMS 0x000000008e600000| PB 0x000000008e600000| Complete 
| 231|0x000000008e700000, 0x000000008e800000, 0x000000008e800000|100%| S|  |TAMS 0x000000008e700000| PB 0x000000008e700000| Complete 
| 232|0x000000008e800000, 0x000000008e900000, 0x000000008e900000|100%| S|  |TAMS 0x000000008e800000| PB 0x000000008e800000| Complete 
| 233|0x000000008e900000, 0x000000008ea00000, 0x000000008ea00000|100%| S|  |TAMS 0x000000008e900000| PB 0x000000008e900000| Complete 
| 234|0x000000008ea00000, 0x000000008eb00000, 0x000000008eb00000|100%| S|  |TAMS 0x000000008ea00000| PB 0x000000008ea00000| Complete 
| 235|0x000000008eb00000, 0x000000008ec00000, 0x000000008ec00000|100%| S|  |TAMS 0x000000008eb00000| PB 0x000000008eb00000| Complete 
| 236|0x000000008ec00000, 0x000000008ed00000, 0x000000008ed00000|100%| S|  |TAMS 0x000000008ec00000| PB 0x000000008ec00000| Complete 
| 328|0x0000000094800000, 0x0000000094900000, 0x0000000094900000|100%| E|CS|TAMS 0x0000000094800000| PB 0x0000000094800000| Complete 
| 351|0x0000000095f00000, 0x0000000096000000, 0x0000000096000000|100%| E|CS|TAMS 0x0000000095f00000| PB 0x0000000095f00000| Complete 
|2031|0x00000000fef00000, 0x00000000ff000000, 0x00000000ff000000|100%| O|  |TAMS 0x00000000fef00000| PB 0x00000000fef00000| Untracked 
|2032|0x00000000ff000000, 0x00000000ff100000, 0x00000000ff100000|100%| O|  |TAMS 0x00000000ff000000| PB 0x00000000ff000000| Untracked 
|2033|0x00000000ff100000, 0x00000000ff200000, 0x00000000ff200000|100%| O|  |TAMS 0x00000000ff100000| PB 0x00000000ff100000| Untracked 
|2034|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| O|  |TAMS 0x00000000ff200000| PB 0x00000000ff200000| Untracked 
|2035|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| O|  |TAMS 0x00000000ff39ba00| PB 0x00000000ff300000| Untracked 
|2036|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| O|  |TAMS 0x00000000ff500000| PB 0x00000000ff400000| Untracked 
|2037|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| O|  |TAMS 0x00000000ff600000| PB 0x00000000ff500000| Untracked 
|2038|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| O|  |TAMS 0x00000000ff700000| PB 0x00000000ff600000| Untracked 
|2039|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| O|  |TAMS 0x00000000ff800000| PB 0x00000000ff700000| Untracked 
|2040|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| O|  |TAMS 0x00000000ff900000| PB 0x00000000ff800000| Untracked 
|2041|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| O|  |TAMS 0x00000000ffa00000| PB 0x00000000ff900000| Untracked 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| O|  |TAMS 0x00000000ffb00000| PB 0x00000000ffa00000| Untracked 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffc00000| PB 0x00000000ffb00000| Untracked 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| O|  |TAMS 0x00000000ffd00000| PB 0x00000000ffc00000| Untracked 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffe00000| PB 0x00000000ffd00000| Untracked 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| E|CS|TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Complete 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| O|  |TAMS 0x0000000100000000| PB 0x00000000fff00000| Untracked 

Card table byte_map: [0x000001eef9bb0000,0x000001eef9fb0000] _byte_map_base: 0x000001eef97b0000

Marking Bits: (CMBitMap*) 0x000001eefbfda6f0
 Bits: [0x000001eef9fb0000, 0x000001eefbfb0000)

Polling page: 0x000001eee4db0000

Metaspace:

Usage:
  Non-class:     99.22 MB used.
      Class:     15.21 MB used.
       Both:    114.43 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     100.31 MB ( 78%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      16.31 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     116.62 MB ( 10%) committed. 

Chunk freelists:
   Non-Class:  11.48 MB
       Class:  15.67 MB
        Both:  27.15 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 185.62 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 9.
num_arena_births: 4166.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1865.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 9.
num_chunks_taken_from_freelist: 9173.
num_chunk_merges: 9.
num_chunk_splits: 6001.
num_chunks_enlarged: 3757.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=7856Kb max_used=7856Kb free=112143Kb
 bounds [0x000001eef1f50000, 0x000001eef2710000, 0x000001eef9480000]
CodeHeap 'profiled nmethods': size=120000Kb used=23142Kb max_used=23142Kb free=96857Kb
 bounds [0x000001eeea480000, 0x000001eeebb20000, 0x000001eef19b0000]
CodeHeap 'non-nmethods': size=5760Kb used=2938Kb max_used=2973Kb free=2821Kb
 bounds [0x000001eef19b0000, 0x000001eef1cb0000, 0x000001eef1f50000]
 total_blobs=13343 nmethods=12337 adapters=908
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 16.033 Thread 0x000001ee8b65c240 nmethod 14689 0x000001eef2145390 code [0x000001eef2145520, 0x000001eef2145640]
Event: 16.033 Thread 0x000001ee8b6589c0 nmethod 14130 0x000001eef25b6510 code [0x000001eef25b6860, 0x000001eef25b7d90]
Event: 16.033 Thread 0x000001ee8b65c240 14690       4       com.android.tools.r8.dex.m::a (31 bytes)
Event: 16.033 Thread 0x000001ee8b6589c0 14184       4       org.gradle.api.internal.artifacts.ivyservice.resolveengine.artifact.ArtifactVisitorToResolvedFileVisitorAdapter::visitArtifact (17 bytes)
Event: 16.035 Thread 0x000001ee8b65c240 nmethod 14690 0x000001eef2144e10 code [0x000001eef2144fa0, 0x000001eef2145118]
Event: 16.035 Thread 0x000001ee8b65c240 14640       4       java.lang.Long::hashCode (8 bytes)
Event: 16.035 Thread 0x000001ee8b65c240 nmethod 14640 0x000001eef2144b10 code [0x000001eef2144ca0, 0x000001eef2144d38]
Event: 16.035 Thread 0x000001ee8b65c240 14541       4       org.gradle.internal.instantiation.generator.AsmBackedClassGenerator::access$9600 (4 bytes)
Event: 16.036 Thread 0x000001ee8b65c240 nmethod 14541 0x000001eef2144810 code [0x000001eef21449a0, 0x000001eef2144a10]
Event: 16.036 Thread 0x000001ee8b65c240 14046       4       org.gradle.internal.component.external.model.ImmutableCapabilities::asImmutable (38 bytes)
Event: 16.036 Thread 0x000001ee8b6589c0 nmethod 14184 0x000001eef25ae110 code [0x000001eef25ae300, 0x000001eef25ae600]
Event: 16.036 Thread 0x000001ee8b6589c0 14627       4       org.gradle.internal.component.external.model.DefaultImmutableCapability::defaultCapabilityForComponent (26 bytes)
Event: 16.037 Thread 0x000001eeff1bc430 14705       1       com.android.tools.r8.internal.Ms::a (5 bytes)
Event: 16.037 Thread 0x000001eeff1bc430 nmethod 14705 0x000001eef2144510 code [0x000001eef21446a0, 0x000001eef2144768]
Event: 16.040 Thread 0x000001eeff1bc430 14706       3       com.android.tools.r8.dex.C::i (234 bytes)
Event: 16.042 Thread 0x000001eeff1bc430 nmethod 14706 0x000001eeebb17510 code [0x000001eeebb17960, 0x000001eeebb19c58]
Event: 16.048 Thread 0x000001ee8b65c240 nmethod 14046 0x000001eef25b5090 code [0x000001eef25b52e0, 0x000001eef25b5fd8]
Event: 16.048 Thread 0x000001ee8b65c240 14704 %     4       com.android.tools.r8.dex.C::<init> @ 189 (248 bytes)
Event: 16.049 Thread 0x000001ee8b6589c0 nmethod 14627 0x000001eef25acd10 code [0x000001eef25acf40, 0x000001eef25adb80]
Event: 16.049 Thread 0x000001ee8b6589c0 14672       4       org.gradle.tooling.internal.provider.runner.ProjectConfigurationTracker::finished (56 bytes)

GC Heap History (20 events):
Event: 12.318 GC heap after
{Heap after GC invocations=26 (full 0):
 garbage-first heap   total 175104K, used 93820K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 101638K, committed 103680K, reserved 1179648K
  class space    used 13638K, committed 14656K, reserved 1048576K
}
Event: 13.008 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 175104K, used 158332K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 69 young (70656K), 10 survivors (10240K)
 Metaspace       used 105189K, committed 107264K, reserved 1179648K
  class space    used 14055K, committed 15104K, reserved 1048576K
}
Event: 13.019 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total 179200K, used 102669K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 105189K, committed 107264K, reserved 1179648K
  class space    used 14055K, committed 15104K, reserved 1048576K
}
Event: 13.311 GC heap before
{Heap before GC invocations=28 (full 0):
 garbage-first heap   total 179200K, used 157965K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 64 young (65536K), 9 survivors (9216K)
 Metaspace       used 105303K, committed 107520K, reserved 1179648K
  class space    used 14063K, committed 15168K, reserved 1048576K
}
Event: 13.319 GC heap after
{Heap after GC invocations=29 (full 0):
 garbage-first heap   total 179200K, used 105251K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 105303K, committed 107520K, reserved 1179648K
  class space    used 14063K, committed 15168K, reserved 1048576K
}
Event: 13.654 GC heap before
{Heap before GC invocations=29 (full 0):
 garbage-first heap   total 179200K, used 160547K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 62 young (63488K), 7 survivors (7168K)
 Metaspace       used 105683K, committed 107840K, reserved 1179648K
  class space    used 14125K, committed 15232K, reserved 1048576K
}
Event: 13.659 GC heap after
{Heap after GC invocations=30 (full 0):
 garbage-first heap   total 179200K, used 108020K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 105683K, committed 107840K, reserved 1179648K
  class space    used 14125K, committed 15232K, reserved 1048576K
}
Event: 14.352 GC heap before
{Heap before GC invocations=31 (full 0):
 garbage-first heap   total 183296K, used 159220K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 58 young (59392K), 7 survivors (7168K)
 Metaspace       used 110961K, committed 113024K, reserved 1179648K
  class space    used 14781K, committed 15808K, reserved 1048576K
}
Event: 14.357 GC heap after
{Heap after GC invocations=32 (full 0):
 garbage-first heap   total 183296K, used 109430K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 2 young (2048K), 2 survivors (2048K)
 Metaspace       used 110961K, committed 113024K, reserved 1179648K
  class space    used 14781K, committed 15808K, reserved 1048576K
}
Event: 14.755 GC heap before
{Heap before GC invocations=32 (full 0):
 garbage-first heap   total 183296K, used 165750K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 58 young (59392K), 2 survivors (2048K)
 Metaspace       used 111836K, committed 113984K, reserved 1179648K
  class space    used 14900K, committed 15936K, reserved 1048576K
}
Event: 14.764 GC heap after
{Heap after GC invocations=33 (full 0):
 garbage-first heap   total 183296K, used 114458K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 111836K, committed 113984K, reserved 1179648K
  class space    used 14900K, committed 15936K, reserved 1048576K
}
Event: 14.961 GC heap before
{Heap before GC invocations=33 (full 0):
 garbage-first heap   total 183296K, used 163610K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 57 young (58368K), 8 survivors (8192K)
 Metaspace       used 111919K, committed 114048K, reserved 1179648K
  class space    used 14905K, committed 15936K, reserved 1048576K
}
Event: 14.969 GC heap after
{Heap after GC invocations=34 (full 0):
 garbage-first heap   total 220160K, used 117455K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 111919K, committed 114048K, reserved 1179648K
  class space    used 14905K, committed 15936K, reserved 1048576K
}
Event: 15.401 GC heap before
{Heap before GC invocations=35 (full 0):
 garbage-first heap   total 220160K, used 206543K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 85 young (87040K), 6 survivors (6144K)
 Metaspace       used 112467K, committed 114624K, reserved 1179648K
  class space    used 14965K, committed 16000K, reserved 1048576K
}
Event: 15.408 GC heap after
{Heap after GC invocations=36 (full 0):
 garbage-first heap   total 220160K, used 129922K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 9 young (9216K), 9 survivors (9216K)
 Metaspace       used 112467K, committed 114624K, reserved 1179648K
  class space    used 14965K, committed 16000K, reserved 1048576K
}
Event: 15.761 GC heap before
{Heap before GC invocations=36 (full 0):
 garbage-first heap   total 220160K, used 212866K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 76 young (77824K), 9 survivors (9216K)
 Metaspace       used 114902K, committed 117056K, reserved 1179648K
  class space    used 15287K, committed 16384K, reserved 1048576K
}
Event: 15.770 GC heap after
{Heap after GC invocations=37 (full 0):
 garbage-first heap   total 235520K, used 153198K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 114902K, committed 117056K, reserved 1179648K
  class space    used 15287K, committed 16384K, reserved 1048576K
}
Event: 15.962 GC heap before
{Heap before GC invocations=37 (full 0):
 garbage-first heap   total 235520K, used 179822K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 38 young (38912K), 10 survivors (10240K)
 Metaspace       used 116756K, committed 118976K, reserved 1179648K
  class space    used 15516K, committed 16640K, reserved 1048576K
}
Event: 15.967 GC heap after
{Heap after GC invocations=38 (full 0):
 garbage-first heap   total 235520K, used 156968K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 116756K, committed 118976K, reserved 1179648K
  class space    used 15516K, committed 16640K, reserved 1048576K
}
Event: 16.053 GC heap before
{Heap before GC invocations=38 (full 0):
 garbage-first heap   total 249856K, used 248104K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 35 young (35840K), 4 survivors (4096K)
 Metaspace       used 117180K, committed 119424K, reserved 1179648K
  class space    used 15577K, committed 16704K, reserved 1048576K
}

Dll operation events (3 events):
Event: 0.009 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.015 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 0.561 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 15.881 Thread 0x000001ee8a119860 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001eef26adf14 relative=0x0000000000002054
Event: 15.881 Thread 0x000001ee8a119860 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001eef26adf14 method=org.gradle.internal.snapshot.ChildMap$Entry.handlePath(Lorg/gradle/internal/snapshot/VfsRelativePath;Lorg/gradle/internal/snapshot/CaseSensitivity;Lorg/gradle/internal/snaps
Event: 15.881 Thread 0x000001ee8a119860 DEOPT PACKING pc=0x000001eef26adf14 sp=0x000000e4918fb4b0
Event: 15.881 Thread 0x000001ee8a119860 DEOPT UNPACKING pc=0x000001eef1a046a2 sp=0x000000e4918fb480 mode 2
Event: 15.999 Thread 0x000001ee8a11fe90 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001eef20479dc relative=0x000000000000049c
Event: 15.999 Thread 0x000001ee8a11fe90 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001eef20479dc method=com.google.common.collect.MapMakerInternalMap$Segment.drainKeyReferenceQueue(Ljava/lang/ref/ReferenceQueue;)V @ 8 c2
Event: 15.999 Thread 0x000001ee8a11fe90 DEOPT PACKING pc=0x000001eef20479dc sp=0x000000e491efe5e0
Event: 15.999 Thread 0x000001ee8a11fe90 DEOPT UNPACKING pc=0x000001eef1a046a2 sp=0x000000e491efe4d0 mode 2
Event: 16.013 Thread 0x000001ee8a7e8290 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001eef20d1810 relative=0x00000000000000f0
Event: 16.013 Thread 0x000001ee8a7e8290 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001eef20d1810 method=jdk.internal.misc.Unsafe.convEndian(ZS)S @ 4 c2
Event: 16.013 Thread 0x000001ee8a7e8290 DEOPT PACKING pc=0x000001eef20d1810 sp=0x000000e495bfd400
Event: 16.013 Thread 0x000001ee8a7e8290 DEOPT UNPACKING pc=0x000001eef1a046a2 sp=0x000000e495bfd198 mode 2
Event: 16.013 Thread 0x000001ee8a7e8290 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001eef2056530 relative=0x0000000000000090
Event: 16.013 Thread 0x000001ee8a7e8290 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001eef2056530 method=jdk.internal.misc.Unsafe.convEndian(ZS)S @ 4 c2
Event: 16.013 Thread 0x000001ee8a7e8290 DEOPT PACKING pc=0x000001eef2056530 sp=0x000000e495bfd2e0
Event: 16.013 Thread 0x000001ee8a7e8290 DEOPT UNPACKING pc=0x000001eef1a046a2 sp=0x000000e495bfd1e8 mode 2
Event: 16.014 Thread 0x000001ee8a7e8290 Uncommon trap: trap_request=0xffffff45 fr.pc=0x000001eef21612ec relative=0x000000000000004c
Event: 16.014 Thread 0x000001ee8a7e8290 Uncommon trap: reason=unstable_if action=reinterpret pc=0x000001eef21612ec method=jdk.internal.misc.Unsafe.convEndian(ZS)S @ 4 c2
Event: 16.014 Thread 0x000001ee8a7e8290 DEOPT PACKING pc=0x000001eef21612ec sp=0x000000e495bfd230
Event: 16.014 Thread 0x000001ee8a7e8290 DEOPT UNPACKING pc=0x000001eef1a046a2 sp=0x000000e495bfd1c8 mode 2

Classes loaded (20 events):
Event: 15.707 Loading class java/lang/Process$PipeInputStream
Event: 15.707 Loading class java/lang/Process$PipeInputStream done
Event: 15.728 Loading class jdk/internal/event/ProcessStartEvent
Event: 15.729 Loading class jdk/internal/event/ProcessStartEvent done
Event: 15.849 Loading class java/nio/channels/Channels$WritableByteChannelImpl
Event: 15.850 Loading class java/nio/channels/Channels$WritableByteChannelImpl done
Event: 15.850 Loading class java/util/Collections$UnmodifiableSortedMap
Event: 15.850 Loading class java/util/Collections$UnmodifiableSortedMap done
Event: 15.851 Loading class java/nio/StringCharBuffer
Event: 15.851 Loading class java/nio/StringCharBuffer done
Event: 15.851 Loading class sun/nio/cs/ThreadLocalCoders
Event: 15.851 Loading class sun/nio/cs/ThreadLocalCoders done
Event: 15.851 Loading class sun/nio/cs/ThreadLocalCoders$1
Event: 15.851 Loading class sun/nio/cs/ThreadLocalCoders$Cache
Event: 15.851 Loading class sun/nio/cs/ThreadLocalCoders$Cache done
Event: 15.851 Loading class sun/nio/cs/ThreadLocalCoders$1 done
Event: 15.851 Loading class sun/nio/cs/ThreadLocalCoders$2
Event: 15.851 Loading class sun/nio/cs/ThreadLocalCoders$2 done
Event: 15.960 Loading class java/nio/BufferUnderflowException
Event: 15.960 Loading class java/nio/BufferUnderflowException done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 15.593 Thread 0x000001ee8a11acd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008aa01048}: static Lcom/android/build/api/variant/impl/SerializableApplicationMultiOutputHandler;.<clinit>()V> (0x000000008aa01048) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 15.594 Thread 0x000001ee8a11acd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008aa173a8}: static Lcom/android/build/api/variant/impl/VariantOutputImpl$SerializedForm;.<clinit>()V> (0x000000008aa173a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 15.595 Thread 0x000001ee8a11acd0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008aa60dd0}: static Lcom/android/build/api/variant/impl/VariantOutputConfigurationImpl;.<clinit>()V> (0x000000008aa60dd0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 15.645 Thread 0x000001ee8a119860 Exception <a 'sun/nio/fs/WindowsException'{0x000000008a52f7c8}> (0x000000008a52f7c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 15.646 Thread 0x000001ee8a119860 Exception <a 'sun/nio/fs/WindowsException'{0x000000008a537610}> (0x000000008a537610) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 15.705 Thread 0x000001ee8a7e52e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000089bb1b90}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, long)'> (0x0000000089bb1b90) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 15.706 Thread 0x000001ee8a7e52e0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000089a74ff8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, long, java.lang.Object)'> (0x0000000089a74ff8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 15.811 Thread 0x000001ee8a11f7c0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008cb81328}> (0x000000008cb81328) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 15.825 Thread 0x000001ee8a11b3a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008ca67f78}> (0x000000008ca67f78) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 15.829 Thread 0x000001ee8a11b3a0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008caea540}: 'int java.lang.invoke.Invokers$Holder.invokeExact_MT(java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008caea540) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 15.839 Thread 0x000001ee8a11b3a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008c8bb1e0}> (0x000000008c8bb1e0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 15.867 Thread 0x000001ee8a11b3a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008c5f5a80}> (0x000000008c5f5a80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 15.867 Thread 0x000001ee8a11b3a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008c5f6c00}> (0x000000008c5f6c00) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 15.867 Thread 0x000001ee8a11b3a0 Exception <a 'sun/nio/fs/WindowsException'{0x000000008c5f6dd0}> (0x000000008c5f6dd0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 15.886 Thread 0x000001ee8a119860 Exception <a 'sun/nio/fs/WindowsException'{0x000000008c3e28a8}> (0x000000008c3e28a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 15.887 Thread 0x000001ee8a119860 Exception <a 'sun/nio/fs/WindowsException'{0x000000008c3e37e8}> (0x000000008c3e37e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 15.887 Thread 0x000001ee8a119860 Exception <a 'sun/nio/fs/WindowsException'{0x000000008c3e39c8}> (0x000000008c3e39c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 15.887 Thread 0x000001ee86f5b800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008c2008f0}> (0x000000008c2008f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 15.889 Thread 0x000001ee86f5b800 Exception <a 'sun/nio/fs/WindowsException'{0x000000008c204668}> (0x000000008c204668) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 16.006 Thread 0x000001ee8a7e8290 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d6be010}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, java.lang.Object, int, java

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 15.082 Executing VM operation: G1PauseRemark done
Event: 15.128 Executing VM operation: G1PauseCleanup
Event: 15.128 Executing VM operation: G1PauseCleanup done
Event: 15.401 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 15.408 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 15.570 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 15.571 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 15.647 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 15.648 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 15.727 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 15.727 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 15.761 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 15.770 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 15.865 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 15.866 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 15.951 Executing VM operation: ICBufferFull
Event: 15.951 Executing VM operation: ICBufferFull done
Event: 15.962 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation)
Event: 15.967 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation) done
Event: 16.052 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 15.079 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeaf5ea10
Event: 15.079 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeafa6e90
Event: 15.079 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeafaa110
Event: 15.079 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeafb0910
Event: 15.079 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeafb1110
Event: 15.079 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeafb2010
Event: 15.079 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeafe0410
Event: 15.079 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb022390
Event: 15.080 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb06c590
Event: 15.080 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb1d4790
Event: 15.080 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb252f10
Event: 15.080 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb329a10
Event: 15.080 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb32b810
Event: 15.080 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb621e90
Event: 15.080 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb726590
Event: 15.080 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb845490
Event: 15.080 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb893a10
Event: 15.080 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb8ebf10
Event: 15.080 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb8f2590
Event: 15.080 Thread 0x000001eefee2d8c0 flushing  nmethod 0x000001eeeb8f3410

Events (20 events):
Event: 14.641 Thread 0x000001ee8c01f5e0 Thread added: 0x000001ee89caf1a0
Event: 14.642 Thread 0x000001ee8c01f5e0 Thread added: 0x000001ee89caf870
Event: 14.822 Thread 0x000001ee8a11c810 Thread added: 0x000001ee89cb3c90
Event: 14.822 Thread 0x000001ee8a11c810 Thread added: 0x000001ee89cb4360
Event: 14.822 Thread 0x000001ee8a11c810 Thread added: 0x000001ee89cacf90
Event: 14.823 Thread 0x000001ee8a11c810 Thread added: 0x000001ee89cae400
Event: 14.823 Thread 0x000001ee8a11c810 Thread added: 0x000001ee89caead0
Event: 14.824 Thread 0x000001ee8a11c810 Thread added: 0x000001ee89cb4a30
Event: 14.824 Thread 0x000001ee8a11c810 Thread added: 0x000001ee89cad660
Event: 15.282 Thread 0x000001ee8a11c810 Thread added: 0x000001ee8a7e8290
Event: 15.376 Thread 0x000001ee86f5e0e0 Thread added: 0x000001ee8a7e52e0
Event: 15.376 Thread 0x000001ee86f5e0e0 Thread added: 0x000001ee8a7e6e20
Event: 15.598 Thread 0x000001ee8a11acd0 Thread added: 0x000001ee8a7e37a0
Event: 15.631 Thread 0x000001ee8a11f7c0 Thread added: 0x000001ee86f58180
Event: 15.634 Thread 0x000001ee8a7e6e20 Thread added: 0x000001ee86f58f20
Event: 15.640 Thread 0x000001ee8a7e6e20 Thread added: 0x000001ee86f528f0
Event: 15.689 Thread 0x000001ee8a7e52e0 Thread added: 0x000001ee86f56d10
Event: 15.727 Thread 0x000001ee8a7e6e20 Thread added: 0x000001ee86f5b800
Event: 15.732 Thread 0x000001ee8a7e52e0 Thread added: 0x000001ee86f5f550
Event: 15.732 Thread 0x000001ee8a7e52e0 Thread added: 0x000001ee86f59cc0


Dynamic libraries:
0x00007ff645790000 - 0x00007ff64579a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffa35f30000 - 0x00007ffa36128000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa35030000 - 0x00007ffa350f2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa33740000 - 0x00007ffa33a36000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa30220000 - 0x00007ffa302b4000 	C:\WINDOWS\SYSTEM32\apphelp.dll
0x00007ffa1ea70000 - 0x00007ffa1ef06000 	C:\WINDOWS\SYSTEM32\AcLayers.DLL
0x00007ffa34310000 - 0x00007ffa343ae000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa34e90000 - 0x00007ffa3502d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa33dc0000 - 0x00007ffa33de2000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa357f0000 - 0x00007ffa3581b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa33c00000 - 0x00007ffa33d19000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa33d20000 - 0x00007ffa33dbd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa33640000 - 0x00007ffa33740000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa34720000 - 0x00007ffa34e8e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa34160000 - 0x00007ffa341bb000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffa34000000 - 0x00007ffa340b1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa340c0000 - 0x00007ffa3415f000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa35100000 - 0x00007ffa35223000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa33af0000 - 0x00007ffa33b17000 	C:\WINDOWS\System32\bcrypt.dll
0x000001eee4d30000 - 0x000001eee4d33000 	C:\WINDOWS\SYSTEM32\sfc.dll
0x00007ffa126e0000 - 0x00007ffa12784000 	C:\WINDOWS\SYSTEM32\WINSPOOL.DRV
0x00007ffa35230000 - 0x00007ffa35583000 	C:\WINDOWS\System32\combase.dll
0x00007ffa356b0000 - 0x00007ffa3575d000 	C:\WINDOWS\System32\shcore.dll
0x00007ffa10fe0000 - 0x00007ffa10ff2000 	C:\WINDOWS\SYSTEM32\sfc_os.DLL
0x00007ffa30590000 - 0x00007ffa305a1000 	C:\WINDOWS\SYSTEM32\SortWindows61.dll
0x00007ffa35dd0000 - 0x00007ffa35dff000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa181c0000 - 0x00007ffa181d8000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffa18160000 - 0x00007ffa1817b000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffa228a0000 - 0x00007ffa22b3a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffa23210000 - 0x00007ffa2321c000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffa0ba00000 - 0x00007ffa0ba8d000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ff9b4880000 - 0x00007ff9b550b000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffa343b0000 - 0x00007ffa3441b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa33420000 - 0x00007ffa3346b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffa28320000 - 0x00007ffa28347000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa2b410000 - 0x00007ffa2b41a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa33400000 - 0x00007ffa33412000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffa31e30000 - 0x00007ffa31e42000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa217a0000 - 0x00007ffa217aa000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffa31bc0000 - 0x00007ffa31dc1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa21760000 - 0x00007ffa21794000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa33b20000 - 0x00007ffa33ba2000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa180a0000 - 0x00007ffa180ae000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffa14050000 - 0x00007ffa14070000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffa14030000 - 0x00007ffa14048000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffa31410000 - 0x00007ffa31bb3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa32f20000 - 0x00007ffa32f4b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffa35e20000 - 0x00007ffa35eed000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa334f0000 - 0x00007ffa33515000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa18080000 - 0x00007ffa18090000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffa2f470000 - 0x00007ffa2f57a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa32c80000 - 0x00007ffa32cea000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa14010000 - 0x00007ffa14026000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffa13f80000 - 0x00007ffa13f90000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffa23360000 - 0x00007ffa23387000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ff9f0020000 - 0x00007ff9f0098000 	C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffa13f70000 - 0x00007ffa13f79000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffa12000000 - 0x00007ffa1200b000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffa34220000 - 0x00007ffa34228000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa32960000 - 0x00007ffa3299b000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa345d0000 - 0x00007ffa345d8000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa11b10000 - 0x00007ffa11b19000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffa32e70000 - 0x00007ffa32e88000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa325a0000 - 0x00007ffa325d8000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa334b0000 - 0x00007ffa334de000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa32e90000 - 0x00007ffa32e9c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa2f990000 - 0x00007ffa2f997000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\agents\gradle-instrumentation-agent-8.13.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.13
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\gradle-daemon-main-8.13.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ntc
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 30, weak refs: 4

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 658744K (2% of 23015712K total physical memory with 3971836K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 42171K
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 34005K
Loader bootstrap                                                                       : 28504K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 11251K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 507K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 328K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 235K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 160K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 15960B

Classes loaded by more than one classloader:
Class com.google.common.io.LineReader                                                 : loaded 2 times (x 68B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 2 times (x 148B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class com.google.common.io.FileWriteMode                                              : loaded 2 times (x 75B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 2 times (x 107B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class build_eoun890h7qlxwfhq6ek4y6yum$_run_closure1                                   : loaded 2 times (x 135B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedCollection            : loaded 2 times (x 120B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 142B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 2 times (x 93B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 2 times (x 117B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 2 times (x 122B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 84B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 2 times (x 91B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class [Lcom.google.common.collect.AbstractIterator$State;                             : loaded 2 times (x 65B)
Class org.apache.commons.io.file.PathUtils                                            : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 65B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class [Lorg.apache.commons.io.StandardLineSeparator;                                  : loaded 2 times (x 65B)
Class org.apache.commons.io.output.StringBuilderWriter                                : loaded 2 times (x 96B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 122B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 74B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 68B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class com.google.common.collect.Maps$ViewCachingAbstractMap                           : loaded 2 times (x 121B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 135B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 65B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 135B)
Class com.google.common.util.concurrent.AbstractFuture$SafeAtomicHelper               : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 75B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class org.apache.commons.io.output.QueueOutputStream                                  : loaded 2 times (x 83B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class com.google.common.collect.Interner                                              : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 73B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 74B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class org.apache.commons.io.output.UnsynchronizedByteArrayOutputStream                : loaded 2 times (x 98B)
Class com.google.common.collect.ImmutableListMultimap                                 : loaded 2 times (x 172B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 2 times (x 93B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 2 times (x 72B)
Class org.apache.commons.io.output.ByteArrayOutputStream                              : loaded 2 times (x 98B)
Class com.google.common.collect.EmptyImmutableListMultimap                            : loaded 2 times (x 172B)
Class com.google.common.collect.BaseImmutableMultimap                                 : loaded 2 times (x 119B)
Class com.google.common.base.Joiner                                                   : loaded 2 times (x 75B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 2 times (x 76B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class org.apache.commons.io.FileExistsException                                       : loaded 2 times (x 78B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 2 times (x 77B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 2 times (x 73B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 119B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper$1           : loaded 2 times (x 72B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 2 times (x 213B)
Class com.google.common.base.Optional                                                 : loaded 2 times (x 76B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators                                             : loaded 2 times (x 67B)
Class com.google.common.io.ByteSink$AsCharSink                                        : loaded 2 times (x 76B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 2 times (x 203B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 102B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class com.google.common.base.CharMatcher$And                                          : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class com.google.common.collect.FluentIterable$1                                      : loaded 2 times (x 77B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 73B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 2 times (x 148B)
Class com.google.common.collect.BiMap                                                 : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 2 times (x 77B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class com.google.common.base.Absent                                                   : loaded 2 times (x 76B)
Class com.google.common.collect.FluentIterable$2                                      : loaded 2 times (x 77B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 2 times (x 78B)
Class com.google.common.collect.FluentIterable$3                                      : loaded 2 times (x 77B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 67B)
Class com.google.common.collect.ObjectArrays                                          : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 2 times (x 92B)
Class org.apache.commons.io.output.NullOutputStream                                   : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 2 times (x 148B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class settings_cqr77nl3gl6lg9b2yujwf1q4o$_run_closure1                                : loaded 2 times (x 135B)
Class org.apache.commons.io.filefilter.IOFileFilter                                   : loaded 2 times (x 66B)
Class com.google.common.io.Files$2                                                    : loaded 2 times (x 72B)
Class com.google.common.io.AppendableWriter                                           : loaded 2 times (x 96B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 78B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 67B)
Class com.google.common.base.Present                                                  : loaded 2 times (x 77B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class settings_cqr77nl3gl6lg9b2yujwf1q4o$_run_closure1$_closure2                      : loaded 2 times (x 135B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class org.apache.commons.io.file.DeleteOption                                         : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultimap$Keys                                : loaded 2 times (x 162B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 66B)
Class settings_cqr77nl3gl6lg9b2yujwf1q4o                                              : loaded 2 times (x 175B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class org.apache.commons.io.IOUtils                                                   : loaded 2 times (x 67B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class com.google.common.io.ByteSink                                                   : loaded 2 times (x 72B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 2 times (x 203B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.gradle.api.UncheckedIOException                                             : loaded 2 times (x 78B)
Class com.google.common.io.Files$FileByteSource                                       : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 2 times (x 205B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 71B)
Class com.google.common.io.CharStreams$NullWriter                                     : loaded 2 times (x 95B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.google.common.util.concurrent.AbstractFuture$AtomicHelper                   : loaded 2 times (x 74B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class com.google.common.collect.Iterators$1                                           : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 2 times (x 142B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 2 times (x 122B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class com.google.common.collect.Iterators$4                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 2 times (x 139B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class org.apache.commons.io.output.NullWriter                                         : loaded 2 times (x 95B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$5                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 72B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 80B)
Class com.google.common.collect.ImmutableMultimap$Values                              : loaded 2 times (x 122B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper             : loaded 2 times (x 74B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 2 times (x 108B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 2 times (x 121B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 2 times (x 108B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 86B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 2 times (x 144B)
Class com.google.common.collect.Lists                                                 : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$9                                           : loaded 2 times (x 77B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 2 times (x 205B)
Class com.google.common.io.CharStreams                                                : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultiset                                     : loaded 2 times (x 159B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 66B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class org.apache.commons.io.CloseableURLConnection                                    : loaded 2 times (x 112B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 2 times (x 107B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 70B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMultimap                                     : loaded 2 times (x 143B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class com.google.common.base.Function                                                 : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 2 times (x 81B)
Class org.apache.commons.io.output.AbstractByteArrayOutputStream                      : loaded 2 times (x 98B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 2 times (x 209B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class build_eoun890h7qlxwfhq6ek4y6yum                                                 : loaded 2 times (x 176B)
Class com.google.common.base.Charsets                                                 : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 67B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class org.apache.commons.io.file.PathFilter                                           : loaded 2 times (x 66B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 65B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class com.google.common.io.LineReader$1                                               : loaded 2 times (x 71B)
Class com.google.common.collect.CollectSpliterators                                   : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap$AsMap                        : loaded 2 times (x 124B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap$WrappedList                  : loaded 2 times (x 201B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 65B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 78B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class com.google.common.util.concurrent.AbstractFuture$SynchronizedHelper             : loaded 2 times (x 74B)
Class com.google.common.graph.SuccessorsFunction                                      : loaded 2 times (x 66B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class org.apache.commons.io.FileUtils                                                 : loaded 2 times (x 67B)
Class com.google.common.io.Files$FileByteSink                                         : loaded 2 times (x 73B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 75B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 76B)
Class com.google.common.io.Closer                                                     : loaded 2 times (x 74B)
Class com.google.common.io.Java8Compatibility                                         : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$Ascii                                        : loaded 2 times (x 108B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 2 times (x 203B)
Class com.google.common.collect.ImmutableSet                                          : loaded 2 times (x 141B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 75B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class com.google.common.io.Closer$Suppressor                                          : loaded 2 times (x 66B)
Class com.google.common.io.LineProcessor                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultisetGwtSerializationDependencies         : loaded 2 times (x 121B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 2 times (x 108B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class com.google.common.collect.ImmutableList$SerializedForm                          : loaded 2 times (x 69B)
Class com.google.common.util.concurrent.AbstractFuture$Waiter                         : loaded 2 times (x 68B)
Class org.gradle.internal.Cast                                                        : loaded 2 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 2 times (x 66B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 2 times (x 107B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMap                                          : loaded 2 times (x 116B)
Class com.google.common.base.Predicate                                                : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 75B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 2 times (x 109B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class com.google.common.cache.CacheBuilder$3                                          : loaded 2 times (x 68B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 2 times (x 78B)
Class org.gradle.api.GradleException                                                  : loaded 2 times (x 78B)
Class org.apache.commons.io.StandardLineSeparator                                     : loaded 2 times (x 75B)
Class com.google.common.base.Suppliers$MemoizingSupplier                              : loaded 2 times (x 75B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 67B)
Class [Lcom.google.common.io.FileWriteMode;                                           : loaded 2 times (x 65B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 66B)
Class com.google.common.collect.Iterables$4                                           : loaded 2 times (x 77B)
Class com.google.common.io.Files                                                      : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class com.google.common.base.CharMatcher$Whitespace                                   : loaded 2 times (x 108B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class com.google.common.base.Splitter$1$1                                             : loaded 2 times (x 82B)
Class org.gradle.internal.IoActions                                                   : loaded 2 times (x 67B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class com.google.common.collect.FluentIterable                                        : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class com.google.common.base.Suppliers$NonSerializableMemoizingSupplier               : loaded 2 times (x 75B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 102B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class com.google.common.collect.RegularImmutableMap$BucketOverflowException           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableMultimap$1                                   : loaded 2 times (x 78B)
Class com.google.common.collect.AbstractMapBasedMultimap$RandomAccessWrappedList      : loaded 2 times (x 201B)
Class com.google.common.collect.AbstractIterator$State                                : loaded 2 times (x 75B)
Class com.google.common.collect.ImmutableMultimap$2                                   : loaded 2 times (x 77B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 145B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 2 times (x 107B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 2 times (x 109B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 67B)
Class com.google.common.base.Preconditions                                            : loaded 2 times (x 67B)
Class [Lorg.apache.commons.io.file.DeleteOption;                                      : loaded 2 times (x 65B)
Class com.google.common.io.CharSink                                                   : loaded 2 times (x 75B)
Class com.google.common.base.Joiner$1                                                 : loaded 2 times (x 76B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class com.google.common.collect.PeekingIterator                                       : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher                                              : loaded 2 times (x 107B)
Class com.google.common.collect.Maps$IteratorBasedAbstractMap                         : loaded 2 times (x 121B)
Class com.google.common.base.Joiner$2                                                 : loaded 2 times (x 75B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 101B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$1                                            : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 2 times (x 108B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 86B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 2 times (x 148B)
Class com.google.common.collect.CollectSpliterators$1                                 : loaded 2 times (x 86B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 2 times (x 107B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class org.apache.commons.io.output.AppendableWriter                                   : loaded 2 times (x 96B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class org.apache.commons.io.output.ThresholdingOutputStream                           : loaded 2 times (x 91B)
Class com.google.common.collect.ImmutableMultimap$Builder                             : loaded 2 times (x 82B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableList                                         : loaded 2 times (x 202B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableMultimap$EntryCollection                     : loaded 2 times (x 124B)
Class com.google.common.collect.Maps$EntrySet                                         : loaded 2 times (x 132B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 2 times (x 72B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.google.common.collect.ForwardingObject                                      : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 2 times (x 205B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class com.google.common.io.ByteSource                                                 : loaded 2 times (x 80B)
Class com.google.common.util.concurrent.AbstractFuture$SetFuture                      : loaded 2 times (x 71B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class com.google.common.io.LineBuffer                                                 : loaded 2 times (x 71B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 2 times (x 77B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 2 times (x 145B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class com.google.common.collect.Iterables                                             : loaded 2 times (x 67B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 66B)

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 0 days 9:04 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (3867M free)
TotalPageFile size 22476M (AvailPageFile size 6M)
current process WorkingSet (physical memory assigned to process): 643M, peak: 643M
current process commit charge ("private bytes"): 680M, peak: 682M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109) for windows-amd64 JRE (21.0.6+-13368085-b895.109), built on 2025-04-16T17:01:31Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
