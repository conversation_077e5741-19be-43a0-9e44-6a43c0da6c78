<?php
require_once 'config/database.php';
require_once 'classes/Auth.php';
require_once 'classes/TextContentManager.php';

$auth = new Auth();

// Check if user is logged in
if (!$auth->isLoggedIn()) {
    header('Location: login.php');
    exit();
}

$textManager = new TextContentManager();
$user = $auth->getCurrentUser();

// Handle actions
$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';

    switch ($action) {
        case 'delete':
            $id = $_POST['id'] ?? 0;
            if ($id) {
                $result = $textManager->deleteTextContent($id);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'error';
            }
            break;

        case 'toggle_status':
            $id = $_POST['id'] ?? 0;
            if ($id) {
                $result = $textManager->toggleActiveStatus($id);
                $message = $result['message'];
                $message_type = $result['success'] ? 'success' : 'error';
            }
            break;
    }
}

// Get filter parameters
$category_filter = $_GET['category'] ?? '';
$search = $_GET['search'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$per_page = 20;

// Get data
$categories = $textManager->getCategories();
$content = $textManager->getTextContent($category_filter, $page, $per_page, $search);
$total_count = $textManager->getTextContentCount($category_filter, $search);
$total_pages = ceil($total_count / $per_page);

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Text Content Management</h3>
                    <div>
                        <a href="categories.php" class="btn btn-secondary">
                            <i class="fas fa-tags"></i> Manage Categories
                        </a>
                        <a href="text-content-form.php" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add New Content
                        </a>
                    </div>
                </div>

                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-<?php echo $message_type == 'success' ? 'success' : 'danger'; ?> alert-dismissible fade show">
                            <?php echo htmlspecialchars($message); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Filters -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <form method="GET" class="d-flex">
                                <select name="category" class="form-select me-2" onchange="this.form.submit()">
                                    <option value="">All Categories</option>
                                    <?php foreach ($categories as $category): ?>
                                        <option value="<?php echo $category['id']; ?>"
                                                <?php echo $category_filter == $category['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($category['display_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <input type="hidden" name="search" value="<?php echo htmlspecialchars($search); ?>">
                            </form>
                        </div>
                        <div class="col-md-6">
                            <form method="GET" class="d-flex">
                                <input type="text" name="search" class="form-control me-2"
                                       placeholder="Search messages..."
                                       value="<?php echo htmlspecialchars($search); ?>">
                                <button type="submit" class="btn btn-outline-secondary">Search</button>
                                <input type="hidden" name="category" value="<?php echo htmlspecialchars($category_filter); ?>">
                            </form>
                        </div>
                    </div>

                    <!-- Statistics -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <strong>Total Content:</strong> <?php echo $total_count; ?> messages
                                <?php if ($category_filter): ?>
                                    in selected category
                                <?php endif; ?>
                                <?php if ($search): ?>
                                    matching "<?php echo htmlspecialchars($search); ?>"
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <!-- Content Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Category</th>
                                    <th>Message</th>
                                    <th>Status</th>
                                    <th>Sort Order</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($content)): ?>
                                    <tr>
                                        <td colspan="7" class="text-center">No content found</td>
                                    </tr>
                                <?php else: ?>
                                    <?php foreach ($content as $item): ?>
                                        <tr>
                                            <td><?php echo $item['id']; ?></td>
                                            <td>
                                                <span class="badge bg-secondary">
                                                    <?php echo htmlspecialchars($item['category_name']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div style="max-width: 300px; overflow: hidden; text-overflow: ellipsis;">
                                                    <?php echo htmlspecialchars(substr($item['message'], 0, 100)); ?>
                                                    <?php if (strlen($item['message']) > 100): ?>...<?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $item['is_active'] ? 'success' : 'danger'; ?>">
                                                    <?php echo $item['is_active'] ? 'Active' : 'Inactive'; ?>
                                                </span>
                                            </td>
                                            <td><?php echo $item['sort_order']; ?></td>
                                            <td><?php echo date('M j, Y', strtotime($item['created_at'])); ?></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <a href="text-content-form.php?id=<?php echo $item['id']; ?>"
                                                       class="btn btn-outline-primary" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>

                                                    <form method="POST" style="display: inline;"
                                                          onsubmit="return confirm('Toggle status for this content?')">
                                                        <input type="hidden" name="action" value="toggle_status">
                                                        <input type="hidden" name="id" value="<?php echo $item['id']; ?>">
                                                        <button type="submit" class="btn btn-outline-warning" title="Toggle Status">
                                                            <i class="fas fa-toggle-<?php echo $item['is_active'] ? 'on' : 'off'; ?>"></i>
                                                        </button>
                                                    </form>

                                                    <form method="POST" style="display: inline;"
                                                          onsubmit="return confirm('Are you sure you want to delete this content?')">
                                                        <input type="hidden" name="action" value="delete">
                                                        <input type="hidden" name="id" value="<?php echo $item['id']; ?>">
                                                        <button type="submit" class="btn btn-outline-danger" title="Delete">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="Page navigation">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page - 1; ?>&category=<?php echo urlencode($category_filter); ?>&search=<?php echo urlencode($search); ?>">Previous</a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?>&category=<?php echo urlencode($category_filter); ?>&search=<?php echo urlencode($search); ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo $page + 1; ?>&category=<?php echo urlencode($category_filter); ?>&search=<?php echo urlencode($search); ?>">Next</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
