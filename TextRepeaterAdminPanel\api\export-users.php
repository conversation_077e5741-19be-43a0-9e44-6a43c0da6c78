<?php
/**
 * Export Users API
 * Exports app users data to CSV format
 */

// Include required files
require_once '../config/database.php';

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();

    if (!$conn) {
        throw new Exception('Database connection failed');
    }

    // Get filter parameters (same as app-users.php)
    $search = $_GET['search'] ?? '';
    $status_filter = $_GET['status'] ?? '';
    $date_from = $_GET['date_from'] ?? '';
    $date_to = $_GET['date_to'] ?? '';

    // Build WHERE clause
    $where_conditions = [];
    $params = [];

    if (!empty($search)) {
        $where_conditions[] = "(device_id LIKE :search OR device_model LIKE :search OR device_brand LIKE :search)";
        $params[':search'] = '%' . $search . '%';
    }

    if (!empty($status_filter)) {
        if ($status_filter === 'active') {
            $where_conditions[] = "last_seen >= DATE_SUB(NOW(), INTERVAL 7 DAY)";
        } elseif ($status_filter === 'inactive') {
            $where_conditions[] = "last_seen < DATE_SUB(NOW(), INTERVAL 7 DAY)";
        }
    }

    if (!empty($date_from)) {
        $where_conditions[] = "DATE(created_at) >= :date_from";
        $params[':date_from'] = $date_from;
    }

    if (!empty($date_to)) {
        $where_conditions[] = "DATE(created_at) <= :date_to";
        $params[':date_to'] = $date_to;
    }

    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

    // Query to get users
    $query = "
        SELECT
            device_id,
            app_version,
            android_version,
            device_model,
            device_brand,
            screen_resolution,
            language,
            timezone,
            total_sessions,
            notifications_enabled,
            created_at,
            last_seen,
            CASE
                WHEN last_seen >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 'active'
                ELSE 'inactive'
            END as status
        FROM app_users
        $where_clause
        ORDER BY last_seen DESC
    ";

    $stmt = $conn->prepare($query);
    foreach ($params as $key => $value) {
        $stmt->bindValue($key, $value);
    }
    $stmt->execute();
    $users = $stmt->fetchAll();

    // Set headers for CSV download
    $filename = 'app_users_' . date('Y-m-d_H-i-s') . '.csv';
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');

    // Create file pointer connected to the output stream
    $output = fopen('php://output', 'w');

    // Add CSV headers
    $headers = [
        'Device ID',
        'App Version',
        'Android Version',
        'Device Model',
        'Device Brand',
        'Screen Resolution',
        'Language',
        'Timezone',
        'Total Sessions',
        'Notifications Enabled',
        'Status',
        'First Seen',
        'Last Seen'
    ];

    fputcsv($output, $headers);

    // Add data rows
    foreach ($users as $user) {
        $row = [
            $user['device_id'],
            $user['app_version'] ?: 'Unknown',
            $user['android_version'] ?: 'Unknown',
            $user['device_model'] ?: 'Unknown',
            $user['device_brand'] ?: 'Unknown',
            $user['screen_resolution'] ?: 'Unknown',
            $user['language'] ?: 'en',
            $user['timezone'] ?: 'UTC',
            $user['total_sessions'],
            $user['notifications_enabled'] ? 'Yes' : 'No',
            ucfirst($user['status']),
            $user['created_at'],
            $user['last_seen']
        ];

        fputcsv($output, $row);
    }

    // Close the file pointer
    fclose($output);

} catch (Exception $e) {
    // Log error
    error_log("Export users error: " . $e->getMessage());

    // Return error page
    header('Content-Type: text/html');
    echo "<h1>Export Error</h1>";
    echo "<p>An error occurred while exporting users: " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><a href='../app-users.php'>Go back to App Users</a></p>";
}
?>
