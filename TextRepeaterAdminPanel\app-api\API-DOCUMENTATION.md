# Text Repeater App API Documentation

## 🚀 Status: FULLY WORKING ✅

## Base URL
```
http://*************/monirulvitextrepeater/TextRepeaterAdminPanel/app-api/index.php
```

## URL Format
Since URL rewriting is disabled, use this format:
```
{BASE_URL}?path={ENDPOINT}
```

## 🧪 Test Pages
- **Working Test Page**: [working-test.php](http://*************/monirulvitextrepeater/TextRepeaterAdminPanel/app-api/working-test.php)
- **Database Setup**: [setup-database.php](http://*************/monirulvitextrepeater/TextRepeaterAdminPanel/app-api/setup-database.php)

## Authentication
- Currently no API key required
- Rate limiting: 1000 requests per hour per IP

## Response Format

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2025-01-01T12:00:00+00:00",
  "version": "1.0.0"
}
```

### Error Response
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": 400
  },
  "timestamp": "2025-01-01T12:00:00+00:00",
  "version": "1.0.0"
}
```

## API Endpoints

### 1. Health Check
**GET** `?path=health`

Returns API health status.

**Response:**
```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "timestamp": "2025-01-01T12:00:00",
    "version": "1.0.0"
  }
}
```

### 2. Version Info
**GET** `?path=version`

Returns API version and available endpoints.

### 3. AdMob Configuration
**GET** `?path=ads/config`

Returns active AdMob configuration for the app.

**Response:**
```json
{
  "success": true,
  "data": {
    "app_id": "ca-app-pub-xxxxxxxxxxxxxxxx~xxxxxxxxxx",
    "banner_id": "ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx",
    "interstitial_id": "ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx",
    "app_open_id": "ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx",
    "is_active": 1,
    "test_mode": 0,
    "ad_frequency_minutes": 5,
    "max_ads_per_session": 10
  }
}
```

### 4. Track Ad Events
**POST** `?path=ads/track`

Track ad impressions, clicks, and revenue.

**Request Body:**
```json
{
  "device_id": "unique-device-id",
  "ad_type": "banner|interstitial|app_open",
  "event_type": "impression|click|revenue",
  "ad_unit_id": "ca-app-pub-xxxxxxxxxxxxxxxx/xxxxxxxxxx",
  "revenue": 0.01,
  "currency": "USD",
  "session_id": "session-id",
  "app_version": "1.0.0",
  "placement": "main_screen",
  "network": "admob"
}
```

### 5. User Registration
**POST** `?path=users/register`

Register or update user information.

**Request Body:**
```json
{
  "device_id": "unique-device-id",
  "fcm_token": "firebase-token",
  "app_version": "1.0.0",
  "android_version": "11",
  "device_model": "Samsung Galaxy S21",
  "device_brand": "Samsung",
  "screen_resolution": "1080x2400",
  "language": "en",
  "timezone": "UTC"
}
```

### 6. User Profile
**GET** `?path=users/profile&device_id={DEVICE_ID}`

Get user profile information.

### 7. App Settings
**GET** `?path=settings/settings`

Get public app settings.

### 8. App Configuration
**GET** `?path=settings/config`

Get app configuration including features and limits.

### 9. Notification Delivered
**POST** `?path=notifications/delivered`

Mark notification as delivered.

**Request Body:**
```json
{
  "notification_id": 123,
  "device_id": "unique-device-id"
}
```

### 10. Notification Clicked
**POST** `?path=notifications/clicked`

Track notification clicks.

**Request Body:**
```json
{
  "notification_id": 123,
  "device_id": "unique-device-id"
}
```

## Android Integration Example

### Retrofit Interface (Java/Kotlin)
```kotlin
interface TextRepeaterAPI {
    @GET("index.php?path=health")
    suspend fun getHealth(): Response<ApiResponse<HealthData>>

    @GET("index.php?path=ads/config")
    suspend fun getAdConfig(): Response<ApiResponse<AdConfig>>

    @POST("index.php?path=ads/track")
    suspend fun trackAdEvent(@Body event: AdEvent): Response<ApiResponse<Any>>

    @POST("index.php?path=users/register")
    suspend fun registerUser(@Body user: UserData): Response<ApiResponse<Any>>

    @GET("index.php?path=settings/config")
    suspend fun getAppConfig(): Response<ApiResponse<AppConfig>>
}
```

### Base URL Configuration
```kotlin
const val BASE_URL = "http://*************/monirulvitextrepeater/TextRepeaterAdminPanel/app-api/"
```

## Error Codes
- **200**: Success
- **400**: Bad Request (validation error)
- **401**: Unauthorized
- **404**: Not Found
- **405**: Method Not Allowed
- **429**: Rate Limit Exceeded
- **500**: Internal Server Error

## Testing
Use the working test page: `working-test.php`

## Notes
- Replace `*************` with your actual server IP/domain
- All timestamps are in ISO 8601 format
- Device ID should be unique and persistent per device
- FCM tokens should be updated when they change
