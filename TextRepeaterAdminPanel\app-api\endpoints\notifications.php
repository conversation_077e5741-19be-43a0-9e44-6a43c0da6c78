<?php
/**
 * Notifications Endpoint Handler
 * Handles all push notification related API requests
 */

// Check if NotificationManager class is already loaded, if not load it
if (!class_exists('NotificationManager')) {
    require_once __DIR__ . '/../../classes/NotificationManager.php';
}

class NotificationsEndpoint {
    private $conn;
    private $response;
    private $notificationManager;

    public function __construct() {
        $this->conn = getAPIDatabase();
        $this->response = new APIResponse();
        $this->notificationManager = new NotificationManager();
    }

    /**
     * Handle notifications endpoint requests
     */
    public function handle($method, $pathParts) {
        $action = $pathParts[1] ?? '';

        switch ($action) {
            case 'delivered':
                return $this->handleDelivered($method);

            case 'clicked':
                return $this->handleClicked($method);

            case 'register':
                return $this->handleTokenRegistration($method);

            case 'unregister':
                return $this->handleTokenUnregistration($method);

            case 'history':
                return $this->handleHistory($method);

            default:
                return $this->response->notFound('Notifications endpoint');
        }
    }

    /**
     * Handle notification delivered tracking
     */
    private function handleDelivered($method) {
        if ($method !== 'POST') {
            return $this->response->error('Method not allowed', 405);
        }

        $input = $this->response->getJsonInput();
        if (!$input) {
            return $this->response->error('Invalid JSON input', 400);
        }

        $requiredFields = ['notification_id', 'device_id'];
        $errors = $this->response->validateRequired($input, $requiredFields);

        if (!empty($errors)) {
            return $this->response->validationError($errors);
        }

        try {
            $query = "UPDATE notification_delivery SET
                     status = 'delivered',
                     delivered_at = NOW(),
                     updated_at = NOW()
                     WHERE notification_id = :notification_id AND device_id = :device_id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':notification_id', $input['notification_id']);
            $stmt->bindParam(':device_id', $input['device_id']);

            if ($stmt->execute()) {
                // Update notification delivery count
                $this->updateNotificationStats($input['notification_id'], 'delivered');

                return $this->response->success(['message' => 'Delivery status updated']);
            } else {
                return $this->response->serverError('Failed to update delivery status');
            }

        } catch (Exception $e) {
            return $this->response->serverError('Database error occurred');
        }
    }

    /**
     * Handle notification clicked tracking
     */
    private function handleClicked($method) {
        if ($method !== 'POST') {
            return $this->response->error('Method not allowed', 405);
        }

        $input = $this->response->getJsonInput();
        if (!$input) {
            return $this->response->error('Invalid JSON input', 400);
        }

        $requiredFields = ['notification_id', 'device_id'];
        $errors = $this->response->validateRequired($input, $requiredFields);

        if (!empty($errors)) {
            return $this->response->validationError($errors);
        }

        try {
            $query = "UPDATE notification_delivery SET
                     status = 'clicked',
                     clicked_at = NOW(),
                     updated_at = NOW()
                     WHERE notification_id = :notification_id AND device_id = :device_id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':notification_id', $input['notification_id']);
            $stmt->bindParam(':device_id', $input['device_id']);

            if ($stmt->execute()) {
                // Update notification click count
                $this->updateNotificationStats($input['notification_id'], 'clicked');

                return $this->response->success(['message' => 'Click tracked successfully']);
            } else {
                return $this->response->serverError('Failed to track click');
            }

        } catch (Exception $e) {
            return $this->response->serverError('Database error occurred');
        }
    }

    /**
     * Handle FCM token registration
     */
    private function handleTokenRegistration($method) {
        if ($method !== 'POST') {
            return $this->response->error('Method not allowed', 405);
        }

        $input = $this->response->getJsonInput();
        if (!$input) {
            return $this->response->error('Invalid JSON input', 400);
        }

        $requiredFields = ['device_id', 'fcm_token'];
        $errors = $this->response->validateRequired($input, $requiredFields);

        if (!empty($errors)) {
            return $this->response->validationError($errors);
        }

        try {
            // Update user's FCM token
            $query = "UPDATE app_users SET
                     fcm_token = :fcm_token,
                     notifications_enabled = 1,
                     updated_at = NOW()
                     WHERE device_id = :device_id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':fcm_token', $input['fcm_token']);
            $stmt->bindParam(':device_id', $input['device_id']);

            if ($stmt->execute()) {
                return $this->response->success(['message' => 'FCM token registered successfully']);
            } else {
                return $this->response->serverError('Failed to register FCM token');
            }

        } catch (Exception $e) {
            return $this->response->serverError('Database error occurred');
        }
    }

    /**
     * Handle FCM token unregistration
     */
    private function handleTokenUnregistration($method) {
        if ($method !== 'POST') {
            return $this->response->error('Method not allowed', 405);
        }

        $input = $this->response->getJsonInput();
        if (!$input) {
            return $this->response->error('Invalid JSON input', 400);
        }

        if (empty($input['device_id'])) {
            return $this->response->error('Device ID is required', 400);
        }

        try {
            $query = "UPDATE app_users SET
                     fcm_token = '',
                     notifications_enabled = 0,
                     updated_at = NOW()
                     WHERE device_id = :device_id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':device_id', $input['device_id']);

            if ($stmt->execute()) {
                return $this->response->success(['message' => 'FCM token unregistered successfully']);
            } else {
                return $this->response->serverError('Failed to unregister FCM token');
            }

        } catch (Exception $e) {
            return $this->response->serverError('Database error occurred');
        }
    }

    /**
     * Handle notification history requests
     */
    private function handleHistory($method) {
        if ($method !== 'GET') {
            return $this->response->error('Method not allowed', 405);
        }

        $deviceId = $_GET['device_id'] ?? '';
        $limit = min(intval($_GET['limit'] ?? 20), 100); // Max 100 notifications
        $offset = intval($_GET['offset'] ?? 0);

        if (empty($deviceId)) {
            return $this->response->error('Device ID is required', 400);
        }

        try {
            $query = "SELECT
                        n.id,
                        n.title,
                        n.message,
                        n.image_url,
                        n.action_url,
                        nd.status,
                        nd.delivered_at,
                        nd.clicked_at,
                        n.created_at
                     FROM notifications n
                     JOIN notification_delivery nd ON n.id = nd.notification_id
                     WHERE nd.device_id = :device_id
                     ORDER BY n.created_at DESC
                     LIMIT :limit OFFSET :offset";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':device_id', $deviceId);
            $stmt->bindParam(':limit', $limit, PDO::PARAM_INT);
            $stmt->bindParam(':offset', $offset, PDO::PARAM_INT);
            $stmt->execute();

            $notifications = $stmt->fetchAll();

            return $this->response->success([
                'notifications' => $notifications,
                'pagination' => [
                    'limit' => $limit,
                    'offset' => $offset,
                    'total' => count($notifications)
                ]
            ]);

        } catch (Exception $e) {
            return $this->response->serverError('Failed to get notification history');
        }
    }

    /**
     * Update notification statistics
     */
    private function updateNotificationStats($notificationId, $action) {
        try {
            $field = $action === 'clicked' ? 'total_clicked' : 'total_delivered';

            $query = "UPDATE notifications SET
                     $field = $field + 1,
                     updated_at = NOW()
                     WHERE id = :notification_id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':notification_id', $notificationId);
            $stmt->execute();

        } catch (Exception $e) {
            error_log("Failed to update notification stats: " . $e->getMessage());
        }
    }
}
?>
