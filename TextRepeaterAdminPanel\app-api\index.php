<?php
/**
 * Text Repeater App API
 * Main API Router and Entry Point
 *
 * This file handles all incoming API requests and routes them to appropriate endpoints
 */

// Set headers for API responses
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization, X-API-Key');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Include configuration and dependencies
require_once 'config/api-config.php';
require_once 'middleware/auth.php';
require_once 'utils/response.php';
require_once 'utils/cache.php';

// Include classes if they exist
$classFiles = [
    '../classes/TextContentManager.php',
    '../classes/AdMobManager.php',
    '../classes/NotificationManager.php'
];

foreach ($classFiles as $classFile) {
    if (file_exists($classFile)) {
        require_once $classFile;
    }
}

// Include endpoint handlers
require_once 'endpoints/ads.php';
require_once 'endpoints/users.php';
require_once 'endpoints/settings.php';
require_once 'endpoints/notifications.php';
require_once 'endpoints/text-content.php';

class AppAPI {
    private $auth;
    private $response;

    public function __construct() {
        $this->auth = new APIAuth();
        $this->response = new APIResponse();
    }

    /**
     * Main request handler with caching and performance optimizations
     */
    public function handleRequest() {
        try {
            // Enable gzip compression for better performance
            enableGzipCompression();

            // Get request method and path
            $method = $_SERVER['REQUEST_METHOD'];
            $path = $this->getRequestPath();

            // Log the request for debugging
            $this->logRequest($method, $path);

            // Check cache for GET requests
            if ($method === 'GET' && $this->isCacheable($path)) {
                $cacheKey = generateAPICacheKey($path, $_GET);
                $cache = getAPICache();

                $cachedResponse = $cache->get($cacheKey);
                if ($cachedResponse !== null) {
                    // Add cache headers
                    addCacheHeaders(300); // 5 minutes
                    header('X-Cache: HIT');
                    return $cachedResponse;
                }
            }

            // Basic API key validation for certain endpoints
            if ($this->requiresAuth($path) && !$this->auth->validateRequest()) {
                return $this->response->error('Unauthorized access', 401);
            }

            // Route the request to appropriate handler
            $response = $this->routeRequest($method, $path);

            // Cache successful GET responses
            if ($method === 'GET' && $this->isCacheable($path) && isset($cacheKey)) {
                $cache->set($cacheKey, $response, $this->getCacheTTL($path));
                header('X-Cache: MISS');
            }

            return $response;

        } catch (Exception $e) {
            error_log("API Error: " . $e->getMessage());
            return $this->response->error('Internal server error', 500);
        }
    }

    /**
     * Get the request path from URL
     */
    private function getRequestPath() {
        $path = $_GET['path'] ?? '';
        return trim($path, '/');
    }

    /**
     * Route requests to appropriate endpoint handlers
     */
    private function routeRequest($method, $path) {
        $pathParts = explode('/', $path);
        $endpoint = $pathParts[0] ?? '';

        switch ($endpoint) {
            case 'ads':
                $adsHandler = new AdsEndpoint();
                return $adsHandler->handle($method, $pathParts);

            case 'users':
            case 'user':
                $usersHandler = new UsersEndpoint();
                return $usersHandler->handle($method, $pathParts);

            case 'settings':
            case 'app':
                $settingsHandler = new SettingsEndpoint();
                return $settingsHandler->handle($method, $pathParts);

            case 'notifications':
            case 'notification':
                $notificationsHandler = new NotificationsEndpoint();
                return $notificationsHandler->handle($method, $pathParts);

            case 'text-content':
            case 'text':
            case 'content':
                $textContentHandler = new TextContentEndpoint();
                return $textContentHandler->handle($method, $pathParts);

            case 'health':
                return $this->healthCheck();

            case 'version':
                return $this->getVersion();

            default:
                return $this->response->error('Endpoint not found', 404);
        }
    }

    /**
     * Check if endpoint requires authentication
     */
    private function requiresAuth($path) {
        $publicEndpoints = [
            'health',
            'version',
            'ads/config',
            'app/settings',
            'users/register',
            'text-content',
            'text-content/json',
            'text-content/all',
            'text-content/category',
            'text-content/sync',
            'text-content/stats'
        ];

        return !in_array($path, $publicEndpoints);
    }

    /**
     * Log API requests for monitoring
     */
    private function logRequest($method, $path) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'method' => $method,
            'path' => $path,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
        ];

        error_log("API Request: " . json_encode($logData));
    }

    /**
     * Health check endpoint
     */
    private function healthCheck() {
        return $this->response->success([
            'status' => 'healthy',
            'timestamp' => date('Y-m-d H:i:s'),
            'version' => API_VERSION
        ]);
    }

    /**
     * Get API version
     */
    private function getVersion() {
        return $this->response->success([
            'version' => API_VERSION,
            'name' => 'Text Repeater App API',
            'endpoints' => [
                'ads' => 'AdMob configuration and tracking',
                'users' => 'User registration and management',
                'settings' => 'App settings and configuration',
                'notifications' => 'Push notification tracking',
                'text-content' => 'Text content management (romantic, sad, funny messages)'
            ]
        ]);
    }

    /**
     * Check if endpoint is cacheable
     */
    private function isCacheable($path) {
        // NEVER cache sync endpoints - always return fresh data
        $nonCacheableEndpoints = [
            'text-content/sync',
            'text/sync',
            'content/sync'
        ];

        foreach ($nonCacheableEndpoints as $endpoint) {
            if (strpos($path, $endpoint) === 0) {
                return false;
            }
        }

        $cacheableEndpoints = [
            'ads/config',
            'settings',
            'app',
            'text-content',
            'text',
            'content',
            'health',
            'version'
        ];

        foreach ($cacheableEndpoints as $endpoint) {
            if (strpos($path, $endpoint) === 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get cache TTL for specific endpoints
     */
    private function getCacheTTL($path) {
        $cacheTTLs = [
            'ads/config' => 300,      // 5 minutes
            'settings' => 300,        // 5 minutes
            'app' => 300,            // 5 minutes
            'text-content' => 600,    // 10 minutes
            'text' => 600,           // 10 minutes
            'content' => 600,        // 10 minutes
            'health' => 60,          // 1 minute
            'version' => 3600        // 1 hour
        ];

        foreach ($cacheTTLs as $endpoint => $ttl) {
            if (strpos($path, $endpoint) === 0) {
                return $ttl;
            }
        }

        return 300; // Default 5 minutes
    }
}

// Initialize and handle the request
$api = new AppAPI();
echo $api->handleRequest();
?>
