#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 24117248 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=18088, tid=2380
#
# JRE version: OpenJDK Runtime Environment (21.0.6) (build 21.0.6+-13368085-b895.109)
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\agents\gradle-instrumentation-agent-8.13.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.13

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Thu Jun  5 04:54:31 2025 Bangladesh Standard Time elapsed time: 15.141598 seconds (0d 0h 0m 15s)

---------------  T H R E A D  ---------------

Current thread (0x000001951f3ca700):  VMThread "VM Thread"          [id=2380, stack(0x0000003690c00000,0x0000003690d00000) (1024K)]

Stack: [0x0000003690c00000,0x0000003690d00000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d0639]
V  [jvm.dll+0x85eb03]
V  [jvm.dll+0x86105e]
V  [jvm.dll+0x861743]
V  [jvm.dll+0x27e6e6]
V  [jvm.dll+0x6ccfd5]
V  [jvm.dll+0x6c0a8a]
V  [jvm.dll+0x35537b]
V  [jvm.dll+0x35cfd6]
V  [jvm.dll+0x3aef86]
V  [jvm.dll+0x3af258]
V  [jvm.dll+0x327a2c]
V  [jvm.dll+0x32a436]
V  [jvm.dll+0x33438d]
V  [jvm.dll+0x36ba15]
V  [jvm.dll+0x8674c8]
V  [jvm.dll+0x868844]
V  [jvm.dll+0x868d80]
V  [jvm.dll+0x869013]
V  [jvm.dll+0x806ed8]
V  [jvm.dll+0x6cef0d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x00000036907ff2f0): G1PauseRemark, mode: safepoint, requested by thread 0x000001951c44b4c0


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x0000019529675400, length=135, elements={
0x000001950780dfd0, 0x000001951f5cb320, 0x000001951f5cc2a0, 0x000001951f5ce890,
0x000001951f5d13e0, 0x000001951f5d1eb0, 0x000001951f5d2980, 0x000001951f5d37b0,
0x000001951f603350, 0x000001951fa4c690, 0x00000195077f6100, 0x00000195252bf870,
0x00000195256457c0, 0x0000019524130aa0, 0x000001951fa85590, 0x000001951fa85c60,
0x000001951fa86330, 0x000001951fa86a00, 0x000001951fa847f0, 0x000001951fa83380,
0x000001951fa83a50, 0x0000019526222020, 0x00000195262293f0, 0x00000195262226f0,
0x0000019526226b10, 0x00000195262278b0, 0x0000019528266160, 0x0000019528268a40,
0x00000195282653c0, 0x0000019528265a90, 0x0000019528264cf0, 0x0000019528266830,
0x0000019528266f00, 0x0000019528269110, 0x00000195282675d0, 0x00000195282631b0,
0x0000019528262410, 0x0000019526224230, 0x0000019526224900, 0x0000019526224fd0,
0x0000019528eae130, 0x0000019528eaeed0, 0x0000019528eb0a10, 0x0000019528eaccc0,
0x0000019528eab850, 0x0000019528ead390, 0x0000019528eabf20, 0x0000019528eaa3e0,
0x0000019528eada60, 0x0000019528eaaab0, 0x0000019526228650, 0x0000019526228d20,
0x0000019526222dc0, 0x0000019526223490, 0x00000195277790e0, 0x000001952777ce30,
0x000001952777d500, 0x00000195277797b0, 0x0000019528eaf5a0, 0x0000019528eac5f0,
0x0000019528eae800, 0x0000019528eafc70, 0x0000019528eb0340, 0x0000019528eb17b0,
0x00000195277775a0, 0x000001952777b9c0, 0x0000019527778a10, 0x000001952777b2f0,
0x000001952777ac20, 0x000001952777c090, 0x0000019527779e80, 0x000001952777a550,
0x000001952777c760, 0x000001952777e2a0, 0x0000019527776ed0, 0x0000019527778340,
0x0000019526223b60, 0x00000195262256a0, 0x0000019526225d70, 0x000001952b40d3d0,
0x0000019528d4c4b0, 0x000001952cea5590, 0x000001952cfe53d0, 0x0000019528d48760,
0x000001952cce97f0, 0x000001952cce2af0, 0x000001952cce5aa0, 0x000001952cce6840,
0x000001952cce31c0, 0x000001952cce3890, 0x000001952b40daa0, 0x000001952b408fb0,
0x000001952b4066d0, 0x000001952ad99f30, 0x0000019528677790, 0x000001952867a740,
0x000001952867d6f0, 0x000001952867ddc0, 0x0000019528684ac0, 0x0000019528685860,
0x000001952867eb60, 0x000001952cea6330, 0x000001952cea03d0, 0x000001952cea1170,
0x000001952cea0aa0, 0x000001952cea1840, 0x000001952cea3a50, 0x000001952cea25e0,
0x000001952cea47f0, 0x000001952cea3380, 0x000001952cea1f10, 0x000001952cea2cb0,
0x000001952cea4ec0, 0x000001952cea4120, 0x000001952cea5c60, 0x000001952cea6a00,
0x000001952ce9f630, 0x0000019528678c00, 0x000001952867bbb0, 0x000001952867c950,
0x00000195286799a0, 0x0000019528676320, 0x000001952867f230, 0x000001952867f900,
0x00000195286806a0, 0x0000019528680d70, 0x0000019528681440, 0x000001952867ffd0,
0x000001952867a070, 0x000001952867b4e0, 0x0000019528681b10, 0x0000019528683d20,
0x00000195286821e0, 0x00000195286843f0, 0x00000195286828b0
}

Java Threads: ( => current thread )
  0x000001950780dfd0 JavaThread "main"                              [_thread_blocked, id=19196, stack(0x0000003690500000,0x0000003690600000) (1024K)]
  0x000001951f5cb320 JavaThread "Reference Handler"          daemon [_thread_blocked, id=14280, stack(0x0000003690d00000,0x0000003690e00000) (1024K)]
  0x000001951f5cc2a0 JavaThread "Finalizer"                  daemon [_thread_blocked, id=2792, stack(0x0000003690e00000,0x0000003690f00000) (1024K)]
  0x000001951f5ce890 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=19092, stack(0x0000003690f00000,0x0000003691000000) (1024K)]
  0x000001951f5d13e0 JavaThread "Attach Listener"            daemon [_thread_blocked, id=18592, stack(0x0000003691000000,0x0000003691100000) (1024K)]
  0x000001951f5d1eb0 JavaThread "Service Thread"             daemon [_thread_blocked, id=1340, stack(0x0000003691100000,0x0000003691200000) (1024K)]
  0x000001951f5d2980 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=17236, stack(0x0000003691200000,0x0000003691300000) (1024K)]
  0x000001951f5d37b0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=14900, stack(0x0000003691300000,0x0000003691400000) (1024K)]
  0x000001951f603350 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=11376, stack(0x0000003691400000,0x0000003691500000) (1024K)]
  0x000001951fa4c690 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=9796, stack(0x0000003691500000,0x0000003691600000) (1024K)]
  0x00000195077f6100 JavaThread "Notification Thread"        daemon [_thread_blocked, id=18632, stack(0x0000003691600000,0x0000003691700000) (1024K)]
  0x00000195252bf870 JavaThread "Daemon health stats"               [_thread_blocked, id=18288, stack(0x0000003691700000,0x0000003691800000) (1024K)]
  0x00000195256457c0 JavaThread "Incoming local TCP Connector on port 54840"        [_thread_in_native, id=17500, stack(0x0000003692000000,0x0000003692100000) (1024K)]
  0x0000019524130aa0 JavaThread "Daemon periodic checks"            [_thread_blocked, id=19452, stack(0x0000003692100000,0x0000003692200000) (1024K)]
  0x000001951fa85590 JavaThread "Daemon"                            [_thread_blocked, id=18764, stack(0x0000003692200000,0x0000003692300000) (1024K)]
  0x000001951fa85c60 JavaThread "Handler for socket connection from /127.0.0.1:54840 to /127.0.0.1:54841"        [_thread_in_native, id=18788, stack(0x0000003692300000,0x0000003692400000) (1024K)]
  0x000001951fa86330 JavaThread "Cancel handler"                    [_thread_blocked, id=7560, stack(0x0000003692400000,0x0000003692500000) (1024K)]
  0x000001951fa86a00 JavaThread "Daemon worker"                     [_thread_blocked, id=10488, stack(0x0000003692500000,0x0000003692600000) (1024K)]
  0x000001951fa847f0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:54840 to /127.0.0.1:54841"        [_thread_blocked, id=18828, stack(0x0000003692600000,0x0000003692700000) (1024K)]
  0x000001951fa83380 JavaThread "Stdin handler"                     [_thread_blocked, id=10180, stack(0x0000003692700000,0x0000003692800000) (1024K)]
  0x000001951fa83a50 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=17068, stack(0x0000003692800000,0x0000003692900000) (1024K)]
  0x0000019526222020 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=17584, stack(0x0000003692a00000,0x0000003692b00000) (1024K)]
  0x00000195262293f0 JavaThread "File lock request listener"        [_thread_in_native, id=19036, stack(0x0000003692b00000,0x0000003692c00000) (1024K)]
  0x00000195262226f0 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.13\fileHashes)"        [_thread_blocked, id=3480, stack(0x0000003692c00000,0x0000003692d00000) (1024K)]
  0x0000019526226b10 JavaThread "Cache worker for file hash cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\8.13\fileHashes)"        [_thread_blocked, id=16648, stack(0x0000003692e00000,0x0000003692f00000) (1024K)]
  0x00000195262278b0 JavaThread "Cache worker for Build Output Cleanup Cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\buildOutputCleanup)"        [_thread_blocked, id=10428, stack(0x0000003692f00000,0x0000003693000000) (1024K)]
  0x0000019528266160 JavaThread "File watcher server"        daemon [_thread_in_native, id=9288, stack(0x0000003693000000,0x0000003693100000) (1024K)]
  0x0000019528268a40 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=11440, stack(0x0000003693100000,0x0000003693200000) (1024K)]
  0x00000195282653c0 JavaThread "jar transforms"                    [_thread_blocked, id=2312, stack(0x0000003693200000,0x0000003693300000) (1024K)]
  0x0000019528265a90 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=9996, stack(0x0000003693300000,0x0000003693400000) (1024K)]
  0x0000019528264cf0 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=18472, stack(0x0000003693400000,0x0000003693500000) (1024K)]
  0x0000019528266830 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=5112, stack(0x0000003693500000,0x0000003693600000) (1024K)]
  0x0000019528266f00 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=16252, stack(0x0000003693600000,0x0000003693700000) (1024K)]
  0x0000019528269110 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=14316, stack(0x0000003693700000,0x0000003693800000) (1024K)]
  0x00000195282675d0 JavaThread "jar transforms Thread 7"           [_thread_blocked, id=15188, stack(0x0000003693800000,0x0000003693900000) (1024K)]
  0x00000195282631b0 JavaThread "jar transforms Thread 8"           [_thread_blocked, id=6116, stack(0x0000003693900000,0x0000003693a00000) (1024K)]
  0x0000019528262410 JavaThread "Cache worker for checksums cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\8.13\checksums)"        [_thread_blocked, id=18668, stack(0x0000003693a00000,0x0000003693b00000) (1024K)]
  0x0000019526224230 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.13\fileContent)"        [_thread_blocked, id=18980, stack(0x0000003693b00000,0x0000003693c00000) (1024K)]
  0x0000019526224900 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.13\md-supplier)"        [_thread_blocked, id=6332, stack(0x0000003692d00000,0x0000003692e00000) (1024K)]
  0x0000019526224fd0 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.13\md-rule)"        [_thread_blocked, id=12852, stack(0x0000003693c00000,0x0000003693d00000) (1024K)]
  0x0000019528eae130 JavaThread "Unconstrained build operations"        [_thread_blocked, id=11432, stack(0x0000003693d00000,0x0000003693e00000) (1024K)]
  0x0000019528eaeed0 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=18748, stack(0x0000003693e00000,0x0000003693f00000) (1024K)]
  0x0000019528eb0a10 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=19320, stack(0x0000003693f00000,0x0000003694000000) (1024K)]
  0x0000019528eaccc0 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=11864, stack(0x0000003694000000,0x0000003694100000) (1024K)]
  0x0000019528eab850 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=4312, stack(0x0000003694100000,0x0000003694200000) (1024K)]
  0x0000019528ead390 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=7424, stack(0x0000003694200000,0x0000003694300000) (1024K)]
  0x0000019528eabf20 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=12648, stack(0x0000003694300000,0x0000003694400000) (1024K)]
  0x0000019528eaa3e0 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=11860, stack(0x0000003694400000,0x0000003694500000) (1024K)]
  0x0000019528eada60 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=14832, stack(0x0000003694500000,0x0000003694600000) (1024K)]
  0x0000019528eaaab0 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=8148, stack(0x0000003694600000,0x0000003694700000) (1024K)]
  0x0000019526228650 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=14572, stack(0x0000003694700000,0x0000003694800000) (1024K)]
  0x0000019526228d20 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=1760, stack(0x0000003694800000,0x0000003694900000) (1024K)]
  0x0000019526222dc0 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=11004, stack(0x0000003694900000,0x0000003694a00000) (1024K)]
  0x0000019526223490 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=11488, stack(0x0000003694a00000,0x0000003694b00000) (1024K)]
  0x00000195277790e0 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=11016, stack(0x0000003694b00000,0x0000003694c00000) (1024K)]
  0x000001952777ce30 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=7680, stack(0x0000003694c00000,0x0000003694d00000) (1024K)]
  0x000001952777d500 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=19008, stack(0x0000003694d00000,0x0000003694e00000) (1024K)]
  0x00000195277797b0 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=11652, stack(0x0000003694e00000,0x0000003694f00000) (1024K)]
  0x0000019528eaf5a0 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=15168, stack(0x0000003695000000,0x0000003695100000) (1024K)]
  0x0000019528eac5f0 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=7624, stack(0x0000003695100000,0x0000003695200000) (1024K)]
  0x0000019528eae800 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=4828, stack(0x0000003695200000,0x0000003695300000) (1024K)]
  0x0000019528eafc70 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=18156, stack(0x0000003695300000,0x0000003695400000) (1024K)]
  0x0000019528eb0340 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=11880, stack(0x0000003695400000,0x0000003695500000) (1024K)]
  0x0000019528eb17b0 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=4844, stack(0x0000003695500000,0x0000003695600000) (1024K)]
  0x00000195277775a0 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=5724, stack(0x0000003695600000,0x0000003695700000) (1024K)]
  0x000001952777b9c0 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=14468, stack(0x0000003695700000,0x0000003695800000) (1024K)]
  0x0000019527778a10 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=19248, stack(0x0000003695800000,0x0000003695900000) (1024K)]
  0x000001952777b2f0 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=19132, stack(0x0000003695900000,0x0000003695a00000) (1024K)]
  0x000001952777ac20 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=11228, stack(0x0000003695a00000,0x0000003695b00000) (1024K)]
  0x000001952777c090 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=4324, stack(0x0000003695b00000,0x0000003695c00000) (1024K)]
  0x0000019527779e80 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=18888, stack(0x0000003695c00000,0x0000003695d00000) (1024K)]
  0x000001952777a550 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=5672, stack(0x0000003695d00000,0x0000003695e00000) (1024K)]
  0x000001952777c760 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=18544, stack(0x0000003695e00000,0x0000003695f00000) (1024K)]
  0x000001952777e2a0 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=15732, stack(0x0000003695f00000,0x0000003696000000) (1024K)]
  0x0000019527776ed0 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=16028, stack(0x0000003696000000,0x0000003696100000) (1024K)]
  0x0000019527778340 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=5636, stack(0x0000003696100000,0x0000003696200000) (1024K)]
  0x0000019526223b60 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=5432, stack(0x0000003696200000,0x0000003696300000) (1024K)]
  0x00000195262256a0 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=5732, stack(0x0000003696300000,0x0000003696400000) (1024K)]
  0x0000019526225d70 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=17752, stack(0x0000003696400000,0x0000003696500000) (1024K)]
  0x000001952b40d3d0 JavaThread "Memory manager"                    [_thread_blocked, id=10740, stack(0x0000003696500000,0x0000003696600000) (1024K)]
  0x0000019528d4c4b0 JavaThread "Problems report writer"            [_thread_blocked, id=3044, stack(0x0000003696600000,0x0000003696700000) (1024K)]
  0x000001952cea5590 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=7880, stack(0x0000003696700000,0x0000003696800000) (1024K)]
  0x000001952cfe53d0 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=17192, stack(0x0000003691800000,0x0000003691900000) (1024K)]
  0x0000019528d48760 JavaThread "included builds"                   [_thread_blocked, id=6756, stack(0x0000003694f00000,0x0000003695000000) (1024K)]
  0x000001952cce97f0 JavaThread "Execution worker"                  [_thread_blocked, id=17684, stack(0x0000003696800000,0x0000003696900000) (1024K)]
  0x000001952cce2af0 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=12824, stack(0x0000003696900000,0x0000003696a00000) (1024K)]
  0x000001952cce5aa0 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=19172, stack(0x0000003696a00000,0x0000003696b00000) (1024K)]
  0x000001952cce6840 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=10836, stack(0x0000003696b00000,0x0000003696c00000) (1024K)]
  0x000001952cce31c0 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=15196, stack(0x0000003696c00000,0x0000003696d00000) (1024K)]
  0x000001952cce3890 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=5028, stack(0x0000003696d00000,0x0000003696e00000) (1024K)]
  0x000001952b40daa0 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=264, stack(0x0000003696e00000,0x0000003696f00000) (1024K)]
  0x000001952b408fb0 JavaThread "Cache worker for execution history cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\8.13\executionHistory)"        [_thread_blocked, id=18968, stack(0x0000003696f00000,0x0000003697000000) (1024K)]
  0x000001952b4066d0 JavaThread "idea-tooling-model-converter"        [_thread_blocked, id=11356, stack(0x0000003697000000,0x0000003697100000) (1024K)]
  0x000001952ad99f30 JavaThread "C2 CompilerThread2"         daemon [_thread_blocked, id=2916, stack(0x0000003697100000,0x0000003697200000) (1024K)]
  0x0000019528677790 JavaThread "Unconstrained build operations Thread 40"        [_thread_blocked, id=5792, stack(0x0000003697200000,0x0000003697300000) (1024K)]
  0x000001952867a740 JavaThread "Unconstrained build operations Thread 41"        [_thread_blocked, id=19016, stack(0x0000003697300000,0x0000003697400000) (1024K)]
  0x000001952867d6f0 JavaThread "Unconstrained build operations Thread 42"        [_thread_blocked, id=1700, stack(0x0000003697400000,0x0000003697500000) (1024K)]
  0x000001952867ddc0 JavaThread "Unconstrained build operations Thread 43"        [_thread_blocked, id=16644, stack(0x0000003697500000,0x0000003697600000) (1024K)]
  0x0000019528684ac0 JavaThread "Unconstrained build operations Thread 44"        [_thread_blocked, id=2328, stack(0x0000003697600000,0x0000003697700000) (1024K)]
  0x0000019528685860 JavaThread "Unconstrained build operations Thread 45"        [_thread_blocked, id=14856, stack(0x0000003697700000,0x0000003697800000) (1024K)]
  0x000001952867eb60 JavaThread "Unconstrained build operations Thread 46"        [_thread_blocked, id=17036, stack(0x0000003697800000,0x0000003697900000) (1024K)]
  0x000001952cea6330 JavaThread "Unconstrained build operations Thread 47"        [_thread_blocked, id=4608, stack(0x0000003697900000,0x0000003697a00000) (1024K)]
  0x000001952cea03d0 JavaThread "Unconstrained build operations Thread 48"        [_thread_blocked, id=14532, stack(0x0000003697a00000,0x0000003697b00000) (1024K)]
  0x000001952cea1170 JavaThread "Unconstrained build operations Thread 49"        [_thread_blocked, id=5676, stack(0x0000003697b00000,0x0000003697c00000) (1024K)]
  0x000001952cea0aa0 JavaThread "Unconstrained build operations Thread 50"        [_thread_blocked, id=14472, stack(0x0000003697c00000,0x0000003697d00000) (1024K)]
  0x000001952cea1840 JavaThread "Unconstrained build operations Thread 51"        [_thread_blocked, id=19348, stack(0x0000003697d00000,0x0000003697e00000) (1024K)]
  0x000001952cea3a50 JavaThread "Unconstrained build operations Thread 52"        [_thread_blocked, id=18708, stack(0x0000003697e00000,0x0000003697f00000) (1024K)]
  0x000001952cea25e0 JavaThread "Unconstrained build operations Thread 53"        [_thread_blocked, id=10020, stack(0x0000003697f00000,0x0000003698000000) (1024K)]
  0x000001952cea47f0 JavaThread "Unconstrained build operations Thread 54"        [_thread_blocked, id=15836, stack(0x0000003698000000,0x0000003698100000) (1024K)]
  0x000001952cea3380 JavaThread "Unconstrained build operations Thread 55"        [_thread_blocked, id=1476, stack(0x0000003698100000,0x0000003698200000) (1024K)]
  0x000001952cea1f10 JavaThread "Unconstrained build operations Thread 56"        [_thread_blocked, id=11540, stack(0x0000003698200000,0x0000003698300000) (1024K)]
  0x000001952cea2cb0 JavaThread "Unconstrained build operations Thread 57"        [_thread_blocked, id=18612, stack(0x0000003698300000,0x0000003698400000) (1024K)]
  0x000001952cea4ec0 JavaThread "Unconstrained build operations Thread 58"        [_thread_blocked, id=15620, stack(0x0000003698400000,0x0000003698500000) (1024K)]
  0x000001952cea4120 JavaThread "Unconstrained build operations Thread 59"        [_thread_blocked, id=17640, stack(0x0000003698500000,0x0000003698600000) (1024K)]
  0x000001952cea5c60 JavaThread "Unconstrained build operations Thread 60"        [_thread_blocked, id=5012, stack(0x0000003698600000,0x0000003698700000) (1024K)]
  0x000001952cea6a00 JavaThread "Unconstrained build operations Thread 61"        [_thread_blocked, id=7628, stack(0x0000003698700000,0x0000003698800000) (1024K)]
  0x000001952ce9f630 JavaThread "Unconstrained build operations Thread 62"        [_thread_blocked, id=18072, stack(0x0000003698800000,0x0000003698900000) (1024K)]
  0x0000019528678c00 JavaThread "Unconstrained build operations Thread 63"        [_thread_blocked, id=11156, stack(0x0000003698900000,0x0000003698a00000) (1024K)]
  0x000001952867bbb0 JavaThread "Unconstrained build operations Thread 64"        [_thread_blocked, id=12916, stack(0x0000003698a00000,0x0000003698b00000) (1024K)]
  0x000001952867c950 JavaThread "Unconstrained build operations Thread 65"        [_thread_blocked, id=15976, stack(0x0000003698b00000,0x0000003698c00000) (1024K)]
  0x00000195286799a0 JavaThread "Unconstrained build operations Thread 66"        [_thread_blocked, id=14608, stack(0x0000003698c00000,0x0000003698d00000) (1024K)]
  0x0000019528676320 JavaThread "Unconstrained build operations Thread 67"        [_thread_blocked, id=19344, stack(0x0000003698d00000,0x0000003698e00000) (1024K)]
  0x000001952867f230 JavaThread "Unconstrained build operations Thread 68"        [_thread_blocked, id=12884, stack(0x0000003698e00000,0x0000003698f00000) (1024K)]
  0x000001952867f900 JavaThread "Unconstrained build operations Thread 69"        [_thread_blocked, id=1312, stack(0x0000003698f00000,0x0000003699000000) (1024K)]
  0x00000195286806a0 JavaThread "Unconstrained build operations Thread 70"        [_thread_blocked, id=17444, stack(0x0000003699000000,0x0000003699100000) (1024K)]
  0x0000019528680d70 JavaThread "Unconstrained build operations Thread 71"        [_thread_blocked, id=15748, stack(0x0000003699100000,0x0000003699200000) (1024K)]
  0x0000019528681440 JavaThread "Unconstrained build operations Thread 72"        [_thread_blocked, id=1904, stack(0x0000003699200000,0x0000003699300000) (1024K)]
  0x000001952867ffd0 JavaThread "Unconstrained build operations Thread 73"        [_thread_blocked, id=2076, stack(0x0000003699300000,0x0000003699400000) (1024K)]
  0x000001952867a070 JavaThread "Unconstrained build operations Thread 74"        [_thread_blocked, id=17532, stack(0x0000003699400000,0x0000003699500000) (1024K)]
  0x000001952867b4e0 JavaThread "Unconstrained build operations Thread 75"        [_thread_blocked, id=11656, stack(0x0000003699500000,0x0000003699600000) (1024K)]
  0x0000019528681b10 JavaThread "Unconstrained build operations Thread 76"        [_thread_blocked, id=1128, stack(0x0000003699600000,0x0000003699700000) (1024K)]
  0x0000019528683d20 JavaThread "Unconstrained build operations Thread 77"        [_thread_blocked, id=14276, stack(0x0000003699700000,0x0000003699800000) (1024K)]
  0x00000195286821e0 JavaThread "Unconstrained build operations Thread 78"        [_thread_blocked, id=17952, stack(0x0000003699800000,0x0000003699900000) (1024K)]
  0x00000195286843f0 JavaThread "Unconstrained build operations Thread 79"        [_thread_blocked, id=18604, stack(0x0000003699900000,0x0000003699a00000) (1024K)]
  0x00000195286828b0 JavaThread "Unconstrained build operations Thread 80"        [_thread_blocked, id=15332, stack(0x0000003699a00000,0x0000003699b00000) (1024K)]
Total: 135

Other Threads:
=>0x000001951f3ca700 VMThread "VM Thread"                           [id=2380, stack(0x0000003690c00000,0x0000003690d00000) (1024K)]
  0x000001951efee3e0 WatcherThread "VM Periodic Task Thread"        [id=1492, stack(0x0000003690b00000,0x0000003690c00000) (1024K)]
  0x000001951c439470 WorkerThread "GC Thread#0"                     [id=17328, stack(0x0000003690600000,0x0000003690700000) (1024K)]
  0x0000019524445730 WorkerThread "GC Thread#1"                     [id=15364, stack(0x0000003691900000,0x0000003691a00000) (1024K)]
  0x00000195249db900 WorkerThread "GC Thread#2"                     [id=17824, stack(0x0000003691a00000,0x0000003691b00000) (1024K)]
  0x0000019524541270 WorkerThread "GC Thread#3"                     [id=11744, stack(0x0000003691b00000,0x0000003691c00000) (1024K)]
  0x0000019524541650 WorkerThread "GC Thread#4"                     [id=3712, stack(0x0000003691c00000,0x0000003691d00000) (1024K)]
  0x00000195248369d0 WorkerThread "GC Thread#5"                     [id=19212, stack(0x0000003691d00000,0x0000003691e00000) (1024K)]
  0x0000019524836db0 WorkerThread "GC Thread#6"                     [id=7216, stack(0x0000003691e00000,0x0000003691f00000) (1024K)]
  0x000001951f811a10 WorkerThread "GC Thread#7"                     [id=19028, stack(0x0000003691f00000,0x0000003692000000) (1024K)]
  0x000001951c44b4c0 ConcurrentGCThread "G1 Main Marker"            [id=1876, stack(0x0000003690700000,0x0000003690800000) (1024K)]
  0x000001951c44c650 WorkerThread "G1 Conc#0"                       [id=6988, stack(0x0000003690800000,0x0000003690900000) (1024K)]
  0x0000019526440100 WorkerThread "G1 Conc#1"                       [id=18600, stack(0x0000003692900000,0x0000003692a00000) (1024K)]
  0x000001951eec20b0 ConcurrentGCThread "G1 Refine#0"               [id=18528, stack(0x0000003690900000,0x0000003690a00000) (1024K)]
  0x000001951eec2950 ConcurrentGCThread "G1 Service"                [id=2272, stack(0x0000003690a00000,0x0000003690b00000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0  15175 13271       4       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter::createView (171 bytes)
C2 CompilerThread1  15175 13257       4       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter::convert (177 bytes)
C2 CompilerThread2  15175 13184       4       java.lang.String::split (8 bytes)
Total: 3

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff9b542e3a0] Threads_lock - owner thread: 0x000001951f3ca700
[0x00007ff9b542e4a0] Heap_lock - owner thread: 0x000001951c44b4c0

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: 1073741824
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 258048K, used 176439K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 17 young (17408K), 8 survivors (8192K)
 Metaspace       used 117880K, committed 120256K, reserved 1179648K
  class space    used 15519K, committed 16704K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080100000| PB 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080400000| Untracked 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080500000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%| O|  |TAMS 0x0000000080700000| PB 0x0000000080700000| Untracked 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%|HS|  |TAMS 0x0000000080800000| PB 0x0000000080700000| Complete 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Updating 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Untracked 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%|HS|  |TAMS 0x0000000081600000| PB 0x0000000081500000| Complete 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%|HC|  |TAMS 0x0000000081700000| PB 0x0000000081600000| Complete 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%|HC|  |TAMS 0x0000000081800000| PB 0x0000000081700000| Complete 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%|HC|  |TAMS 0x0000000081900000| PB 0x0000000081800000| Complete 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%|HC|  |TAMS 0x0000000081a00000| PB 0x0000000081900000| Complete 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%|HC|  |TAMS 0x0000000081b00000| PB 0x0000000081a00000| Complete 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%|HS|  |TAMS 0x0000000081c00000| PB 0x0000000081b00000| Complete 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%|HC|  |TAMS 0x0000000081d00000| PB 0x0000000081c00000| Complete 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%|HC|  |TAMS 0x0000000081e00000| PB 0x0000000081d00000| Complete 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%|HC|  |TAMS 0x0000000081f00000| PB 0x0000000081e00000| Complete 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%|HS|  |TAMS 0x0000000082000000| PB 0x0000000081f00000| Complete 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%|HC|  |TAMS 0x0000000082100000| PB 0x0000000082000000| Complete 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%|HS|  |TAMS 0x0000000082200000| PB 0x0000000082100000| Complete 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%| O|  |TAMS 0x0000000082300000| PB 0x0000000082300000| Untracked 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%| O|  |TAMS 0x0000000082500000| PB 0x0000000082500000| Untracked 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Untracked 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%|HS|  |TAMS 0x0000000082800000| PB 0x0000000082700000| Complete 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%|HC|  |TAMS 0x0000000082900000| PB 0x0000000082800000| Complete 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%|HS|  |TAMS 0x0000000082a00000| PB 0x0000000082900000| Complete 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Untracked 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Updating 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%|HS|  |TAMS 0x0000000082f00000| PB 0x0000000082e00000| Complete 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%|HC|  |TAMS 0x0000000083000000| PB 0x0000000082f00000| Complete 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%|HC|  |TAMS 0x0000000083100000| PB 0x0000000083000000| Complete 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%|HC|  |TAMS 0x0000000083200000| PB 0x0000000083100000| Complete 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%|HC|  |TAMS 0x0000000083300000| PB 0x0000000083200000| Complete 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%|HC|  |TAMS 0x0000000083400000| PB 0x0000000083300000| Complete 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Untracked 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%| O|  |TAMS 0x0000000083600000| PB 0x0000000083600000| Untracked 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%| O|  |TAMS 0x0000000083700000| PB 0x0000000083700000| Untracked 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%| O|  |TAMS 0x0000000083800000| PB 0x0000000083800000| Untracked 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%| O|  |TAMS 0x0000000083900000| PB 0x0000000083900000| Untracked 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%| O|  |TAMS 0x0000000083a00000| PB 0x0000000083a00000| Untracked 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%|HS|  |TAMS 0x0000000083b00000| PB 0x0000000083a00000| Complete 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%|HC|  |TAMS 0x0000000083c00000| PB 0x0000000083b00000| Complete 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%|HC|  |TAMS 0x0000000083d00000| PB 0x0000000083c00000| Complete 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%|HC|  |TAMS 0x0000000083e00000| PB 0x0000000083d00000| Complete 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%|HC|  |TAMS 0x0000000083f00000| PB 0x0000000083e00000| Complete 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%|HC|  |TAMS 0x0000000084000000| PB 0x0000000083f00000| Complete 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%| O|  |TAMS 0x0000000084100000| PB 0x0000000084100000| Updating 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Updating 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Updating 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Updating 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%|HS|  |TAMS 0x0000000084700000| PB 0x0000000084600000| Complete 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%|HS|  |TAMS 0x0000000084800000| PB 0x0000000084700000| Complete 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%|HS|  |TAMS 0x0000000084900000| PB 0x0000000084800000| Complete 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%|HS|  |TAMS 0x0000000084a00000| PB 0x0000000084900000| Complete 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%| O|  |TAMS 0x0000000084b00000| PB 0x0000000084b00000| Untracked 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%| O|  |TAMS 0x0000000084c00000| PB 0x0000000084c00000| Untracked 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%| O|  |TAMS 0x0000000084d00000| PB 0x0000000084d00000| Untracked 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Updating 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%|HS|  |TAMS 0x0000000085300000| PB 0x0000000085200000| Complete 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085400000| PB 0x0000000085400000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%|HS|  |TAMS 0x0000000085500000| PB 0x0000000085400000| Complete 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085600000| PB 0x0000000085600000| Updating 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085800000| PB 0x0000000085800000| Updating 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Updating 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Updating 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Updating 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Untracked 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Untracked 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%|HS|  |TAMS 0x0000000086000000| PB 0x0000000085f00000| Complete 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086100000| PB 0x0000000086100000| Updating 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Updating 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Updating 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Untracked 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Updating 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Untracked 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000087000000| PB 0x0000000087000000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%| O|  |TAMS 0x0000000087100000| PB 0x0000000087100000| Updating 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%| O|  |TAMS 0x0000000087200000| PB 0x0000000087200000| Updating 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%|HS|  |TAMS 0x0000000087300000| PB 0x0000000087200000| Complete 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%|HC|  |TAMS 0x0000000087400000| PB 0x0000000087300000| Complete 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%|HS|  |TAMS 0x0000000087500000| PB 0x0000000087400000| Complete 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Updating 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Untracked 
| 120|0x0000000087800000, 0x0000000087900000, 0x0000000087900000|100%| O|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Updating 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%| O|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Updating 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%| O|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Updating 
| 123|0x0000000087b00000, 0x0000000087c00000, 0x0000000087c00000|100%| O|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Updating 
| 124|0x0000000087c00000, 0x0000000087d00000, 0x0000000087d00000|100%| O|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Updating 
| 125|0x0000000087d00000, 0x0000000087e00000, 0x0000000087e00000|100%|HS|  |TAMS 0x0000000087e00000| PB 0x0000000087d00000| Complete 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%|HC|  |TAMS 0x0000000087f00000| PB 0x0000000087e00000| Complete 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%|HC|  |TAMS 0x0000000088000000| PB 0x0000000087f00000| Complete 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%|HC|  |TAMS 0x0000000088100000| PB 0x0000000088000000| Complete 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%|HC|  |TAMS 0x0000000088200000| PB 0x0000000088100000| Complete 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%|HC|  |TAMS 0x0000000088300000| PB 0x0000000088200000| Complete 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%|HS|  |TAMS 0x0000000088400000| PB 0x0000000088300000| Complete 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%|HC|  |TAMS 0x0000000088500000| PB 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%|HC|  |TAMS 0x0000000088600000| PB 0x0000000088500000| Complete 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%|HC|  |TAMS 0x0000000088700000| PB 0x0000000088600000| Complete 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%|HS|  |TAMS 0x0000000088800000| PB 0x0000000088700000| Complete 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%|HC|  |TAMS 0x0000000088900000| PB 0x0000000088800000| Complete 
| 137|0x0000000088900000, 0x0000000088a00000, 0x0000000088a00000|100%| O|  |TAMS 0x00000000889800c0| PB 0x00000000889800c0| Updating 
| 138|0x0000000088a00000, 0x0000000088b00000, 0x0000000088b00000|100%| O|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088c00000, 0x0000000088c00000|100%| O|  |TAMS 0x0000000088b00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088d00000, 0x0000000088d00000|100%| O|  |TAMS 0x0000000088c00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088e00000, 0x0000000088e00000|100%| O|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088f00000, 0x0000000088f00000|100%| O|  |TAMS 0x0000000088e00000| PB 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000089000000, 0x0000000089000000|100%| O|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089000000, 0x0000000089100000|  0%| F|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089100000, 0x0000000089200000|  0%| F|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089200000, 0x0000000089300000|  0%| F|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089300000, 0x0000000089400000|  0%| F|  |TAMS 0x0000000089300000| PB 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089400000, 0x0000000089500000|  0%| F|  |TAMS 0x0000000089400000| PB 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089500000, 0x0000000089600000|  0%| F|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089600000, 0x0000000089700000|  0%| F|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089700000, 0x0000000089800000|  0%| F|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089800000, 0x0000000089900000|  0%| F|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089900000, 0x0000000089a00000|  0%| F|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089a00000, 0x0000000089b00000|  0%| F|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089c00000, 0x0000000089c00000|100%| O|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089c4dfa0, 0x0000000089d00000| 30%| O|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Updating 
| 157|0x0000000089d00000, 0x0000000089d00000, 0x0000000089e00000|  0%| F|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089e00000, 0x0000000089f00000|  0%| F|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x0000000089f00000, 0x000000008a000000|  0%| F|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a000000, 0x000000008a100000|  0%| F|  |TAMS 0x000000008a000000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a200000, 0x000000008a300000|  0%| F|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a500000, 0x000000008a500000|100%| S|CS|TAMS 0x000000008a400000| PB 0x000000008a400000| Complete 
| 165|0x000000008a500000, 0x000000008a600000, 0x000000008a600000|100%| S|CS|TAMS 0x000000008a500000| PB 0x000000008a500000| Complete 
| 166|0x000000008a600000, 0x000000008a700000, 0x000000008a700000|100%| S|CS|TAMS 0x000000008a600000| PB 0x000000008a600000| Complete 
| 167|0x000000008a700000, 0x000000008a800000, 0x000000008a800000|100%| S|CS|TAMS 0x000000008a700000| PB 0x000000008a700000| Complete 
| 168|0x000000008a800000, 0x000000008a900000, 0x000000008a900000|100%| S|CS|TAMS 0x000000008a800000| PB 0x000000008a800000| Complete 
| 169|0x000000008a900000, 0x000000008aa00000, 0x000000008aa00000|100%| S|CS|TAMS 0x000000008a900000| PB 0x000000008a900000| Complete 
| 170|0x000000008aa00000, 0x000000008ab00000, 0x000000008ab00000|100%| S|CS|TAMS 0x000000008aa00000| PB 0x000000008aa00000| Complete 
| 171|0x000000008ab00000, 0x000000008ac00000, 0x000000008ac00000|100%| S|CS|TAMS 0x000000008ab00000| PB 0x000000008ab00000| Complete 
| 172|0x000000008ac00000, 0x000000008ac00000, 0x000000008ad00000|  0%| F|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ad00000, 0x000000008ae00000|  0%| F|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b000000, 0x000000008b100000|  0%| F|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b100000, 0x000000008b200000|  0%| F|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc00000, 0x000000008bd00000|  0%| F|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008bd00000, 0x000000008be00000|  0%| F|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008be00000, 0x000000008bf00000|  0%| F|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c300000, 0x000000008c400000|  0%| F|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c500000, 0x000000008c600000|  0%| F|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c600000, 0x000000008c700000|  0%| F|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c700000, 0x000000008c800000|  0%| F|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c800000, 0x000000008c900000|  0%| F|  |TAMS 0x000000008c800000| PB 0x000000008c800000| Untracked 
| 201|0x000000008c900000, 0x000000008c900000, 0x000000008ca00000|  0%| F|  |TAMS 0x000000008c900000| PB 0x000000008c900000| Untracked 
| 202|0x000000008ca00000, 0x000000008ca00000, 0x000000008cb00000|  0%| F|  |TAMS 0x000000008ca00000| PB 0x000000008ca00000| Untracked 
| 203|0x000000008cb00000, 0x000000008cb00000, 0x000000008cc00000|  0%| F|  |TAMS 0x000000008cb00000| PB 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cc00000, 0x000000008cd00000|  0%| F|  |TAMS 0x000000008cc00000| PB 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008cd00000, 0x000000008ce00000|  0%| F|  |TAMS 0x000000008cd00000| PB 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008ce00000, 0x000000008cf00000|  0%| F|  |TAMS 0x000000008ce00000| PB 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008cf00000, 0x000000008d000000|  0%| F|  |TAMS 0x000000008cf00000| PB 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d000000, 0x000000008d100000|  0%| F|  |TAMS 0x000000008d000000| PB 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d100000, 0x000000008d200000|  0%| F|  |TAMS 0x000000008d100000| PB 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d200000, 0x000000008d300000|  0%| F|  |TAMS 0x000000008d200000| PB 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d300000, 0x000000008d400000|  0%| F|  |TAMS 0x000000008d300000| PB 0x000000008d300000| Untracked 
| 212|0x000000008d400000, 0x000000008d400000, 0x000000008d500000|  0%| F|  |TAMS 0x000000008d400000| PB 0x000000008d400000| Untracked 
| 213|0x000000008d500000, 0x000000008d500000, 0x000000008d600000|  0%| F|  |TAMS 0x000000008d500000| PB 0x000000008d500000| Untracked 
| 214|0x000000008d600000, 0x000000008d600000, 0x000000008d700000|  0%| F|  |TAMS 0x000000008d600000| PB 0x000000008d600000| Untracked 
| 215|0x000000008d700000, 0x000000008d700000, 0x000000008d800000|  0%| F|  |TAMS 0x000000008d700000| PB 0x000000008d700000| Untracked 
| 216|0x000000008d800000, 0x000000008d800000, 0x000000008d900000|  0%| F|  |TAMS 0x000000008d800000| PB 0x000000008d800000| Untracked 
| 217|0x000000008d900000, 0x000000008d900000, 0x000000008da00000|  0%| F|  |TAMS 0x000000008d900000| PB 0x000000008d900000| Untracked 
| 218|0x000000008da00000, 0x000000008da00000, 0x000000008db00000|  0%| F|  |TAMS 0x000000008da00000| PB 0x000000008da00000| Untracked 
| 219|0x000000008db00000, 0x000000008db00000, 0x000000008dc00000|  0%| F|  |TAMS 0x000000008db00000| PB 0x000000008db00000| Untracked 
| 220|0x000000008dc00000, 0x000000008dc00000, 0x000000008dd00000|  0%| F|  |TAMS 0x000000008dc00000| PB 0x000000008dc00000| Untracked 
| 221|0x000000008dd00000, 0x000000008dd00000, 0x000000008de00000|  0%| F|  |TAMS 0x000000008dd00000| PB 0x000000008dd00000| Untracked 
| 222|0x000000008de00000, 0x000000008de00000, 0x000000008df00000|  0%| F|  |TAMS 0x000000008de00000| PB 0x000000008de00000| Untracked 
| 223|0x000000008df00000, 0x000000008df00000, 0x000000008e000000|  0%| F|  |TAMS 0x000000008df00000| PB 0x000000008df00000| Untracked 
| 224|0x000000008e000000, 0x000000008e000000, 0x000000008e100000|  0%| F|  |TAMS 0x000000008e000000| PB 0x000000008e000000| Untracked 
| 225|0x000000008e100000, 0x000000008e100000, 0x000000008e200000|  0%| F|  |TAMS 0x000000008e100000| PB 0x000000008e100000| Untracked 
| 226|0x000000008e200000, 0x000000008e200000, 0x000000008e300000|  0%| F|  |TAMS 0x000000008e200000| PB 0x000000008e200000| Untracked 
| 227|0x000000008e300000, 0x000000008e300000, 0x000000008e400000|  0%| F|  |TAMS 0x000000008e300000| PB 0x000000008e300000| Untracked 
| 321|0x0000000094100000, 0x0000000094100000, 0x0000000094200000|  0%| F|  |TAMS 0x0000000094100000| PB 0x0000000094100000| Untracked 
| 322|0x0000000094200000, 0x0000000094200000, 0x0000000094300000|  0%| F|  |TAMS 0x0000000094200000| PB 0x0000000094200000| Untracked 
| 323|0x0000000094300000, 0x0000000094300000, 0x0000000094400000|  0%| F|  |TAMS 0x0000000094300000| PB 0x0000000094300000| Untracked 
| 324|0x0000000094400000, 0x0000000094400000, 0x0000000094500000|  0%| F|  |TAMS 0x0000000094400000| PB 0x0000000094400000| Untracked 
| 325|0x0000000094500000, 0x0000000094600000, 0x0000000094600000|100%| E|  |TAMS 0x0000000094500000| PB 0x0000000094500000| Complete 
| 326|0x0000000094600000, 0x0000000094700000, 0x0000000094700000|100%| E|CS|TAMS 0x0000000094600000| PB 0x0000000094600000| Complete 
| 327|0x0000000094700000, 0x0000000094800000, 0x0000000094800000|100%| E|CS|TAMS 0x0000000094700000| PB 0x0000000094700000| Complete 
| 328|0x0000000094800000, 0x0000000094900000, 0x0000000094900000|100%| E|CS|TAMS 0x0000000094800000| PB 0x0000000094800000| Complete 
| 329|0x0000000094900000, 0x0000000094a00000, 0x0000000094a00000|100%| E|CS|TAMS 0x0000000094900000| PB 0x0000000094900000| Complete 
| 330|0x0000000094a00000, 0x0000000094b00000, 0x0000000094b00000|100%| E|CS|TAMS 0x0000000094a00000| PB 0x0000000094a00000| Complete 
| 351|0x0000000095f00000, 0x0000000096000000, 0x0000000096000000|100%| E|CS|TAMS 0x0000000095f00000| PB 0x0000000095f00000| Complete 
|2035|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| O|  |TAMS 0x00000000ff400000| PB 0x00000000ff400000| Updating 
|2036|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| E|CS|TAMS 0x00000000ff400000| PB 0x00000000ff400000| Complete 
|2037|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| O|  |TAMS 0x00000000ff600000| PB 0x00000000ff600000| Updating 
|2038|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| O|  |TAMS 0x00000000ff700000| PB 0x00000000ff700000| Untracked 
|2039|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| O|  |TAMS 0x00000000ff800000| PB 0x00000000ff800000| Untracked 
|2040|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| O|  |TAMS 0x00000000ff900000| PB 0x00000000ff900000| Untracked 
|2041|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| O|  |TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Untracked 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| O|  |TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Untracked 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Updating 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| E|CS|TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Complete 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Updating 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| O|  |TAMS 0x00000000fff00000| PB 0x00000000fff00000| Untracked 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| O|  |TAMS 0x0000000100000000| PB 0x0000000100000000| Untracked 

Card table byte_map: [0x000001951a010000,0x000001951a410000] _byte_map_base: 0x0000019519c10000

Marking Bits: (CMBitMap*) 0x000001951c4399f0
 Bits: [0x000001951a410000, 0x000001951c410000)

Polling page: 0x0000019505390000

Metaspace:

Usage:
  Non-class:     99.96 MB used.
      Class:     15.16 MB used.
       Both:    115.12 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,     101.12 MB ( 79%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      16.31 MB (  2%) committed,  1 nodes.
             Both:        1.12 GB reserved,     117.44 MB ( 10%) committed. 

Chunk freelists:
   Non-Class:  10.20 MB
       Class:  15.66 MB
        Both:  25.87 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 177.31 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 6.
num_arena_births: 4258.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1878.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 6.
num_chunks_taken_from_freelist: 9333.
num_chunk_merges: 6.
num_chunk_splits: 6094.
num_chunks_enlarged: 3815.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=7124Kb max_used=7281Kb free=112875Kb
 bounds [0x00000195123b0000, 0x0000019512ad0000, 0x00000195198e0000]
CodeHeap 'profiled nmethods': size=120000Kb used=21180Kb max_used=22050Kb free=98819Kb
 bounds [0x000001950a8e0000, 0x000001950be70000, 0x0000019511e10000]
CodeHeap 'non-nmethods': size=5760Kb used=2908Kb max_used=2984Kb free=2851Kb
 bounds [0x0000019511e10000, 0x0000019512100000, 0x00000195123b0000]
 total_blobs=12335 nmethods=11328 adapters=911
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 15.124 Thread 0x000001951f603350 13296       3       com.android.tools.idea.gradle.project.sync.ModelCacheV2ImplKt::modelCacheV2Impl$createFromDependencies$lambda$68$populateAndroidLibraries$lambda$59$lambda$58$lambda$57 (13 bytes)
Event: 15.125 Thread 0x000001951f603350 nmethod 13296 0x000001950be62890 code [0x000001950be62ac0, 0x000001950be63078]
Event: 15.125 Thread 0x000001951f603350 13293       3       kotlin.collections.CollectionsKt___CollectionsKt::joinTo$default (98 bytes)
Event: 15.125 Thread 0x000001951f603350 nmethod 13293 0x000001950be63310 code [0x000001950be634c0, 0x000001950be63768]
Event: 15.125 Thread 0x000001951f603350 13276       1       com.android.ide.common.gradle.Separator::getChar (5 bytes)
Event: 15.125 Thread 0x000001951f603350 nmethod 13276 0x0000019512acc390 code [0x0000019512acc520, 0x0000019512acc5e8]
Event: 15.125 Thread 0x000001951f603350 13290       3       kotlin.collections.CollectionsKt___CollectionsKt::last (36 bytes)
Event: 15.126 Thread 0x000001951f603350 nmethod 13290 0x000001950be63810 code [0x000001950be63b00, 0x000001950be64860]
Event: 15.126 Thread 0x000001951f603350 13297       3       kotlin.collections.CollectionsKt__CollectionsKt::getLastIndex (15 bytes)
Event: 15.126 Thread 0x000001951f603350 nmethod 13297 0x000001950be64d10 code [0x000001950be64f40, 0x000001950be65568]
Event: 15.126 Thread 0x000001951f603350 13298       3       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter::convertCollectionInternal (47 bytes)
Event: 15.127 Thread 0x000001951f603350 nmethod 13298 0x000001950be65810 code [0x000001950be65a20, 0x000001950be65f10]
Event: 15.127 Thread 0x000001951f603350 13294       3       kotlin.collections.CollectionsKt__CollectionsJVMKt::listOf (11 bytes)
Event: 15.127 Thread 0x000001951f603350 nmethod 13294 0x000001950be66110 code [0x000001950be662e0, 0x000001950be666a0]
Event: 15.127 Thread 0x000001951f603350 13289       3       kotlin.collections.CollectionsKt___CollectionsKt::last (75 bytes)
Event: 15.128 Thread 0x000001951f603350 nmethod 13289 0x000001950be66810 code [0x000001950be66ae0, 0x000001950be67848]
Event: 15.128 Thread 0x000001951f603350 13299       3       com.android.tools.idea.gradle.model.impl.IdeAndroidLibraryImpl$Companion::create$makeRelative (31 bytes)
Event: 15.128 Thread 0x000001951f603350 nmethod 13299 0x000001950be67c10 code [0x000001950be67e00, 0x000001950be681e0]
Event: 15.130 Thread 0x000001951f603350 13300   !   3       jdk.proxy4.$Proxy218::getGroup (29 bytes)
Event: 15.130 Thread 0x000001951f603350 nmethod 13300 0x000001950be68390 code [0x000001950be68560, 0x000001950be68940]

GC Heap History (20 events):
Event: 8.607 GC heap before
{Heap before GC invocations=16 (full 0):
 garbage-first heap   total 223232K, used 192523K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 111 young (113664K), 12 survivors (12288K)
 Metaspace       used 68126K, committed 70080K, reserved 1114112K
  class space    used 9195K, committed 10176K, reserved 1048576K
}
Event: 8.614 GC heap after
{Heap after GC invocations=17 (full 0):
 garbage-first heap   total 223232K, used 97943K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 68126K, committed 70080K, reserved 1114112K
  class space    used 9195K, committed 10176K, reserved 1048576K
}
Event: 9.498 GC heap before
{Heap before GC invocations=18 (full 0):
 garbage-first heap   total 223232K, used 195223K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 103 young (105472K), 10 survivors (10240K)
 Metaspace       used 77668K, committed 79680K, reserved 1179648K
  class space    used 10335K, committed 11328K, reserved 1048576K
}
Event: 9.504 GC heap after
{Heap after GC invocations=19 (full 0):
 garbage-first heap   total 223232K, used 106473K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 7 survivors (7168K)
 Metaspace       used 77668K, committed 79680K, reserved 1179648K
  class space    used 10335K, committed 11328K, reserved 1048576K
}
Event: 10.458 GC heap before
{Heap before GC invocations=19 (full 0):
 garbage-first heap   total 223232K, used 201705K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 96 young (98304K), 7 survivors (7168K)
 Metaspace       used 85198K, committed 87232K, reserved 1179648K
  class space    used 11276K, committed 12288K, reserved 1048576K
}
Event: 10.464 GC heap after
{Heap after GC invocations=20 (full 0):
 garbage-first heap   total 223232K, used 114430K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 85198K, committed 87232K, reserved 1179648K
  class space    used 11276K, committed 12288K, reserved 1048576K
}
Event: 11.040 GC heap before
{Heap before GC invocations=20 (full 0):
 garbage-first heap   total 223232K, used 187134K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 84 young (86016K), 12 survivors (12288K)
 Metaspace       used 88900K, committed 91008K, reserved 1179648K
  class space    used 11768K, committed 12800K, reserved 1048576K
}
Event: 11.047 GC heap after
{Heap after GC invocations=21 (full 0):
 garbage-first heap   total 223232K, used 119318K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 88900K, committed 91008K, reserved 1179648K
  class space    used 11768K, committed 12800K, reserved 1048576K
}
Event: 11.914 GC heap before
{Heap before GC invocations=22 (full 0):
 garbage-first heap   total 223232K, used 199190K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 89 young (91136K), 11 survivors (11264K)
 Metaspace       used 95392K, committed 97536K, reserved 1179648K
  class space    used 12828K, committed 13888K, reserved 1048576K
}
Event: 11.921 GC heap after
{Heap after GC invocations=23 (full 0):
 garbage-first heap   total 223232K, used 122601K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 95392K, committed 97536K, reserved 1179648K
  class space    used 12828K, committed 13888K, reserved 1048576K
}
Event: 12.851 GC heap before
{Heap before GC invocations=23 (full 0):
 garbage-first heap   total 223232K, used 220905K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 85 young (87040K), 8 survivors (8192K)
 Metaspace       used 105005K, committed 107264K, reserved 1179648K
  class space    used 13979K, committed 15104K, reserved 1048576K
}
Event: 12.863 GC heap after
{Heap after GC invocations=24 (full 0):
 garbage-first heap   total 242688K, used 147993K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 105005K, committed 107264K, reserved 1179648K
  class space    used 13979K, committed 15104K, reserved 1048576K
}
Event: 12.978 GC heap before
{Heap before GC invocations=24 (full 0):
 garbage-first heap   total 242688K, used 159257K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 24 young (24576K), 11 survivors (11264K)
 Metaspace       used 105640K, committed 107904K, reserved 1179648K
  class space    used 14034K, committed 15168K, reserved 1048576K
}
Event: 12.984 GC heap after
{Heap after GC invocations=25 (full 0):
 garbage-first heap   total 242688K, used 151827K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 105640K, committed 107904K, reserved 1179648K
  class space    used 14034K, committed 15168K, reserved 1048576K
}
Event: 13.858 GC heap before
{Heap before GC invocations=26 (full 0):
 garbage-first heap   total 258048K, used 222483K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 70 young (71680K), 3 survivors (3072K)
 Metaspace       used 113762K, committed 116096K, reserved 1179648K
  class space    used 15097K, committed 16256K, reserved 1048576K
}
Event: 13.862 GC heap after
{Heap after GC invocations=27 (full 0):
 garbage-first heap   total 258048K, used 154344K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 113762K, committed 116096K, reserved 1179648K
  class space    used 15097K, committed 16256K, reserved 1048576K
}
Event: 14.595 GC heap before
{Heap before GC invocations=27 (full 0):
 garbage-first heap   total 258048K, used 234216K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 85 young (87040K), 6 survivors (6144K)
 Metaspace       used 117011K, committed 119360K, reserved 1179648K
  class space    used 15426K, committed 16576K, reserved 1048576K
}
Event: 14.604 GC heap after
{Heap after GC invocations=28 (full 0):
 garbage-first heap   total 258048K, used 161280K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 117011K, committed 119360K, reserved 1179648K
  class space    used 15426K, committed 16576K, reserved 1048576K
}
Event: 15.033 GC heap before
{Heap before GC invocations=28 (full 0):
 garbage-first heap   total 258048K, used 230912K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 80 young (81920K), 11 survivors (11264K)
 Metaspace       used 117530K, committed 119872K, reserved 1179648K
  class space    used 15474K, committed 16640K, reserved 1048576K
}
Event: 15.043 GC heap after
{Heap after GC invocations=29 (full 0):
 garbage-first heap   total 258048K, used 168247K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 8 young (8192K), 8 survivors (8192K)
 Metaspace       used 117530K, committed 119872K, reserved 1179648K
  class space    used 15474K, committed 16640K, reserved 1048576K
}

Dll operation events (3 events):
Event: 0.008 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.014 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 0.557 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 14.962 Thread 0x0000019528eaccc0 Uncommon trap: reason=array_check action=maybe_recompile pc=0x0000019512a9bd70 method=java.util.TimSort.reverseRange([Ljava/lang/Object;II)V @ 27 c2
Event: 14.962 Thread 0x000001951fa86a00 DEOPT UNPACKING pc=0x0000019511e646a2 sp=0x00000036925f6600 mode 2
Event: 14.962 Thread 0x0000019528eaccc0 DEOPT PACKING pc=0x0000019512a9bd70 sp=0x00000036940fd600
Event: 14.962 Thread 0x0000019528eaccc0 DEOPT UNPACKING pc=0x0000019511e646a2 sp=0x00000036940fd430 mode 2
Event: 15.015 Thread 0x000001951fa86a00 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000019512a98108 relative=0x0000000000001028
Event: 15.015 Thread 0x000001951fa86a00 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000019512a98108 method=org.gradle.api.internal.artifacts.result.DefaultResolvedComponentResult.eachElement(Lorg/gradle/api/artifacts/result/ResolvedComponentResult;Lorg/gradle/api/Action
Event: 15.015 Thread 0x000001951fa86a00 DEOPT PACKING pc=0x0000019512a98108 sp=0x00000036925f7860
Event: 15.015 Thread 0x000001951fa86a00 DEOPT UNPACKING pc=0x0000019511e646a2 sp=0x00000036925f77e8 mode 2
Event: 15.015 Thread 0x000001951fa86a00 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000019512a98108 relative=0x0000000000001028
Event: 15.015 Thread 0x000001951fa86a00 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000019512a98108 method=org.gradle.api.internal.artifacts.result.DefaultResolvedComponentResult.eachElement(Lorg/gradle/api/artifacts/result/ResolvedComponentResult;Lorg/gradle/api/Action
Event: 15.015 Thread 0x000001951fa86a00 DEOPT PACKING pc=0x0000019512a98108 sp=0x00000036925f7750
Event: 15.015 Thread 0x000001951fa86a00 DEOPT UNPACKING pc=0x0000019511e646a2 sp=0x00000036925f76d8 mode 2
Event: 15.015 Thread 0x000001951fa86a00 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000019512a98108 relative=0x0000000000001028
Event: 15.015 Thread 0x000001951fa86a00 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000019512a98108 method=org.gradle.api.internal.artifacts.result.DefaultResolvedComponentResult.eachElement(Lorg/gradle/api/artifacts/result/ResolvedComponentResult;Lorg/gradle/api/Action
Event: 15.015 Thread 0x000001951fa86a00 DEOPT PACKING pc=0x0000019512a98108 sp=0x00000036925f7640
Event: 15.015 Thread 0x000001951fa86a00 DEOPT UNPACKING pc=0x0000019511e646a2 sp=0x00000036925f75c8 mode 2
Event: 15.015 Thread 0x000001951fa86a00 Uncommon trap: trap_request=0xffffffbe fr.pc=0x0000019512a98108 relative=0x0000000000001028
Event: 15.015 Thread 0x000001951fa86a00 Uncommon trap: reason=profile_predicate action=maybe_recompile pc=0x0000019512a98108 method=org.gradle.api.internal.artifacts.result.DefaultResolvedComponentResult.eachElement(Lorg/gradle/api/artifacts/result/ResolvedComponentResult;Lorg/gradle/api/Action
Event: 15.015 Thread 0x000001951fa86a00 DEOPT PACKING pc=0x0000019512a98108 sp=0x00000036925f7530
Event: 15.015 Thread 0x000001951fa86a00 DEOPT UNPACKING pc=0x0000019511e646a2 sp=0x00000036925f74b8 mode 2

Classes loaded (20 events):
Event: 13.828 Loading class com/sun/xml/internal/stream/events/EndElementEvent
Event: 13.828 Loading class javax/xml/stream/events/EndElement
Event: 13.828 Loading class javax/xml/stream/events/EndElement done
Event: 13.828 Loading class com/sun/xml/internal/stream/events/EndElementEvent done
Event: 13.828 Loading class com/sun/xml/internal/stream/events/CommentEvent
Event: 13.828 Loading class javax/xml/stream/events/Comment
Event: 13.828 Loading class javax/xml/stream/events/Comment done
Event: 13.828 Loading class com/sun/xml/internal/stream/events/CommentEvent done
Event: 14.050 Loading class java/util/regex/Pattern$BackRef
Event: 14.050 Loading class java/util/regex/Pattern$BackRef done
Event: 14.051 Loading class java/util/stream/SortedOps$RefSortingSink
Event: 14.051 Loading class java/util/stream/SortedOps$RefSortingSink done
Event: 14.053 Loading class java/util/regex/Pattern$Pos
Event: 14.053 Loading class java/util/regex/Pattern$Pos done
Event: 14.124 Loading class java/io/CharArrayWriter
Event: 14.125 Loading class java/io/CharArrayWriter done
Event: 14.130 Loading class java/io/SequenceInputStream
Event: 14.131 Loading class java/io/SequenceInputStream done
Event: 14.137 Loading class java/util/function/IntUnaryOperator
Event: 14.137 Loading class java/util/function/IntUnaryOperator done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 12.585 Thread 0x000001952b4066d0 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a0f6b60}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/assignment/AssignmentModelImpl;.<clinit>()V> (0x000000008a0f6b60) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.587 Thread 0x000001952b4066d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000089f067a8}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/samWithReceiver/SamWithReceiverModelImpl;.<clinit>()V> (0x0000000089f067a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.587 Thread 0x000001952b4066d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000089f0f4c8}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/noarg/NoArgModelImpl;.<clinit>()V> (0x0000000089f0f4c8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.588 Thread 0x000001952b4066d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000089f17fe8}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/allopen/AllOpenModelImpl;.<clinit>()V> (0x0000000089f17fe8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.593 Thread 0x000001952b4066d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000089fc3fb0}: static Lorg/jetbrains/plugins/gradle/tooling/internal/VersionCatalogsModelImpl;.<clinit>()V> (0x0000000089fc3fb0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.599 Thread 0x000001952b4066d0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000089ff1d60}: static Lorg/jetbrains/plugins/gradle/tooling/serialization/internal/adapter/kotlin/dsl/InternalKotlinDslScriptsModel;.<clinit>()V> (0x0000000089ff1d60) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.675 Thread 0x000001951fa86a00 Exception <a 'java/lang/NoSuchMethodError'{0x00000000899e2c70}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object)'> (0x00000000899e2c70) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 12.679 Thread 0x000001951fa86a00 Exception <a 'java/lang/NoSuchMethodError'{0x00000000898193b8}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object)'> (0x00000000898193b8) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 13.215 Thread 0x000001951fa86a00 Exception <a 'sun/nio/fs/WindowsException'{0x000000008e30f788}> (0x000000008e30f788) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 13.215 Thread 0x000001951fa86a00 Exception <a 'sun/nio/fs/WindowsException'{0x000000008e310df0}> (0x000000008e310df0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 13.216 Thread 0x000001951fa86a00 Exception <a 'sun/nio/fs/WindowsException'{0x000000008e3124a8}> (0x000000008e3124a8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 13.216 Thread 0x000001951fa86a00 Exception <a 'sun/nio/fs/WindowsException'{0x000000008e313b60}> (0x000000008e313b60) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 535]
Event: 13.332 Thread 0x000001951fa86a00 Exception <a 'java/lang/NullPointerException'{0x000000008d9e1270}> (0x000000008d9e1270) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 1471]
Event: 13.862 Thread 0x000001951f5cc2a0 Implicit null exception at 0x000001951291ebc8 to 0x000001951291f298
Event: 14.074 Thread 0x000001951fa86a00 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dd88ef0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object, java.lang.Object, java.lang.Object)'> (0x000000008dd88ef0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 14.074 Thread 0x000001951fa86a00 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dd90b58}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.newInvokeSpecial(java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008dd90b58) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 14.074 Thread 0x000001951fa86a00 Exception <a 'java/lang/NoSuchMethodError'{0x000000008dd94e20}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int, java.lang.Object, java.lang.Object)'> (0x000000008dd94e20) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 14.138 Thread 0x000001951fa86a00 Exception <a 'java/lang/NoSuchMethodError'{0x000000008d8ca950}: 'int java.lang.invoke.DirectMethodHandle$Holder.invokeSpecial(java.lang.Object, java.lang.Object, int)'> (0x000000008d8ca950) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 14.765 Thread 0x000001951fa86a00 Implicit null exception at 0x00000195127e297d to 0x00000195127e2b14
Event: 14.772 Thread 0x000001951fa86a00 Implicit null exception at 0x00000195127e23e8 to 0x00000195127e2554

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 14.056 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 14.205 Executing VM operation: ICBufferFull
Event: 14.205 Executing VM operation: ICBufferFull done
Event: 14.342 Executing VM operation: ICBufferFull
Event: 14.343 Executing VM operation: ICBufferFull done
Event: 14.458 Executing VM operation: ICBufferFull
Event: 14.458 Executing VM operation: ICBufferFull done
Event: 14.458 Executing VM operation: ICBufferFull
Event: 14.458 Executing VM operation: ICBufferFull done
Event: 14.503 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 14.504 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 14.594 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 14.604 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 14.837 Executing VM operation: ICBufferFull
Event: 14.837 Executing VM operation: ICBufferFull done
Event: 14.896 Executing VM operation: ICBufferFull
Event: 14.896 Executing VM operation: ICBufferFull done
Event: 15.033 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 15.043 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 15.131 Executing VM operation: G1PauseRemark

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b2f0790
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b322e90
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b3c2810
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b3c3890
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b3ec990
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b3ee390
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b498610
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b4cc690
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b4cdc90
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b7dea90
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b93d810
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b993610
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b998f90
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b999790
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b99b290
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b99cb90
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950b99f810
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950ba74c90
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950ba75e90
Event: 15.137 Thread 0x000001951f3ca700 flushing  nmethod 0x000001950ba77310

Events (20 events):
Event: 14.755 Thread 0x000001951fa86a00 Thread added: 0x000001952cea6a00
Event: 14.755 Thread 0x000001951fa86a00 Thread added: 0x000001952ce9f630
Event: 14.755 Thread 0x000001951fa86a00 Thread added: 0x0000019528678c00
Event: 14.756 Thread 0x000001951fa86a00 Thread added: 0x000001952867bbb0
Event: 14.908 Thread 0x000001951fa86a00 Thread added: 0x000001952867c950
Event: 14.909 Thread 0x000001951fa86a00 Thread added: 0x00000195286799a0
Event: 14.909 Thread 0x000001951fa86a00 Thread added: 0x0000019528676320
Event: 14.910 Thread 0x000001951fa86a00 Thread added: 0x000001952867f230
Event: 14.911 Thread 0x000001951fa86a00 Thread added: 0x000001952867f900
Event: 14.912 Thread 0x000001951fa86a00 Thread added: 0x00000195286806a0
Event: 14.912 Thread 0x000001951fa86a00 Thread added: 0x0000019528680d70
Event: 14.942 Thread 0x000001951fa86a00 Thread added: 0x0000019528681440
Event: 14.943 Thread 0x000001951fa86a00 Thread added: 0x000001952867ffd0
Event: 14.943 Thread 0x000001951fa86a00 Thread added: 0x000001952867a070
Event: 14.944 Thread 0x000001951fa86a00 Thread added: 0x000001952867b4e0
Event: 14.945 Thread 0x000001951fa86a00 Thread added: 0x0000019528681b10
Event: 14.945 Thread 0x000001951fa86a00 Thread added: 0x0000019528683d20
Event: 14.946 Thread 0x000001951fa86a00 Thread added: 0x00000195286821e0
Event: 14.960 Thread 0x000001951fa86a00 Thread added: 0x00000195286843f0
Event: 14.960 Thread 0x000001951fa86a00 Thread added: 0x00000195286828b0


Dynamic libraries:
0x00007ff645790000 - 0x00007ff64579a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffa35f30000 - 0x00007ffa36128000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa35030000 - 0x00007ffa350f2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa33740000 - 0x00007ffa33a36000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa30220000 - 0x00007ffa302b4000 	C:\WINDOWS\SYSTEM32\apphelp.dll
0x00007ffa1ea70000 - 0x00007ffa1ef06000 	C:\WINDOWS\SYSTEM32\AcLayers.DLL
0x00007ffa34310000 - 0x00007ffa343ae000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa34e90000 - 0x00007ffa3502d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa33dc0000 - 0x00007ffa33de2000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa357f0000 - 0x00007ffa3581b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa33c00000 - 0x00007ffa33d19000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa33d20000 - 0x00007ffa33dbd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa33640000 - 0x00007ffa33740000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa34720000 - 0x00007ffa34e8e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa34160000 - 0x00007ffa341bb000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffa34000000 - 0x00007ffa340b1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa340c0000 - 0x00007ffa3415f000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa35100000 - 0x00007ffa35223000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa33af0000 - 0x00007ffa33b17000 	C:\WINDOWS\System32\bcrypt.dll
0x0000019505100000 - 0x0000019505103000 	C:\WINDOWS\SYSTEM32\sfc.dll
0x00007ffa126e0000 - 0x00007ffa12784000 	C:\WINDOWS\SYSTEM32\WINSPOOL.DRV
0x00007ffa35230000 - 0x00007ffa35583000 	C:\WINDOWS\System32\combase.dll
0x00007ffa356b0000 - 0x00007ffa3575d000 	C:\WINDOWS\System32\shcore.dll
0x00007ffa10fe0000 - 0x00007ffa10ff2000 	C:\WINDOWS\SYSTEM32\sfc_os.DLL
0x00007ffa30590000 - 0x00007ffa305a1000 	C:\WINDOWS\SYSTEM32\SortWindows61.dll
0x00007ffa35dd0000 - 0x00007ffa35dff000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa240f0000 - 0x00007ffa24108000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffa181c0000 - 0x00007ffa181db000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffa228a0000 - 0x00007ffa22b3a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffa240e0000 - 0x00007ffa240ec000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffa0ba00000 - 0x00007ffa0ba8d000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ff9b4880000 - 0x00007ff9b550b000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffa343b0000 - 0x00007ffa3441b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa33420000 - 0x00007ffa3346b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffa2b410000 - 0x00007ffa2b41a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa28320000 - 0x00007ffa28347000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa33400000 - 0x00007ffa33412000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffa31e30000 - 0x00007ffa31e42000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa23210000 - 0x00007ffa2321a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffa31bc0000 - 0x00007ffa31dc1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa21760000 - 0x00007ffa21794000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa33b20000 - 0x00007ffa33ba2000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa217a0000 - 0x00007ffa217ae000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffa18160000 - 0x00007ffa18180000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffa14050000 - 0x00007ffa14068000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffa31410000 - 0x00007ffa31bb3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa32f20000 - 0x00007ffa32f4b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffa35e20000 - 0x00007ffa35eed000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa334f0000 - 0x00007ffa33515000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa180a0000 - 0x00007ffa180b0000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffa2f470000 - 0x00007ffa2f57a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa32c80000 - 0x00007ffa32cea000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa14030000 - 0x00007ffa14046000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffa18080000 - 0x00007ffa18090000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffa23360000 - 0x00007ffa23387000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ff9f0020000 - 0x00007ff9f0098000 	C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffa14020000 - 0x00007ffa14029000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffa14010000 - 0x00007ffa1401b000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffa34220000 - 0x00007ffa34228000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa32960000 - 0x00007ffa3299b000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa345d0000 - 0x00007ffa345d8000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa13f80000 - 0x00007ffa13f89000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffa32e70000 - 0x00007ffa32e88000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa325a0000 - 0x00007ffa325d8000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa334b0000 - 0x00007ffa334de000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa32e90000 - 0x00007ffa32e9c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa2f990000 - 0x00007ffa2f997000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\agents\gradle-instrumentation-agent-8.13.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.13
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\gradle-daemon-main-8.13.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ntc
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 29, weak refs: 2

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 645064K (2% of 23015712K total physical memory with 3969912K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 37725K
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 30004K
Loader bootstrap                                                                       : 27740K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 13300K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 7643K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 507K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 408K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 246K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 235K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 69880B

Classes loaded by more than one classloader:
Class com.google.common.collect.Iterables                                             : loaded 3 times (x 67B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 3 times (x 77B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 3 times (x 205B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 3 times (x 148B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 3 times (x 72B)
Class com.google.common.collect.ImmutableList                                         : loaded 3 times (x 202B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 3 times (x 148B)
Class com.google.common.base.Joiner$2                                                 : loaded 3 times (x 75B)
Class com.google.common.collect.PeekingIterator                                       : loaded 3 times (x 66B)
Class com.google.common.base.Joiner$1                                                 : loaded 3 times (x 76B)
Class com.google.common.base.Preconditions                                            : loaded 3 times (x 67B)
Class com.google.common.collect.RegularImmutableMap$BucketOverflowException           : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 3 times (x 73B)
Class org.gradle.internal.IoActions                                                   : loaded 3 times (x 67B)
Class org.gradle.api.GradleException                                                  : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 3 times (x 78B)
Class com.google.common.base.Predicate                                                : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap                                          : loaded 3 times (x 116B)
Class org.gradle.internal.Cast                                                        : loaded 3 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableSet                                          : loaded 3 times (x 141B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 3 times (x 203B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 3 times (x 209B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 3 times (x 81B)
Class com.google.common.base.Function                                                 : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 3 times (x 81B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 3 times (x 145B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 3 times (x 93B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 3 times (x 205B)
Class com.google.common.collect.Iterators$9                                           : loaded 3 times (x 77B)
Class com.google.common.collect.Lists                                                 : loaded 3 times (x 67B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 3 times (x 144B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 3 times (x 121B)
Class com.google.common.collect.Iterators$5                                           : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 3 times (x 139B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 3 times (x 117B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 3 times (x 122B)
Class com.google.common.collect.Iterators$4                                           : loaded 3 times (x 78B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 3 times (x 122B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 3 times (x 142B)
Class com.google.common.collect.Iterators$1                                           : loaded 3 times (x 77B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 3 times (x 205B)
Class org.gradle.api.UncheckedIOException                                             : loaded 3 times (x 78B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 3 times (x 203B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 3 times (x 148B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 3 times (x 92B)
Class com.google.common.collect.ObjectArrays                                          : loaded 3 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 3 times (x 78B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 3 times (x 91B)
Class com.google.common.collect.BiMap                                                 : loaded 3 times (x 66B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 3 times (x 77B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 3 times (x 148B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 3 times (x 203B)
Class com.google.common.collect.Iterators                                             : loaded 3 times (x 67B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 3 times (x 213B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 3 times (x 73B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 3 times (x 77B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 3 times (x 76B)
Class com.google.common.base.Joiner                                                   : loaded 3 times (x 75B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 3 times (x 72B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 3 times (x 93B)
Class com.intellij.util.containers.IntObjectHashMap$ArrayProducer                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.noarg.NoArgModel                  : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 108B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$2     : loaded 2 times (x 76B)
Class com.google.common.collect.ReverseNaturalOrdering                                : loaded 2 times (x 110B)
Class kotlin.text.MatcherMatchResult$groupValues$1                                    : loaded 2 times (x 194B)
Class kotlin.sequences.SequencesKt___SequencesKt                                      : loaded 2 times (x 67B)
Class kotlin.collections.AbstractList$Companion                                       : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class [Lcom.android.ide.common.repository.AgpVersion$PreviewKind;                     : loaded 2 times (x 65B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 65B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService: loaded 2 times (x 73B)
Class com.google.common.collect.Ordering                                              : loaded 2 times (x 110B)
Class kotlin.sequences.SequencesKt___SequencesKt$flatMap$2                            : loaded 2 times (x 121B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class com.android.builder.model.v2.models.AndroidDsl                                  : loaded 2 times (x 66B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 122B)
Class com.amazon.ion.impl.lite.IonSystemLite                                          : loaded 2 times (x 271B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode                    : loaded 2 times (x 78B)
Class com.amazon.ion.impl.bin._Private_IonRawWriter                                   : loaded 2 times (x 66B)
Class com.amazon.ion.IonWriter                                                        : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__AppendableKt                                             : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableRangeSet                                     : loaded 2 times (x 111B)
Class org.gradle.tooling.internal.gradle.DefaultProjectIdentifier                     : loaded 2 times (x 82B)
Class com.android.ide.common.gradle.Numeric                                           : loaded 2 times (x 73B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 74B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 68B)
Class com.amazon.ion.impl._Private_RecyclingQueue$ElementFactory                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_IonContainer                                       : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIntHashMap                         : loaded 2 times (x 105B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.allopen.AllOpenModel              : loaded 2 times (x 66B)
Class com.google.common.collect.ComparatorOrdering                                    : loaded 2 times (x 111B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class com.android.builder.model.v2.models.BasicAndroidProject                         : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 135B)
Class com.amazon.ion.impl._Private_RecyclingStack$$Iterator                           : loaded 2 times (x 95B)
Class com.amazon.ion.impl.SharedSymbolTable                                           : loaded 2 times (x 89B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter                                      : loaded 2 times (x 165B)
Class com.amazon.ion.SymbolToken                                                      : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__StringNumberConversionsJVMKt                             : loaded 2 times (x 67B)
Class com.google.common.collect.NaturalOrdering                                       : loaded 2 times (x 111B)
Class kotlin.text.MatchResult                                                         : loaded 2 times (x 66B)
Class kotlin.sequences.SequenceScope                                                  : loaded 2 times (x 69B)
Class kotlin.sequences.Sequence                                                       : loaded 2 times (x 66B)
Class org.gradle.tooling.ToolingModelContract                                         : loaded 2 times (x 66B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class [Lorg.objectweb.asm.AnnotationVisitor;                                          : loaded 2 times (x 65B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 135B)
Class com.google.common.util.concurrent.AbstractFuture$SafeAtomicHelper               : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 75B)
Class com.amazon.ion.impl.lite.IonBoolLite                                            : loaded 2 times (x 174B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHash$NULL                          : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt___CollectionsKt$asSequence$$inlined$Sequence$1 : loaded 2 times (x 71B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class org.gradle.internal.impldep.gnu.trove.PrimeFinder                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.kapt.KaptGradleModel              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinDslScriptAdditionalTask           : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ClasspathEntryModel                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class com.amazon.ion.IonTimestamp                                                     : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider$PooledBlockAllocator       : loaded 2 times (x 77B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService: loaded 2 times (x 73B)
Class org.gradle.api.JavaVersion                                                      : loaded 2 times (x 75B)
Class org.gradle.tooling.model.Dependency                                             : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.AbstractArtifact                               : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 73B)
Class com.amazon.ion.IonDatagram                                                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingStack$ElementFactory                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1$1   : loaded 2 times (x 74B)
Class com.amazon.ion.impl._Private_ByteTransferSink                                   : loaded 2 times (x 66B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.ResolutionState  : loaded 2 times (x 75B)
Class kotlin.text.StringsKt__RegexExtensionsJVMKt                                     : loaded 2 times (x 67B)
Class com.google.common.collect.AllEqualOrdering                                      : loaded 2 times (x 110B)
Class kotlin.collections.ArraysKt___ArraysJvmKt                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__CollectionsKt                                 : loaded 2 times (x 67B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 74B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class org.jetbrains.plugins.gradle.model.ExternalLibraryDependency                    : loaded 2 times (x 66B)
Class kotlin.text.Regex$Companion                                                     : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableListMultimap                                 : loaded 2 times (x 172B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1     : loaded 2 times (x 76B)
Class com.amazon.ion.impl.lite.IonSymbolLite                                          : loaded 2 times (x 242B)
Class com.amazon.ion.IonValue                                                         : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableRangeSet$AsSet                               : loaded 2 times (x 296B)
Class kotlin.jvm.internal.FunctionReference                                           : loaded 2 times (x 118B)
Class [Lkotlin.coroutines.intrinsics.CoroutineSingletons;                             : loaded 2 times (x 65B)
Class kotlin.jvm.internal.FunctionBase                                                : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntriesList                                                    : loaded 2 times (x 219B)
Class com.google.common.collect.EmptyImmutableListMultimap                            : loaded 2 times (x 172B)
Class com.google.common.collect.BaseImmutableMultimap                                 : loaded 2 times (x 119B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$StreamCloseMode;                   : loaded 2 times (x 65B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerType                        : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonTextWriterBuilder$Mutable                       : loaded 2 times (x 98B)
Class kotlin.comparisons.ComparisonsKt___ComparisonsJvmKt                             : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IteratorsJVMKt                                : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.TestInfo$Execution                             : loaded 2 times (x 75B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.idea.gradleTooling.AndroidAwareGradleModelProvider$Companion: loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.model.IntelliJSettings                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinSourceSetContainer                : loaded 2 times (x 66B)
Class kotlin.Result                                                                   : loaded 2 times (x 68B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class com.google.common.collect.Iterators$SingletonIterator                           : loaded 2 times (x 77B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class com.amazon.ion.impl.IonWriterSystem                                             : loaded 2 times (x 167B)
Class com.amazon.ion.impl.bin.Symbols$2                                               : loaded 2 times (x 103B)
Class com.amazon.ion.impl.bin._Private_IonManagedWriter                               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.android.synthetic.idea.AndroidExtensionsGradleModel        : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.util.GradleVersionUtil                     : loaded 2 times (x 67B)
Class com.android.ide.gradle.model.GradlePropertiesModel                              : loaded 2 times (x 66B)
Class kotlin.sequences.TransformingSequence$iterator$1                                : loaded 2 times (x 75B)
Class kotlin.Pair                                                                     : loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$SupportedPropertyInvoker: loaded 2 times (x 72B)
Class org.gradle.tooling.model.UnsupportedMethodException                             : loaded 2 times (x 78B)
Class com.android.builder.model.v2.ide.BytecodeTransformation                         : loaded 2 times (x 75B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 119B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.amazon.ion.impl.lite.IonContainerLite                                       : loaded 2 times (x 244B)
Class com.amazon.ion.IonBufferConfiguration$OversizedSymbolTableHandler               : loaded 2 times (x 66B)
Class com.amazon.ion.system.IonWriterBuilder                                          : loaded 2 times (x 70B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ToolingStreamApiUtils        : loaded 2 times (x 67B)
Class com.amazon.ion.facet.Faceted                                                    : loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt__MutableCollectionsKt                          : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.CodeShrinker                                   : loaded 2 times (x 75B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper$1           : loaded 2 times (x 72B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class com.amazon.ion.impl.bin.utf8.Poolable                                           : loaded 2 times (x 75B)
Class com.amazon.ion.IonSymbol                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.THash                                     : loaded 2 times (x 77B)
Class kotlin.io.TerminateException                                                    : loaded 2 times (x 78B)
Class kotlin.io.FilesKt__FilePathComponentsKt                                         : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableRangeSet$1                                   : loaded 2 times (x 206B)
Class kotlin.reflect.KCallable                                                        : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class com.amazon.ion.impl.lite.IonStructLite                                          : loaded 2 times (x 294B)
Class com.amazon.ion.impl._Private_ReaderWriter                                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.AbstractSymbolTable                                     : loaded 2 times (x 103B)
Class com.amazon.ion.Decimal                                                          : loaded 2 times (x 129B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode$1  : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService: loaded 2 times (x 73B)
Class com.google.common.collect.Range                                                 : loaded 2 times (x 83B)
Class org.gradle.tooling.internal.adapter.CollectionMapper                            : loaded 2 times (x 69B)
Class [Lcom.android.builder.model.v2.ide.LibraryType;                                 : loaded 2 times (x 65B)
Class com.android.builder.model.v2.ide.LibraryType                                    : loaded 2 times (x 75B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class org.gradle.internal.operations.MultipleBuildOperationFailures                   : loaded 2 times (x 91B)
Class com.amazon.ion.impl.lite.IonFloatLite                                           : loaded 2 times (x 182B)
Class com.amazon.ion.impl._Private_IonTextWriterBuilder                               : loaded 2 times (x 98B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode$2  : loaded 2 times (x 76B)
Class org.gradle.internal.impldep.gnu.trove.Equality                                  : loaded 2 times (x 66B)
Class com.google.common.collect.ExplicitOrdering                                      : loaded 2 times (x 111B)
Class org.jetbrains.plugins.gradle.ExternalDependencyId                               : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingStack                                     : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonReaderBuilder                                   : loaded 2 times (x 91B)
Class com.amazon.ion.IonMutableCatalog                                                : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter                      : loaded 2 times (x 76B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.AndroidGradlePluginProjectFlags$BooleanFlag    : loaded 2 times (x 75B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class com.android.ide.common.gradle.Separator                                         : loaded 2 times (x 75B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 102B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class com.google.common.base.CharMatcher$And                                          : loaded 2 times (x 108B)
Class org.gradle.internal.impldep.gnu.trove.TObjectCanonicalHashingStrategy           : loaded 2 times (x 76B)
Class kotlin.collections.MapsKt__MapsJVMKt                                            : loaded 2 times (x 67B)
Class kotlin.collections.EmptyList                                                    : loaded 2 times (x 170B)
Class org.gradle.internal.time.DefaultTimer                                           : loaded 2 times (x 76B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 73B)
Class org.gradle.tooling.internal.adapter.MethodInvocation                            : loaded 2 times (x 81B)
Class kotlin.sequences.TransformingSequence                                           : loaded 2 times (x 71B)
Class com.intellij.util.ThrowableConsumer                                             : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider$PooledBlockAllocator$1     : loaded 2 times (x 75B)
Class com.amazon.ion.impl.lite.IonStringLite                                          : loaded 2 times (x 208B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewKey              : loaded 2 times (x 68B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class com.android.ide.common.repository.AgpVersion                                    : loaded 2 times (x 71B)
Class com.android.builder.model.v2.CustomSourceDirectory                              : loaded 2 times (x 66B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 84B)
Class com.amazon.ion.impl.bin.Block                                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.lombok.LombokModel                : loaded 2 times (x 66B)
Class com.google.common.collect.NullsFirstOrdering                                    : loaded 2 times (x 111B)
Class kotlin.ResultKt                                                                 : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$PropertyCachingMethodInvoker: loaded 2 times (x 72B)
Class com.android.builder.model.v2.models.ProjectSyncIssues                           : loaded 2 times (x 66B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class com.android.utils.FileUtils                                                     : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.model.ExternalTask                                 : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoderPool$1                            : loaded 2 times (x 72B)
Class com.amazon.ion.impl.lite.IonLoaderLite                                          : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite.IonContext                                             : loaded 2 times (x 66B)
Class com.amazon.ion.impl.IonWriterSystemText                                         : loaded 2 times (x 186B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode;    : loaded 2 times (x 65B)
Class com.amazon.ion.ValueFactory                                                     : loaded 2 times (x 66B)
Class kotlin.collections.ArraysKt__ArraysKt                                           : loaded 2 times (x 67B)
Class kotlin.Result$Failure                                                           : loaded 2 times (x 68B)
Class org.gradle.internal.time.DefaultCountdownTimer                                  : loaded 2 times (x 84B)
Class com.android.builder.model.v2.ide.AaptOptions$Namespacing                        : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonReaderBuilder$TwoElementSequenceInputStream     : loaded 2 times (x 88B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolResolver                   : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_SymbolToken                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode    : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.util.ObjectCollector                       : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.GradleTaskModel                              : loaded 2 times (x 66B)
Class kotlin.collections.SetsKt__SetsJVMKt                                            : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$AdaptingMethodInvoker: loaded 2 times (x 72B)
Class org.gradle.tooling.internal.gradle.DefaultBuildIdentifier                       : loaded 2 times (x 75B)
Class kotlin.Function                                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_IonReaderBuilder$Mutable                           : loaded 2 times (x 91B)
Class com.intellij.openapi.externalSystem.model.project.IExternalSystemSourceType     : loaded 2 times (x 66B)
Class kotlin.io.FileAlreadyExistsException                                            : loaded 2 times (x 78B)
Class kotlin.internal.ProgressionUtilKt                                               : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class settings_cqr77nl3gl6lg9b2yujwf1q4o$_run_closure1                                : loaded 2 times (x 135B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIdentityHashingStrategy            : loaded 2 times (x 74B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.ProjectDependencies: loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 78B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 67B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class settings_cqr77nl3gl6lg9b2yujwf1q4o$_run_closure1$_closure2                      : loaded 2 times (x 135B)
Class com.amazon.ion.IonBufferConfiguration                                           : loaded 2 times (x 69B)
Class com.amazon.ion.impl.bin.Symbols                                                 : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService: loaded 2 times (x 78B)
Class com.android.ide.gradle.model.artifacts.AdditionalClassifierArtifactsModel       : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.gradle.GradleProjectIdentity                        : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableMultimap$Keys                                : loaded 2 times (x 162B)
Class com.android.builder.model.v2.ide.JavaArtifact                                   : loaded 2 times (x 66B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 66B)
Class settings_cqr77nl3gl6lg9b2yujwf1q4o                                              : loaded 2 times (x 175B)
Class [Lcom.amazon.ion.impl.bin.AbstractIonWriter$WriteValueOptimization;             : loaded 2 times (x 65B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService$ReadContext: loaded 2 times (x 68B)
Class kotlin.ranges.ClosedRange                                                       : loaded 2 times (x 66B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState                        : loaded 2 times (x 81B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.adapter.InternalBuildIdentifier: loaded 2 times (x 71B)
Class com.amazon.ion.impl._Private_IonWriter                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder                  : loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService$WriteContext: loaded 2 times (x 69B)
Class kotlin.text.StringsKt__StringsJVMKt                                             : loaded 2 times (x 67B)
Class com.google.common.collect.NullnessCasts                                         : loaded 2 times (x 67B)
Class com.android.builder.model.v2.dsl.SigningConfig                                  : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.AndroidProject                              : loaded 2 times (x 66B)
Class com.android.builder.model.v2.AndroidModel                                       : loaded 2 times (x 66B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.TargetTypeProvider                          : loaded 2 times (x 66B)
Class [Lcom.amazon.ion.SymbolToken;                                                   : loaded 2 times (x 65B)
Class kotlin.text.StringsKt__IndentKt                                                 : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt                                                       : loaded 2 times (x 67B)
Class kotlin.ranges.IntProgression$Companion                                          : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.AnnotationProcessingModel                    : loaded 2 times (x 66B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 71B)
Class org.jetbrains.plugins.gradle.model.ExternalProjectDependency                    : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.KotlinDslScriptsModelSerializationService: loaded 2 times (x 77B)
Class kotlin.reflect.KFunction                                                        : loaded 2 times (x 66B)
Class kotlin.collections.ArrayDeque                                                   : loaded 2 times (x 203B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.google.common.util.concurrent.AbstractFuture$AtomicHelper                   : loaded 2 times (x 74B)
Class com.amazon.ion.system.IonSystemBuilder$Mutable                                  : loaded 2 times (x 71B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolResolverBuilder            : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinGradleModel                       : loaded 2 times (x 66B)
Class kotlin.jvm.internal.Lambda                                                      : loaded 2 times (x 71B)
Class kotlin.collections.MapsKt__MapsKt                                               : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.Variant                                        : loaded 2 times (x 66B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class org.jetbrains.plugins.gradle.model.GradleExtensions                             : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingStack$Recycler                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$LocalSymbolTableView             : loaded 2 times (x 103B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ThrowingRunnable                     : loaded 2 times (x 66B)
Class org.gradle.tooling.model.Model                                                  : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class com.amazon.ion.system.IonBinaryWriterBuilder                                    : loaded 2 times (x 95B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$4                      : loaded 2 times (x 81B)
Class com.amazon.ion.UnknownSymbolException                                           : loaded 2 times (x 82B)
Class com.android.ide.gradle.model.composites.BuildMap                                : loaded 2 times (x 66B)
Class kotlin.text.MatchResult$Destructured                                            : loaded 2 times (x 68B)
Class kotlin.Metadata                                                                 : loaded 2 times (x 66B)
Class kotlin.UninitializedPropertyAccessException                                     : loaded 2 times (x 78B)
Class com.android.builder.model.v2.ide.Library                                        : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.ArtifactDependenciesAdjacencyList              : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.Versions                                    : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class org.jetbrains.plugins.gradle.model.IntelliJProjectSettings                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.annotation.AnnotationBasedPluginModel: loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt___SequencesKt$filterNotNull$1                      : loaded 2 times (x 74B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MethodInvocationCache: loaded 2 times (x 69B)
Class com.android.builder.model.v2.models.ndk.NativeModule                            : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class org.jetbrains.plugins.gradle.model.UnresolvedExternalDependency                 : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.AndroidArtifact                                : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ExternalDependency                           : loaded 2 times (x 66B)
Class com.intellij.util.containers.IntObjectHashMap                                   : loaded 2 times (x 68B)
Class com.google.common.collect.SortedIterable                                        : loaded 2 times (x 66B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.PrivacySandboxSdkInfo                          : loaded 2 times (x 66B)
Class com.android.ide.common.repository.AgpVersion$PreviewKind$Companion              : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 72B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 80B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$1                    : loaded 2 times (x 76B)
Class com.amazon.ion.impl.LocalSymbolTableAsStruct$Factory                            : loaded 2 times (x 75B)
Class [Lcom.amazon.ion.IonType;                                                       : loaded 2 times (x 65B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$SourceSetDependencyModelReadContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyModel.DependencyReadContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.VersionCatalogsModel                         : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ObjectGraphAdapter                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultimap$Values                              : loaded 2 times (x 122B)
Class com.android.builder.model.v2.ide.Installation                                   : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper             : loaded 2 times (x 74B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 2 times (x 108B)
Class org.jetbrains.plugins.gradle.model.ProjectImportModelProvider                   : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ExternalSourceSet                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$2                    : loaded 2 times (x 76B)
Class com.amazon.ion.IonNull                                                          : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.warmUp.GradleTaskWarmUpRequest  : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableSortedAsList                                 : loaded 2 times (x 217B)
Class kotlin.text.RegexKt                                                             : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsKt                         : loaded 2 times (x 67B)
Class com.android.builder.model.v2.models.ModelBuilderParameter                       : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$InvocationHandlerImpl: loaded 2 times (x 73B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 2 times (x 108B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerInfo                        : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$3                    : loaded 2 times (x 76B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$StreamFlushMode                      : loaded 2 times (x 75B)
Class com.amazon.ion.IonBinaryWriter                                                  : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetArtifactIndex.GradleSourceSetArtifactBuildRequest: loaded 2 times (x 66B)
Class kotlin.io.FilesKt__UtilsKt                                                      : loaded 2 times (x 67B)
Class com.google.common.collect.Lists$TransformingRandomAccessList                    : loaded 2 times (x 195B)
Class kotlin.collections.CollectionsKt__CollectionsJVMKt                              : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 86B)
Class org.gradle.tooling.model.build.GradleEnvironment                                : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap$ObjectFactory            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$4                    : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$1: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.model.ExternalProject                              : loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt__IteratorsKt                                   : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt                                                    : loaded 2 times (x 67B)
Class [Lcom.android.builder.model.v2.ide.CodeShrinker;                                : loaded 2 times (x 65B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class com.amazon.ion.IonException                                                     : loaded 2 times (x 80B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$3                      : loaded 2 times (x 81B)
Class com.amazon.ion.impl.bin.utf8.Pool                                               : loaded 2 times (x 70B)
Class com.amazon.ion.impl._Private_SymtabExtendsCache                                 : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$2: loaded 2 times (x 79B)
Class kotlin.text.StringsKt                                                           : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultiset                                     : loaded 2 times (x 159B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class com.amazon.ion.impl._Private_IonSymbol                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonValueLite                                           : loaded 2 times (x 143B)
Class com.amazon.ion.IonContainer                                                     : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider                            : loaded 2 times (x 69B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$3: loaded 2 times (x 79B)
Class org.gradle.util.internal.DefaultGradleVersion$Stage                             : loaded 2 times (x 71B)
Class kotlin.coroutines.jvm.internal.RestrictedContinuationImpl                       : loaded 2 times (x 83B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.AndroidAwareGradleModelProvider         : loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$4: loaded 2 times (x 79B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$Companion: loaded 2 times (x 67B)
Class [Lkotlin.Pair;                                                                  : loaded 2 times (x 65B)
Class kotlin.text.StringsKt__StringNumberConversionsKt                                : loaded 2 times (x 67B)
Class kotlin.io.FilesKt__FileReadWriteKt                                              : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class kotlin.text.MatchNamedGroupCollection                                           : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMutableList                                        : loaded 2 times (x 66B)
Class org.gradle.internal.time.MonotonicClock                                         : loaded 2 times (x 73B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class com.google.common.collect.Lists$ReverseList                                     : loaded 2 times (x 196B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class com.amazon.ion.BufferConfiguration$Builder                                      : loaded 2 times (x 74B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$5: loaded 2 times (x 79B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$SourceSetDependencyModelWriteContext: loaded 2 times (x 68B)
Class kotlin.sequences.SequencesKt___SequencesKt$sortedWith$1                         : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableSortedSet                                    : loaded 2 times (x 296B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.LibraryInfo                                    : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 2 times (x 107B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.IntList                                                 : loaded 2 times (x 73B)
Class com.amazon.ion.impl._Private_RecyclingQueue$ElementIterator                     : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite._Private_LiteDomTrampoline                             : loaded 2 times (x 67B)
Class com.amazon.ion.IonType                                                          : loaded 2 times (x 75B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$6: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap                          : loaded 2 times (x 69B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 67B)
Class com.amazon.ion.impl.lite.ValueFactoryLite                                       : loaded 2 times (x 214B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$7: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.model.RepositoryModels                             : loaded 2 times (x 66B)
Class com.google.common.collect.RegularImmutableSortedSet                             : loaded 2 times (x 296B)
Class kotlin.collections.AbstractList$SubList                                         : loaded 2 times (x 194B)
Class kotlin.jvm.internal.CallableReference                                           : loaded 2 times (x 102B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 142B)
Class com.amazon.ion.impl.bin.WriteBuffer                                             : loaded 2 times (x 74B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter                                  : loaded 2 times (x 162B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$8: loaded 2 times (x 79B)
Class kotlin.ranges.IntRange                                                          : loaded 2 times (x 89B)
Class [Lorg.gradle.api.JavaVersion;                                                   : loaded 2 times (x 65B)
Class org.gradle.tooling.model.idea.IdeaDependencyScope                               : loaded 2 times (x 66B)
Class org.gradle.internal.time.TimeSource                                             : loaded 2 times (x 66B)
Class org.gradle.tooling.model.kotlin.dsl.KotlinDslScriptsModel                       : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultimap                                     : loaded 2 times (x 143B)
Class com.android.builder.model.v2.ide.TestInfo                                       : loaded 2 times (x 66B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class com.android.builder.model.v2.models.VariantDependenciesAdjacencyList            : loaded 2 times (x 66B)
Class build_eoun890h7qlxwfhq6ek4y6yum$_run_closure1                                   : loaded 2 times (x 135B)
Class com.amazon.ion.impl.lite.IonBlobLite                                            : loaded 2 times (x 215B)
Class com.amazon.ion.impl.LocalSymbolTable                                            : loaded 2 times (x 117B)
Class org.jetbrains.plugins.gradle.tooling.serialization.SerializationService         : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.gradle.GradleBuildIdentity                          : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntries                                                        : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.JavaCompileOptions                             : loaded 2 times (x 66B)
Class com.google.common.collect.IndexedImmutableSet$1                                 : loaded 2 times (x 209B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class com.amazon.ion.IonList                                                          : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.adapter.Supplier    : loaded 2 times (x 66B)
Class ijInit1_cxra44aza6j7soyehk88aj27j                                               : loaded 2 times (x 175B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MixInMappingAction   : loaded 2 times (x 76B)
Class org.gradle.internal.exceptions.ResolutionProvider                               : loaded 2 times (x 66B)
Class com.amazon.ion.IonSystem                                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolContext            : loaded 2 times (x 68B)
Class kotlin.text.MatchResult$DefaultImpls                                            : loaded 2 times (x 67B)
Class org.gradle.tooling.model.idea.IdeaCompilerOutput                                : loaded 2 times (x 66B)
Class org.gradle.tooling.model.ProjectIdentifier                                      : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class build_eoun890h7qlxwfhq6ek4y6yum                                                 : loaded 2 times (x 176B)
Class com.android.builder.model.v2.ide.ComponentInfo                                  : loaded 2 times (x 66B)
Class com.google.common.base.Charsets                                                 : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.ProjectType                                    : loaded 2 times (x 75B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService: loaded 2 times (x 73B)
Class kotlin.io.FileSystemException                                                   : loaded 2 times (x 78B)
Class com.android.builder.model.v2.dsl.BaseConfig                                     : loaded 2 times (x 66B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 67B)
Class com.amazon.ion.impl.lite.IonIntLite                                             : loaded 2 times (x 185B)
Class com.amazon.ion.util._Private_FastAppendable                                     : loaded 2 times (x 66B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode;                 : loaded 2 times (x 65B)
Class org.gradle.internal.time.TimeSource$1                                           : loaded 2 times (x 73B)
Class kotlin.jvm.internal.DefaultConstructorMarker                                    : loaded 2 times (x 67B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 65B)
Class com.amazon.ion.impl.lite.IonSexpLite                                            : loaded 2 times (x 486B)
Class com.amazon.ion.impl.lite.IonListLite                                            : loaded 2 times (x 486B)
Class com.amazon.ion.IonFloat                                                         : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class com.amazon.ion.impl.LocalSymbolTable$Factory                                    : loaded 2 times (x 73B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode       : loaded 2 times (x 76B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService: loaded 2 times (x 73B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class com.amazon.ion.impl.LocalSymbolTableAsStruct                                    : loaded 2 times (x 122B)
Class com.amazon.ion.impl.lite.IonTimestampLite                                       : loaded 2 times (x 188B)
Class org.jetbrains.plugins.gradle.model.FileCollectionDependency                     : loaded 2 times (x 66B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHashingStrategy                    : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.utilDummyModel.DummyModel       : loaded 2 times (x 66B)
Class kotlin.collections.EmptyIterator                                                : loaded 2 times (x 85B)
Class kotlin.text.StringsKt__RegexExtensionsKt                                        : loaded 2 times (x 67B)
Class org.gradle.internal.time.Clock                                                  : loaded 2 times (x 66B)
Class com.android.builder.model.v2.dsl.ProductFlavor                                  : loaded 2 times (x 66B)
Class [Lcom.android.builder.model.v2.ide.TestInfo$Execution;                          : loaded 2 times (x 65B)
Class [Lorg.objectweb.asm.AnnotationWriter;                                           : loaded 2 times (x 65B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 78B)
Class com.amazon.ion.IonString                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleSourceSetDependencyModel               : loaded 2 times (x 66B)
Class sync_studio_tooling10_qq92yx5xm5tihf4ihjz64yli                                  : loaded 2 times (x 175B)
Class com.google.common.collect.CompoundOrdering                                      : loaded 2 times (x 111B)
Class kotlin.sequences.SequencesKt__SequencesJVMKt                                    : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.RestrictedSuspendLambda                          : loaded 2 times (x 87B)
Class org.gradle.tooling.internal.adapter.TypeInspector                               : loaded 2 times (x 69B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$NoOpDecoration       : loaded 2 times (x 75B)
Class org.gradle.tooling.model.Element                                                : loaded 2 times (x 66B)
Class kotlin.collections.AbstractList                                                 : loaded 2 times (x 193B)
Class kotlin.collections.AbstractCollection                                           : loaded 2 times (x 113B)
Class com.google.common.collect.Lists$ReverseList$1                                   : loaded 2 times (x 95B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class com.google.common.util.concurrent.AbstractFuture$SynchronizedHelper             : loaded 2 times (x 74B)
Class com.amazon.ion.BufferConfiguration                                              : loaded 2 times (x 69B)
Class com.amazon.ion.system.SimpleCatalog                                             : loaded 2 times (x 87B)
Class com.intellij.gradle.toolingExtension.impl.model.taskModel.GradleTaskSerialisationService: loaded 2 times (x 72B)
Class com.android.ide.gradle.model.LegacyV1AgpVersionModel                            : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__StringBuilderJVMKt                                       : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableRangeSet$Builder                             : loaded 2 times (x 73B)
Class kotlin.text.Regex                                                               : loaded 2 times (x 68B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt$sequence$$inlined$Sequence$1    : loaded 2 times (x 71B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap$1                        : loaded 2 times (x 73B)
Class org.gradle.internal.impldep.com.google.common.base.Preconditions                : loaded 2 times (x 67B)
Class org.gradle.tooling.model.internal.ImmutableDomainObjectSet                      : loaded 2 times (x 151B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class com.android.builder.model.v2.ide.AndroidGradlePluginProjectFlags                : loaded 2 times (x 66B)
Class [Lcom.android.builder.model.v2.ide.AndroidGradlePluginProjectFlags$BooleanFlag; : loaded 2 times (x 65B)
Class com.android.builder.model.v2.ide.BasicVariant                                   : loaded 2 times (x 66B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 75B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 67B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$StreamFlushMode;                   : loaded 2 times (x 65B)
Class com.amazon.ion.UnsupportedIonVersionException                                   : loaded 2 times (x 81B)
Class kotlin.comparisons.ComparisonsKt__ComparisonsKt                                 : loaded 2 times (x 67B)
Class kotlin.ranges.RangesKt__RangesKt                                                : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt___SetsKt                                              : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt__SequencesKt                                       : loaded 2 times (x 67B)
Class kotlin.coroutines.Continuation                                                  : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class com.android.builder.model.v2.ide.BundleInfo                                     : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.SourceProvider                                 : loaded 2 times (x 66B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 76B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState                      : loaded 2 times (x 76B)
Class com.amazon.ion.IonStruct                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleBuildScriptClasspathModel              : loaded 2 times (x 66B)
Class kotlin.sequences.FilteringSequence                                              : loaded 2 times (x 71B)
Class kotlin.comparisons.ComparisonsKt                                                : loaded 2 times (x 67B)
Class kotlin.comparisons.ComparisonsKt___ComparisonsKt                                : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.BaseContinuationImpl                             : loaded 2 times (x 83B)
Class kotlin.Result$Companion                                                         : loaded 2 times (x 67B)
Class org.gradle.util.internal.DefaultGradleVersion                                   : loaded 2 times (x 80B)
Class com.google.common.base.CharMatcher$Ascii                                        : loaded 2 times (x 108B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt                                 : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__ReversedViewsKt                               : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 75B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class com.amazon.ion.impl.IonWriterSystemTextMarkup                                   : loaded 2 times (x 188B)
Class com.amazon.ion.impl.bin.BlockAllocator                                          : loaded 2 times (x 76B)
Class kotlin.jvm.internal.FunctionReferenceImpl                                       : loaded 2 times (x 118B)
Class kotlin.collections.CollectionsKt___CollectionsJvmKt                             : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultisetGwtSerializationDependencies         : loaded 2 times (x 121B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class [Lcom.android.builder.model.v2.ide.ProjectType;                                 : loaded 2 times (x 65B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 2 times (x 108B)
Class com.amazon.ion.impl._Private_RecyclingQueue                                     : loaded 2 times (x 76B)
Class com.amazon.ion.system.IonWriterBuilderBase                                      : loaded 2 times (x 79B)
Class kotlin.TuplesKt                                                                 : loaded 2 times (x 67B)
Class kotlin.jvm.internal.markers.KMutableIterable                                    : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt___SequencesJvmKt                                   : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$DefaultViewBuilder   : loaded 2 times (x 76B)
Class kotlin.Unit                                                                     : loaded 2 times (x 67B)
Class [Lcom.android.builder.model.v2.ide.AaptOptions$Namespacing;                     : loaded 2 times (x 65B)
Class com.android.ide.common.gradle.Version$Companion$ParseState                      : loaded 2 times (x 66B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class kotlin.ranges.RangesKt                                                          : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewGraphDetails$1   : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class com.android.ide.common.repository.AgpVersion$Companion                          : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$Waiter                         : loaded 2 times (x 68B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$1                                    : loaded 2 times (x 73B)
Class [Lcom.amazon.ion.SymbolTable;                                                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.IonBinaryWriterAdapter$Factory                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.BlockedBuffer$BufferedOutputStream                          : loaded 2 times (x 86B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService$ReadContext: loaded 2 times (x 68B)
Class kotlin.collections.ArraysUtilJVM                                                : loaded 2 times (x 67B)
Class kotlin.sequences.FlatteningSequence$iterator$1                                  : loaded 2 times (x 75B)
Class kotlin.jvm.functions.Function0                                                  : loaded 2 times (x 66B)
Class kotlin.jvm.internal.CollectionToArray                                           : loaded 2 times (x 67B)
Class kotlin.io.FilesKt__FileTreeWalkKt                                               : loaded 2 times (x 67B)
Class org.gradle.tooling.model.idea.IdeaDependency                                    : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.AaptOptions                                    : loaded 2 times (x 66B)
Class [Lcom.android.builder.model.v2.ide.BytecodeTransformation;                      : loaded 2 times (x 65B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class com.android.ide.common.gradle.Version$Companion                                 : loaded 2 times (x 67B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 2 times (x 107B)
Class com.amazon.ion.impl.bin.AbstractIonWriter$WriteValueOptimization                : loaded 2 times (x 75B)
Class kotlin.collections.AbstractMutableList                                          : loaded 2 times (x 202B)
Class kotlin.jvm.functions.Function1                                                  : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.ndk.NativeModelBuilderParameter             : loaded 2 times (x 66B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class kotlin.sequences.FilteringSequence$iterator$1                                   : loaded 2 times (x 75B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$Companion: loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.BlockAllocatorProvider                                  : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService: loaded 2 times (x 73B)
Class org.jetbrains.plugins.gradle.model.GradleSourceSetModel                         : loaded 2 times (x 66B)
Class kotlin.KotlinNothingValueException                                              : loaded 2 times (x 78B)
Class kotlin.jvm.functions.Function2                                                  : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 75B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 2 times (x 109B)
Class com.amazon.ion.impl.lite.IonSequenceLite                                        : loaded 2 times (x 428B)
Class com.amazon.ion.impl._Private_Utils$1                                            : loaded 2 times (x 85B)
Class com.amazon.ion.impl.SymbolTableAsStruct                                         : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractRangeSet                                      : loaded 2 times (x 110B)
Class kotlin.collections.MapsKt___MapsJvmKt                                           : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap$AbsentValueProvider     : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class com.google.common.cache.CacheBuilder$3                                          : loaded 2 times (x 68B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl._Private_IonValue                                           : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.AbstractIonWriter                                       : loaded 2 times (x 157B)
Class org.jetbrains.plugins.gradle.tooling.util.ObjectCollector$Processor             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.samWithReceiver.SamWithReceiverModel: loaded 2 times (x 66B)
Class kotlin.jvm.functions.Function4                                                  : loaded 2 times (x 66B)
Class kotlin.collections.EmptySet                                                     : loaded 2 times (x 118B)
Class kotlin.collections.CollectionsKt___CollectionsKt                                : loaded 2 times (x 67B)
Class kotlin.annotation.Target                                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$2                      : loaded 2 times (x 81B)
Class org.gradle.internal.exceptions.NonGradleCauseExceptionsHolder                   : loaded 2 times (x 66B)
Class org.gradle.tooling.model.idea.IdeaModuleDependency                              : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ViewBuilder                                 : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.AndroidLibraryData                             : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.ApiVersion                                     : loaded 2 times (x 66B)
Class com.android.ide.common.gradle.Version                                           : loaded 2 times (x 71B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 67B)
Class com.amazon.ion.system.IonReaderBuilder                                          : loaded 2 times (x 89B)
Class org.jetbrains.plugins.gradle.model.AnnotationProcessingConfig                   : loaded 2 times (x 66B)
Class com.amazon.ion.IonReader                                                        : loaded 2 times (x 66B)
Class kotlin.text.StringsKt___StringsJvmKt                                            : loaded 2 times (x 67B)
Class kotlin.reflect.KDeclarationContainer                                            : loaded 2 times (x 66B)
Class kotlin.annotation.Retention                                                     : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.Versions$Version                            : loaded 2 times (x 66B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 66B)
Class com.amazon.ion.IonClob                                                          : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.ModelBuilderService$Parameter              : loaded 2 times (x 66B)
Class kotlin.io.FilesKt                                                               : loaded 2 times (x 67B)
Class kotlin.collections.MapsKt___MapsKt                                              : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt___ArraysKt                                          : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$WriteThroughEntry                            : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$HashIterator                                 : loaded 2 times (x 82B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class com.amazon.ion.impl.lite.IonLobLite                                             : loaded 2 times (x 180B)
Class com.amazon.ion.impl.lite.IonTextLite                                            : loaded 2 times (x 177B)
Class com.amazon.ion.IonNumber                                                        : loaded 2 times (x 66B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIntProcedure                       : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt__MapWithDefaultKt                                     : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ReflectionMethodInvoker: loaded 2 times (x 72B)
Class [Lcom.android.ide.common.gradle.Separator;                                      : loaded 2 times (x 65B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class com.google.common.base.Splitter$1$1                                             : loaded 2 times (x 82B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState;                     : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoder                                  : loaded 2 times (x 76B)
Class com.amazon.ion.IonBool                                                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.assignment.AssignmentModel        : loaded 2 times (x 66B)
Class kotlin.text.StringsKt___StringsKt                                               : loaded 2 times (x 67B)
Class com.google.common.collect.NullsLastOrdering                                     : loaded 2 times (x 111B)
Class kotlin.collections.CollectionsKt__MutableCollectionsJVMKt                       : loaded 2 times (x 67B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 2 times (x 107B)
Class kotlin.enums.EnumEntriesKt                                                      : loaded 2 times (x 67B)
Class com.android.builder.model.v2.models.VariantDependencies                         : loaded 2 times (x 66B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 65B)
Class com.amazon.ion.IonLoader                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleProperty                               : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableRangeSet$ComplementRanges                    : loaded 2 times (x 203B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class com.android.builder.model.v2.ide.TestedTargetVariant                            : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_Utils                                              : loaded 2 times (x 67B)
Class kotlin.ranges.IntRange$Companion                                                : loaded 2 times (x 67B)
Class kotlin.text.MatcherMatchResult$groups$1                                         : loaded 2 times (x 147B)
Class kotlin.collections.EmptyMap                                                     : loaded 2 times (x 105B)
Class kotlin.reflect.KAnnotatedElement                                                : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMutableCollection                                  : loaded 2 times (x 66B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt                                       : loaded 2 times (x 67B)
Class org.gradle.internal.exceptions.MultiCauseException                              : loaded 2 times (x 66B)
Class org.gradle.internal.time.CountdownTimer                                         : loaded 2 times (x 66B)
Class kotlin.jvm.internal.Intrinsics                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 102B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService$ReadContext: loaded 2 times (x 69B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyModel.DependencyWriteContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.parcelize.ParcelizeGradleModel    : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt                                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IterablesKt                                   : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.CoroutineSingletons                                : loaded 2 times (x 75B)
Class org.gradle.tooling.model.ProjectModel                                           : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMultimap$1                                   : loaded 2 times (x 78B)
Class com.android.ide.common.gradle.Version$Companion$ParseState$EMPTY                : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoderPool                              : loaded 2 times (x 70B)
Class org.jetbrains.plugins.gradle.model.GradleConfiguration                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_IonSystem                                          : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration$OversizedValueHandler                        : loaded 2 times (x 66B)
Class kotlin.ranges.OpenEndRange                                                      : loaded 2 times (x 66B)
Class kotlin.sequences.SequenceBuilderIterator                                        : loaded 2 times (x 81B)
Class kotlin.KotlinNullPointerException                                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableMultimap$2                                   : loaded 2 times (x 77B)
Class com.android.ide.common.repository.AgpVersion$Companion$WhenMappings             : loaded 2 times (x 67B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 145B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 2 times (x 107B)
Class com.amazon.ion.IonDecimal                                                       : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.tests.ExternalTestsModel                     : loaded 2 times (x 66B)
Class kotlin.ranges.RangesKt___RangesKt                                               : loaded 2 times (x 67B)
Class org.gradle.tooling.model.idea.IdeaLanguageLevel                                 : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$SafeMethodInvoker    : loaded 2 times (x 72B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap$WeakKey                 : loaded 2 times (x 75B)
Class org.gradle.internal.exceptions.DefaultMultiCauseException                       : loaded 2 times (x 91B)
Class com.android.builder.model.v2.ide.ProjectInfo                                    : loaded 2 times (x 66B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 67B)
Class kotlin.NotImplementedError                                                      : loaded 2 times (x 78B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$StreamCloseMode                      : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonBinaryWriterBuilder$Mutable                     : loaded 2 times (x 111B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService: loaded 2 times (x 73B)
Class kotlin.collections.ArraysKt                                                     : loaded 2 times (x 67B)
Class kotlin.collections.ArrayDeque$Companion                                         : loaded 2 times (x 67B)
Class org.gradle.tooling.model.HierarchicalElement                                    : loaded 2 times (x 66B)
Class com.android.builder.model.v2.dsl.DependenciesInfo                               : loaded 2 times (x 66B)
Class kotlin.sequences.FlatteningSequence                                             : loaded 2 times (x 71B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class kotlin.text.MatcherMatchResult                                                  : loaded 2 times (x 76B)
Class com.google.common.base.CharMatcher                                              : loaded 2 times (x 107B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PatchPoint                           : loaded 2 times (x 69B)
Class com.amazon.ion.IonBlob                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.IonLob                                                           : loaded 2 times (x 66B)
Class com.amazon.ion.IonSequence                                                      : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService: loaded 2 times (x 73B)
Class com.google.common.collect.ByFunctionOrdering                                    : loaded 2 times (x 111B)
Class kotlin.collections.SetsKt__SetsKt                                               : loaded 2 times (x 67B)
Class kotlin.coroutines.EmptyCoroutineContext                                         : loaded 2 times (x 73B)
Class org.gradle.internal.time.Time                                                   : loaded 2 times (x 67B)
Class com.android.ide.common.repository.AgpVersion$PreviewKind                        : loaded 2 times (x 75B)
Class com.android.builder.model.v2.models.ProjectGraph                                : loaded 2 times (x 66B)
Class com.android.ide.common.gradle.Version$Companion$ParseState$NUMERIC              : loaded 2 times (x 70B)
Class kotlin.text.MatchGroupCollection                                                : loaded 2 times (x 66B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 101B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$1                                            : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 2 times (x 108B)
Class com.amazon.ion.system.IonTextWriterBuilder                                      : loaded 2 times (x 94B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1$1$1 : loaded 2 times (x 72B)
Class com.amazon.ion.impl._Private_LocalSymbolTableFactory                            : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService: loaded 2 times (x 73B)
Class com.google.common.collect.RangeGwtSerializationDependencies                     : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt__ArraysJVMKt                                        : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class com.amazon.ion.impl.lite.IonNullLite                                            : loaded 2 times (x 171B)
Class [Lcom.intellij.openapi.externalSystem.model.project.dependencies.ResolutionState;: loaded 2 times (x 65B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHash                               : loaded 2 times (x 90B)
Class org.jetbrains.plugins.gradle.model.DependencyAccessorsModel                     : loaded 2 times (x 66B)
Class kotlin.text.StringsKt__StringBuilderKt                                          : loaded 2 times (x 67B)
Class com.google.common.collect.RangeSet                                              : loaded 2 times (x 66B)
Class kotlin.jvm.internal.CallableReference$NoReceiver                                : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 86B)
Class kotlin.text.StringsKt__StringsKt                                                : loaded 2 times (x 67B)
Class com.amazon.ion.impl.lite.IonClobLite                                            : loaded 2 times (x 216B)
Class com.amazon.ion.impl._Private_ValueFactory                                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$1                  : loaded 2 times (x 78B)
Class [Lcom.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode; : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinMPPGradleModel                    : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyDownloadPolicyModel.GradleDependencyDownloadPolicy: loaded 2 times (x 66B)
Class kotlin.NoWhenBranchMatchedException                                             : loaded 2 times (x 78B)
Class org.gradle.tooling.model.BuildIdentifier                                        : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.VectorDrawablesOptions                         : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 2 times (x 107B)
Class com.amazon.ion.IonInt                                                           : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$2                  : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.idea.gradleTooling.PrepareKotlinIdeImportTaskModel         : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.Edge                                           : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.VariantDependenciesFlatList                 : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$3                  : loaded 2 times (x 78B)
Class com.android.ide.gradle.model.GradlePluginModel                                  : loaded 2 times (x 66B)
Class com.google.common.collect.ReverseOrdering                                       : loaded 2 times (x 111B)
Class org.gradle.tooling.model.build.JavaEnvironment                                  : loaded 2 times (x 66B)
Class com.android.builder.model.v2.ide.SourceSetContainer                             : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class com.amazon.ion.impl.lite.IonDecimalLite                                         : loaded 2 times (x 184B)
Class org.jetbrains.plugins.gradle.model.GradleExtension                              : loaded 2 times (x 66B)
Class com.amazon.ion.IonBufferConfiguration$Builder                                   : loaded 2 times (x 74B)
Class org.gradle.internal.time.Timer                                                  : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMappedMarker                                       : loaded 2 times (x 66B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableMultimap$Builder                             : loaded 2 times (x 82B)
Class com.android.builder.model.v2.ide.LintOptions                                    : loaded 2 times (x 66B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 72B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$ReadContext: loaded 2 times (x 68B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerType;                     : loaded 2 times (x 65B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewGraphDetails     : loaded 2 times (x 68B)
Class org.gradle.tooling.model.idea.IdeaProject                                       : loaded 2 times (x 66B)
Class org.gradle.tooling.model.DomainObjectSet                                        : loaded 2 times (x 66B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableMultimap$EntryCollection                     : loaded 2 times (x 124B)
Class com.android.builder.model.v2.ide.BasicArtifact                                  : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$1                      : loaded 2 times (x 81B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoder$Result                           : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.utf8.Pool$Allocator                                     : loaded 2 times (x 66B)
Class com.amazon.ion.IonText                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.SymbolTokenImpl                                             : loaded 2 times (x 77B)
Class com.amazon.ion.SubstituteSymbolTableException                                   : loaded 2 times (x 80B)
Class kotlin.coroutines.jvm.internal.SuspendFunction                                  : loaded 2 times (x 66B)
Class org.gradle.util.GradleVersion                                                   : loaded 2 times (x 79B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.google.common.collect.ForwardingObject                                      : loaded 2 times (x 68B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportDescriptor                 : loaded 2 times (x 72B)
Class com.amazon.ion.SymbolTable                                                      : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.taskModel.GradleTaskSerialisationService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.javaModel.JavaGradleManifestModel                  : loaded 2 times (x 66B)
Class kotlin.jvm.internal.Ref$BooleanRef                                              : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt                      : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.CoroutineStackFrame                              : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MethodInvocationCache$MethodInvocationKey: loaded 2 times (x 69B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$SetFuture                      : loaded 2 times (x 71B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState;                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl._Private_IonWriterBase                                      : loaded 2 times (x 158B)
Class com.amazon.ion.system.IonSystemBuilder                                          : loaded 2 times (x 71B)
Class com.amazon.ion.IonCatalog                                                       : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$SourceSetModelReadContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.utilTurnOffDefaultTasksModel.TurnOffDefaultTasks: loaded 2 times (x 66B)
Class kotlin.io.NoSuchFileException                                                   : loaded 2 times (x 78B)
Class kotlin.collections.CollectionsKt                                                : loaded 2 times (x 67B)
Class com.android.builder.model.v2.dsl.BuildType                                      : loaded 2 times (x 66B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class com.google.common.collect.UsingToStringOrdering                                 : loaded 2 times (x 110B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$SourceSetModelWriteContext: loaded 2 times (x 68B)
Class kotlin.coroutines.CoroutineContext                                              : loaded 2 times (x 66B)
Class kotlin.coroutines.jvm.internal.DebugProbesKt                                    : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap                         : loaded 2 times (x 72B)
Class org.gradle.tooling.GradleConnectionException                                    : loaded 2 times (x 79B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$1                    : loaded 2 times (x 71B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewDecoration       : loaded 2 times (x 66B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class com.android.builder.model.v2.ide.ViewBindingOptions                             : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingQueue$Recycler                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.ContainerlessContext                                   : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService$ReadContext: loaded 2 times (x 68B)
Class com.google.common.collect.LexicographicalOrdering                               : loaded 2 times (x 111B)
Class org.gradle.tooling.internal.adapter.MethodInvoker                               : loaded 2 times (x 66B)
Class com.android.ide.common.gradle.Part                                              : loaded 2 times (x 73B)
Class com.google.common.collect.Lists$RandomAccessReverseList                         : loaded 2 times (x 196B)
Class com.amazon.ion.UnexpectedEofException                                           : loaded 2 times (x 80B)
Class com.amazon.ion.IonSexp                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.ReadOnlyValueException                                           : loaded 2 times (x 80B)
Class com.amazon.ion.impl._Private_LocalSymbolTable                                   : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration$DataHandler                                  : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_IonBinaryWriterBuilder                             : loaded 2 times (x 111B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.android.ide.gradle.model.LegacyAndroidGradlePluginProperties                : loaded 2 times (x 66B)
Class kotlin.ranges.IntProgression                                                    : loaded 2 times (x 77B)
Class kotlin.jvm.KotlinReflectionNotSupportedError                                    : loaded 2 times (x 78B)
Class org.gradle.tooling.model.gradle.GradleScript                                    : loaded 2 times (x 66B)

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 0 days 9:04 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (3876M free)
TotalPageFile size 22476M (AvailPageFile size 17M)
current process WorkingSet (physical memory assigned to process): 630M, peak: 633M
current process commit charge ("private bytes"): 663M, peak: 686M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109) for windows-amd64 JRE (21.0.6+-13368085-b895.109), built on 2025-04-16T17:01:31Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
