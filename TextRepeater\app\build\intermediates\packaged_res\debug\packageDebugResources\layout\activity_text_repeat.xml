<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Text_Repeat"
    android:background="@color/mother_layout_color"
    >



   <androidx.core.widget.NestedScrollView
       android:layout_width="match_parent"
       android:layout_height="match_parent"
       >

       <androidx.constraintlayout.widget.ConstraintLayout
           android:layout_width="match_parent"
           android:layout_height="match_parent"
           >


    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/textRepeat_materialToolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:navigationIcon="@drawable/baseline_arrow_back_ios_24"
        app:title="Text Repeater"
        app:menu="@menu/settings_item"
        app:navigationIconTint="@color/toolbar_text_color"
        app:titleTextColor="@color/toolbar_text_color"
        >


    </com.google.android.material.appbar.MaterialToolbar>

    <EditText
        android:id="@+id/editEnterText"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="30dp"
        android:background="@drawable/edittext_backround"
        android:drawableStart="@drawable/baseline_text_fields_24"
        android:ems="10"
        android:hint=" Enter text"
        android:paddingEnd="10dp"
        android:inputType="text"
        android:maxLength="150"
        android:paddingLeft="16dp"
        android:textColor="@color/primary_text"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.402"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textRepeat_materialToolbar"
        app:layout_constraintVertical_bias="0.0" />

    <EditText
        android:id="@+id/editLimit"
        android:layout_width="200dp"
        android:layout_height="50dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="28dp"
        android:background="@drawable/edittext_backround"
        android:drawableStart="@drawable/baseline_repeat_24"
        android:ems="10"
        android:hint=" Repetition limit"
        android:inputType="number"
        android:paddingLeft="16dp"
        android:paddingEnd="10dp"
        android:maxLength="5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/editEnterText"
        app:layout_constraintVertical_bias="0.0" />

    <Button
        android:id="@+id/buttonRepeat"
        android:layout_width="175dp"
        android:layout_height="58dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="26dp"
        android:backgroundTint="@color/love"
        android:text="Generate"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/editLimit"
        app:layout_constraintVertical_bias="0.0" />

    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/checkBoxLine"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="New Line"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/buttonRepeat"
        app:layout_constraintTop_toBottomOf="@+id/editLimit"
        app:layout_constraintVertical_bias="0.0"
        android:layout_marginTop="32dp"
        android:layout_marginStart="28dp"
        app:buttonTint="@color/love"
        android:textSize="16.5sp"
        android:fontFamily="@font/alice"

        />


    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/buttonChoose"
        android:layout_width="wrap_content"
        android:layout_height="48dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="28dp"
        android:layout_marginEnd="16dp"
        android:gravity="start|center_vertical"
        android:padding="10dp"
        android:background="@drawable/button_back"
        android:text="Choose message"
        android:textAllCaps="false"
        android:elevation="0dp"
        android:textColor="@color/secondary_text"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toEndOf="@+id/editLimit"
        app:layout_constraintTop_toBottomOf="@+id/editEnterText"
        app:layout_constraintVertical_bias="0.0" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout14"
        android:layout_width="match_parent"
        android:layout_height="330dp"
        android:layout_marginStart="16dp"
        android:background="@color/mother_layout_color"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/buttonRepeat"
        app:layout_constraintVertical_bias="0.0"
        >


        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rcyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>

           <androidx.constraintlayout.widget.ConstraintLayout
               android:id="@+id/copy_share_Layout"
               android:layout_width="match_parent"
               android:layout_height="58dp"
               android:layout_marginStart="16dp"
               android:layout_marginTop="2dp"
               android:layout_marginEnd="16dp"
               android:layout_marginBottom="75dp"
               android:background="@drawable/copy_share_back"
               app:layout_constraintBottom_toBottomOf="parent"
               app:layout_constraintEnd_toEndOf="parent"
               app:layout_constraintHorizontal_bias="0.0"
               app:layout_constraintStart_toStartOf="parent"
               app:layout_constraintTop_toBottomOf="@+id/constraintLayout14"
               app:layout_constraintVertical_bias="0.0"
               android:visibility="gone"
               >


               <androidx.constraintlayout.widget.ConstraintLayout
                   android:id="@+id/layoutCopy"
                   android:layout_width="170dp"
                   android:layout_height="match_parent"
                   android:clickable="true"
                   android:foreground="?attr/selectableItemBackground"
                   app:layout_constraintBottom_toBottomOf="parent"
                   app:layout_constraintEnd_toEndOf="parent"
                   app:layout_constraintHorizontal_bias="0.0"
                   app:layout_constraintStart_toStartOf="parent"
                   app:layout_constraintTop_toTopOf="parent"
                   app:layout_constraintVertical_bias="0.0">


                   <ImageView
                       android:id="@+id/imageView10"
                       android:layout_width="26dp"
                       android:layout_height="26dp"
                       android:layout_marginStart="39dp"
                       android:src="@drawable/baseline_content_copy_24"
                       app:layout_constraintBottom_toBottomOf="parent"
                       app:layout_constraintEnd_toEndOf="parent"
                       app:layout_constraintHorizontal_bias="0.0"
                       app:layout_constraintStart_toStartOf="parent"
                       app:layout_constraintTop_toTopOf="parent" />

                   <TextView
                       android:id="@+id/textView41"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:layout_marginStart="9dp"
                       android:fontFamily="@font/allerta"
                       android:text="Copy"
                       android:textColor="@color/secondary_text"
                       android:textSize="16sp"
                       app:layout_constraintBottom_toBottomOf="parent"
                       app:layout_constraintEnd_toEndOf="parent"
                       app:layout_constraintHorizontal_bias="0.0"
                       app:layout_constraintStart_toEndOf="@+id/imageView10"
                       app:layout_constraintTop_toTopOf="parent"
                       app:layout_constraintVertical_bias="0.508" />


               </androidx.constraintlayout.widget.ConstraintLayout>

               <androidx.constraintlayout.widget.ConstraintLayout
                   android:id="@+id/layoutShare"
                   android:layout_width="170dp"
                   android:layout_height="match_parent"
                   android:clickable="true"
                   android:foreground="?attr/selectableItemBackground"
                   app:layout_constraintBottom_toBottomOf="parent"
                   app:layout_constraintEnd_toEndOf="parent"
                   app:layout_constraintHorizontal_bias="1.0"
                   app:layout_constraintStart_toEndOf="@+id/layoutCopy"
                   app:layout_constraintTop_toTopOf="parent">

                   <ImageView
                       android:id="@+id/imageView4"
                       android:layout_width="26dp"
                       android:layout_height="26dp"
                       android:layout_marginStart="36dp"
                       android:src="@drawable/baseline_share_24"
                       app:layout_constraintBottom_toBottomOf="parent"
                       app:layout_constraintEnd_toEndOf="parent"
                       app:layout_constraintHorizontal_bias="0.0"
                       app:layout_constraintStart_toStartOf="parent"
                       app:layout_constraintTop_toTopOf="parent" />

                   <TextView
                       android:id="@+id/textView4"
                       android:layout_width="wrap_content"
                       android:layout_height="wrap_content"
                       android:layout_marginStart="9dp"
                       android:fontFamily="@font/allerta"
                       android:text="Share"
                       android:textColor="@color/secondary_text"
                       android:textSize="16sp"
                       app:layout_constraintBottom_toBottomOf="parent"
                       app:layout_constraintEnd_toEndOf="parent"
                       app:layout_constraintHorizontal_bias="0.0"
                       app:layout_constraintStart_toEndOf="@+id/imageView4"
                       app:layout_constraintTop_toTopOf="parent"
                       app:layout_constraintVertical_bias="0.508" />

               </androidx.constraintlayout.widget.ConstraintLayout>


               <View
                   android:layout_width="2dp"
                   android:layout_height="match_parent"
                   android:layout_marginVertical="10dp"
                   android:background="@drawable/view_back"
                   app:layout_constraintBottom_toBottomOf="parent"
                   app:layout_constraintEnd_toStartOf="@+id/layoutShare"
                   app:layout_constraintStart_toEndOf="@+id/layoutCopy"
                   app:layout_constraintTop_toTopOf="parent" />


           </androidx.constraintlayout.widget.ConstraintLayout>


       </androidx.constraintlayout.widget.ConstraintLayout>

   </androidx.core.widget.NestedScrollView>





</androidx.constraintlayout.widget.ConstraintLayout>