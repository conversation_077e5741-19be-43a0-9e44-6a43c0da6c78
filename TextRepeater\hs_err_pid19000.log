#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 15728640 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Unscaled Compressed Oops mode in which the Java heap is
#     placed in the first 4GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 4GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=19000, tid=16036
#
# JRE version: OpenJDK Runtime Environment (21.0.6) (build 21.0.6+-13368085-b895.109)
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109, mixed mode, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\agents\gradle-instrumentation-agent-8.13.jar org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.13

Host: AMD Ryzen 5 3400G with Radeon Vega Graphics    , 8 cores, 21G,  Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
Time: Thu Jun  5 04:54:49 2025 Bangladesh Standard Time elapsed time: 13.470791 seconds (0d 0h 0m 13s)

---------------  T H R E A D  ---------------

Current thread (0x0000015563042690):  VMThread "VM Thread"          [id=16036, stack(0x0000003fbf200000,0x0000003fbf300000) (1024K)]

Stack: [0x0000003fbf200000,0x0000003fbf300000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d0639]
V  [jvm.dll+0x85eb03]
V  [jvm.dll+0x86105e]
V  [jvm.dll+0x861743]
V  [jvm.dll+0x27e6e6]
V  [jvm.dll+0x6ccfd5]
V  [jvm.dll+0x6c0a8a]
V  [jvm.dll+0x35537b]
V  [jvm.dll+0x35cfd6]
V  [jvm.dll+0x3aef86]
V  [jvm.dll+0x3af258]
V  [jvm.dll+0x327a2c]
V  [jvm.dll+0x32a436]
V  [jvm.dll+0x33438d]
V  [jvm.dll+0x36ba15]
V  [jvm.dll+0x8674c8]
V  [jvm.dll+0x868844]
V  [jvm.dll+0x868d80]
V  [jvm.dll+0x869013]
V  [jvm.dll+0x806ed8]
V  [jvm.dll+0x6cef0d]
C  [ucrtbase.dll+0x21bb2]
C  [KERNEL32.DLL+0x17374]
C  [ntdll.dll+0x4cc91]

VM_Operation (0x0000003fbedffa30): G1PauseRemark, mode: safepoint, requested by thread 0x000001555bedc4e0


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x000001556a51fc30, length=93, elements={
0x0000015544d6e800, 0x0000015563223320, 0x00000155631cde80, 0x000001556322c240,
0x000001556322ed90, 0x000001556322f860, 0x0000015563253080, 0x00000155632598d0,
0x000001556325ec80, 0x000001556366b410, 0x000001555eadf000, 0x0000015565867540,
0x0000015565a40990, 0x00000155639a55c0, 0x0000015563b9b000, 0x0000015563b9e680,
0x0000015563b9b6d0, 0x0000015563b9bda0, 0x0000015563b9dfb0, 0x0000015563b9c470,
0x0000015563b9d210, 0x00000155660379d0, 0x0000015566039be0, 0x0000015566038e40,
0x0000015566038770, 0x0000015566034a20, 0x0000015566036560, 0x00000155660380a0,
0x0000015565a44700, 0x0000015565a41080, 0x0000015565a43960, 0x0000015565a44030,
0x0000015565a41750, 0x0000015565a41e20, 0x0000015565a424f0, 0x0000015565a42bc0,
0x0000015566036c30, 0x00000155660357c0, 0x00000155660335b0, 0x0000015563b9cb40,
0x000001556909a480, 0x000001556909cd60, 0x00000155690996e0, 0x000001556909ab50,
0x0000015566037300, 0x000001556660fdd0, 0x000001556660d4f0, 0x0000015566610b70,
0x000001556660ce20, 0x000001556660e290, 0x000001556660b2e0, 0x000001556660f700,
0x000001556660c750, 0x000001556660ac10, 0x000001556660b9b0, 0x0000015566611fe0,
0x000001556660c080, 0x000001556660dbc0, 0x000001556ab4faf0, 0x000001556ab501c0,
0x000001556ab52aa0, 0x000001556ab54cb0, 0x000001556ab4f420, 0x000001556ab50890,
0x000001556ab55380, 0x000001556ab53170, 0x000001556ab4dfb0, 0x000001556ab51630,
0x000001556ab51d00, 0x000001556ab53840, 0x000001556ab53f10, 0x000001556ab545e0,
0x000001556ab523d0, 0x00000155684bfcf0, 0x00000155684c3370, 0x00000155684c1f00,
0x00000155684bd410, 0x00000155684c1160, 0x00000155684c0a90, 0x00000155684bc670,
0x0000015569098940, 0x000001556d67bd80, 0x000001556b0ab4f0, 0x000001556b0addd0,
0x000001556b0a99b0, 0x000001556b0ac290, 0x000001556b0aa080, 0x000001556b0aa750,
0x000001556b0aeb70, 0x000001556b0a8540, 0x000001556b0ac960, 0x000001556b0a8c10,
0x00000155649a9700
}

Java Threads: ( => current thread )
  0x0000015544d6e800 JavaThread "main"                              [_thread_blocked, id=3436, stack(0x0000003fbeb00000,0x0000003fbec00000) (1024K)]
  0x0000015563223320 JavaThread "Reference Handler"          daemon [_thread_blocked, id=7108, stack(0x0000003fbf300000,0x0000003fbf400000) (1024K)]
  0x00000155631cde80 JavaThread "Finalizer"                  daemon [_thread_blocked, id=16448, stack(0x0000003fbf400000,0x0000003fbf500000) (1024K)]
  0x000001556322c240 JavaThread "Signal Dispatcher"          daemon [_thread_blocked, id=14552, stack(0x0000003fbf500000,0x0000003fbf600000) (1024K)]
  0x000001556322ed90 JavaThread "Attach Listener"            daemon [_thread_blocked, id=17092, stack(0x0000003fbf600000,0x0000003fbf700000) (1024K)]
  0x000001556322f860 JavaThread "Service Thread"             daemon [_thread_blocked, id=16276, stack(0x0000003fbf700000,0x0000003fbf800000) (1024K)]
  0x0000015563253080 JavaThread "Monitor Deflation Thread"   daemon [_thread_blocked, id=18564, stack(0x0000003fbf800000,0x0000003fbf900000) (1024K)]
  0x00000155632598d0 JavaThread "C2 CompilerThread0"         daemon [_thread_blocked, id=13432, stack(0x0000003fbf900000,0x0000003fbfa00000) (1024K)]
  0x000001556325ec80 JavaThread "C1 CompilerThread0"         daemon [_thread_blocked, id=8692, stack(0x0000003fbfa00000,0x0000003fbfb00000) (1024K)]
  0x000001556366b410 JavaThread "Common-Cleaner"             daemon [_thread_blocked, id=10536, stack(0x0000003fbfb00000,0x0000003fbfc00000) (1024K)]
  0x000001555eadf000 JavaThread "Notification Thread"        daemon [_thread_blocked, id=2244, stack(0x0000003fbfc00000,0x0000003fbfd00000) (1024K)]
  0x0000015565867540 JavaThread "Daemon health stats"               [_thread_blocked, id=7088, stack(0x0000003fbfe00000,0x0000003fbff00000) (1024K)]
  0x0000015565a40990 JavaThread "Incoming local TCP Connector on port 54848"        [_thread_in_native, id=18996, stack(0x0000003fc0600000,0x0000003fc0700000) (1024K)]
  0x00000155639a55c0 JavaThread "Daemon periodic checks"            [_thread_blocked, id=19164, stack(0x0000003fc0700000,0x0000003fc0800000) (1024K)]
  0x0000015563b9b000 JavaThread "Daemon"                            [_thread_blocked, id=18932, stack(0x0000003fc0800000,0x0000003fc0900000) (1024K)]
  0x0000015563b9e680 JavaThread "Handler for socket connection from /127.0.0.1:54848 to /127.0.0.1:54849"        [_thread_in_native, id=15912, stack(0x0000003fc0900000,0x0000003fc0a00000) (1024K)]
  0x0000015563b9b6d0 JavaThread "Cancel handler"                    [_thread_blocked, id=18332, stack(0x0000003fc0a00000,0x0000003fc0b00000) (1024K)]
  0x0000015563b9bda0 JavaThread "Daemon worker"                     [_thread_blocked, id=512, stack(0x0000003fc0b00000,0x0000003fc0c00000) (1024K)]
  0x0000015563b9dfb0 JavaThread "Asynchronous log dispatcher for DefaultDaemonConnection: socket connection from /127.0.0.1:54848 to /127.0.0.1:54849"        [_thread_blocked, id=17424, stack(0x0000003fc0c00000,0x0000003fc0d00000) (1024K)]
  0x0000015563b9c470 JavaThread "Stdin handler"                     [_thread_blocked, id=14740, stack(0x0000003fc0d00000,0x0000003fc0e00000) (1024K)]
  0x0000015563b9d210 JavaThread "Daemon client event forwarder"        [_thread_blocked, id=4640, stack(0x0000003fc0e00000,0x0000003fc0f00000) (1024K)]
  0x00000155660379d0 JavaThread "Cache worker for journal cache (C:\Users\<USER>\.gradle\caches\journal-1)"        [_thread_blocked, id=12232, stack(0x0000003fbfd00000,0x0000003fbfe00000) (1024K)]
  0x0000015566039be0 JavaThread "File lock request listener"        [_thread_in_native, id=16832, stack(0x0000003fc1000000,0x0000003fc1100000) (1024K)]
  0x0000015566038e40 JavaThread "Cache worker for file hash cache (C:\Users\<USER>\.gradle\caches\8.13\fileHashes)"        [_thread_blocked, id=18036, stack(0x0000003fc1200000,0x0000003fc1300000) (1024K)]
  0x0000015566038770 JavaThread "Cache worker for file hash cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\8.13\fileHashes)"        [_thread_blocked, id=18940, stack(0x0000003fc1400000,0x0000003fc1500000) (1024K)]
  0x0000015566034a20 JavaThread "Cache worker for Build Output Cleanup Cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\buildOutputCleanup)"        [_thread_blocked, id=2188, stack(0x0000003fc1500000,0x0000003fc1600000) (1024K)]
  0x0000015566036560 JavaThread "File watcher server"        daemon [_thread_in_native, id=18200, stack(0x0000003fc1600000,0x0000003fc1700000) (1024K)]
  0x00000155660380a0 JavaThread "File watcher consumer"      daemon [_thread_blocked, id=13828, stack(0x0000003fc1700000,0x0000003fc1800000) (1024K)]
  0x0000015565a44700 JavaThread "jar transforms"                    [_thread_blocked, id=10748, stack(0x0000003fc1800000,0x0000003fc1900000) (1024K)]
  0x0000015565a41080 JavaThread "jar transforms Thread 2"           [_thread_blocked, id=17452, stack(0x0000003fc1900000,0x0000003fc1a00000) (1024K)]
  0x0000015565a43960 JavaThread "jar transforms Thread 3"           [_thread_blocked, id=12504, stack(0x0000003fc1a00000,0x0000003fc1b00000) (1024K)]
  0x0000015565a44030 JavaThread "jar transforms Thread 4"           [_thread_blocked, id=14776, stack(0x0000003fc1b00000,0x0000003fc1c00000) (1024K)]
  0x0000015565a41750 JavaThread "jar transforms Thread 5"           [_thread_blocked, id=16884, stack(0x0000003fc1c00000,0x0000003fc1d00000) (1024K)]
  0x0000015565a41e20 JavaThread "jar transforms Thread 6"           [_thread_blocked, id=2540, stack(0x0000003fc1d00000,0x0000003fc1e00000) (1024K)]
  0x0000015565a424f0 JavaThread "jar transforms Thread 7"           [_thread_blocked, id=17564, stack(0x0000003fc1e00000,0x0000003fc1f00000) (1024K)]
  0x0000015565a42bc0 JavaThread "jar transforms Thread 8"           [_thread_blocked, id=10444, stack(0x0000003fc1f00000,0x0000003fc2000000) (1024K)]
  0x0000015566036c30 JavaThread "Cache worker for checksums cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\8.13\checksums)"        [_thread_blocked, id=1252, stack(0x0000003fc2000000,0x0000003fc2100000) (1024K)]
  0x00000155660357c0 JavaThread "Cache worker for file content cache (C:\Users\<USER>\.gradle\caches\8.13\fileContent)"        [_thread_blocked, id=19420, stack(0x0000003fc2100000,0x0000003fc2200000) (1024K)]
  0x00000155660335b0 JavaThread "Cache worker for cache directory md-supplier (C:\Users\<USER>\.gradle\caches\8.13\md-supplier)"        [_thread_blocked, id=17832, stack(0x0000003fc2200000,0x0000003fc2300000) (1024K)]
  0x0000015563b9cb40 JavaThread "Cache worker for cache directory md-rule (C:\Users\<USER>\.gradle\caches\8.13\md-rule)"        [_thread_blocked, id=16848, stack(0x0000003fc2300000,0x0000003fc2400000) (1024K)]
  0x000001556909a480 JavaThread "Unconstrained build operations"        [_thread_blocked, id=19156, stack(0x0000003fc1300000,0x0000003fc1400000) (1024K)]
  0x000001556909cd60 JavaThread "Unconstrained build operations Thread 2"        [_thread_blocked, id=19060, stack(0x0000003fc2400000,0x0000003fc2500000) (1024K)]
  0x00000155690996e0 JavaThread "Unconstrained build operations Thread 3"        [_thread_blocked, id=19064, stack(0x0000003fc2500000,0x0000003fc2600000) (1024K)]
  0x000001556909ab50 JavaThread "Unconstrained build operations Thread 4"        [_thread_blocked, id=6716, stack(0x0000003fc2600000,0x0000003fc2700000) (1024K)]
  0x0000015566037300 JavaThread "Unconstrained build operations Thread 5"        [_thread_blocked, id=16272, stack(0x0000003fc2700000,0x0000003fc2800000) (1024K)]
  0x000001556660fdd0 JavaThread "Unconstrained build operations Thread 6"        [_thread_blocked, id=11680, stack(0x0000003fc2800000,0x0000003fc2900000) (1024K)]
  0x000001556660d4f0 JavaThread "Unconstrained build operations Thread 7"        [_thread_blocked, id=19244, stack(0x0000003fc2900000,0x0000003fc2a00000) (1024K)]
  0x0000015566610b70 JavaThread "Unconstrained build operations Thread 8"        [_thread_blocked, id=13072, stack(0x0000003fc2a00000,0x0000003fc2b00000) (1024K)]
  0x000001556660ce20 JavaThread "Unconstrained build operations Thread 9"        [_thread_blocked, id=17328, stack(0x0000003fc2b00000,0x0000003fc2c00000) (1024K)]
  0x000001556660e290 JavaThread "Unconstrained build operations Thread 10"        [_thread_blocked, id=1876, stack(0x0000003fc2c00000,0x0000003fc2d00000) (1024K)]
  0x000001556660b2e0 JavaThread "Unconstrained build operations Thread 11"        [_thread_blocked, id=6988, stack(0x0000003fc2d00000,0x0000003fc2e00000) (1024K)]
  0x000001556660f700 JavaThread "Unconstrained build operations Thread 12"        [_thread_blocked, id=18528, stack(0x0000003fc2e00000,0x0000003fc2f00000) (1024K)]
  0x000001556660c750 JavaThread "Unconstrained build operations Thread 13"        [_thread_blocked, id=2272, stack(0x0000003fc2f00000,0x0000003fc3000000) (1024K)]
  0x000001556660ac10 JavaThread "Unconstrained build operations Thread 14"        [_thread_blocked, id=1492, stack(0x0000003fc3000000,0x0000003fc3100000) (1024K)]
  0x000001556660b9b0 JavaThread "Unconstrained build operations Thread 15"        [_thread_blocked, id=14280, stack(0x0000003fc3100000,0x0000003fc3200000) (1024K)]
  0x0000015566611fe0 JavaThread "Unconstrained build operations Thread 16"        [_thread_blocked, id=2792, stack(0x0000003fc3200000,0x0000003fc3300000) (1024K)]
  0x000001556660c080 JavaThread "Unconstrained build operations Thread 17"        [_thread_blocked, id=19092, stack(0x0000003fc3300000,0x0000003fc3400000) (1024K)]
  0x000001556660dbc0 JavaThread "Unconstrained build operations Thread 18"        [_thread_blocked, id=18592, stack(0x0000003fc3400000,0x0000003fc3500000) (1024K)]
  0x000001556ab4faf0 JavaThread "Unconstrained build operations Thread 19"        [_thread_blocked, id=6756, stack(0x0000003fc3600000,0x0000003fc3700000) (1024K)]
  0x000001556ab501c0 JavaThread "Unconstrained build operations Thread 20"        [_thread_blocked, id=17500, stack(0x0000003fc3700000,0x0000003fc3800000) (1024K)]
  0x000001556ab52aa0 JavaThread "Unconstrained build operations Thread 21"        [_thread_blocked, id=19452, stack(0x0000003fc3800000,0x0000003fc3900000) (1024K)]
  0x000001556ab54cb0 JavaThread "Unconstrained build operations Thread 22"        [_thread_blocked, id=18764, stack(0x0000003fc3900000,0x0000003fc3a00000) (1024K)]
  0x000001556ab4f420 JavaThread "Unconstrained build operations Thread 23"        [_thread_blocked, id=18788, stack(0x0000003fc3a00000,0x0000003fc3b00000) (1024K)]
  0x000001556ab50890 JavaThread "Unconstrained build operations Thread 24"        [_thread_blocked, id=7560, stack(0x0000003fc3b00000,0x0000003fc3c00000) (1024K)]
  0x000001556ab55380 JavaThread "Unconstrained build operations Thread 25"        [_thread_blocked, id=10488, stack(0x0000003fc3c00000,0x0000003fc3d00000) (1024K)]
  0x000001556ab53170 JavaThread "Unconstrained build operations Thread 26"        [_thread_blocked, id=10180, stack(0x0000003fc3d00000,0x0000003fc3e00000) (1024K)]
  0x000001556ab4dfb0 JavaThread "Unconstrained build operations Thread 27"        [_thread_blocked, id=18600, stack(0x0000003fc3e00000,0x0000003fc3f00000) (1024K)]
  0x000001556ab51630 JavaThread "Unconstrained build operations Thread 28"        [_thread_blocked, id=17584, stack(0x0000003fc3f00000,0x0000003fc4000000) (1024K)]
  0x000001556ab51d00 JavaThread "Unconstrained build operations Thread 29"        [_thread_blocked, id=19036, stack(0x0000003fc4000000,0x0000003fc4100000) (1024K)]
  0x000001556ab53840 JavaThread "Unconstrained build operations Thread 30"        [_thread_blocked, id=3480, stack(0x0000003fc4100000,0x0000003fc4200000) (1024K)]
  0x000001556ab53f10 JavaThread "Unconstrained build operations Thread 31"        [_thread_blocked, id=18828, stack(0x0000003fc4200000,0x0000003fc4300000) (1024K)]
  0x000001556ab545e0 JavaThread "Unconstrained build operations Thread 32"        [_thread_blocked, id=12852, stack(0x0000003fc4300000,0x0000003fc4400000) (1024K)]
  0x000001556ab523d0 JavaThread "Unconstrained build operations Thread 33"        [_thread_blocked, id=16648, stack(0x0000003fc4400000,0x0000003fc4500000) (1024K)]
  0x00000155684bfcf0 JavaThread "Unconstrained build operations Thread 34"        [_thread_blocked, id=10428, stack(0x0000003fc4500000,0x0000003fc4600000) (1024K)]
  0x00000155684c3370 JavaThread "Unconstrained build operations Thread 35"        [_thread_blocked, id=5812, stack(0x0000003fc4600000,0x0000003fc4700000) (1024K)]
  0x00000155684c1f00 JavaThread "Unconstrained build operations Thread 36"        [_thread_blocked, id=16288, stack(0x0000003fc4700000,0x0000003fc4800000) (1024K)]
  0x00000155684bd410 JavaThread "Unconstrained build operations Thread 37"        [_thread_blocked, id=9288, stack(0x0000003fc4800000,0x0000003fc4900000) (1024K)]
  0x00000155684c1160 JavaThread "Unconstrained build operations Thread 38"        [_thread_blocked, id=11440, stack(0x0000003fc4900000,0x0000003fc4a00000) (1024K)]
  0x00000155684c0a90 JavaThread "Unconstrained build operations Thread 39"        [_thread_blocked, id=2312, stack(0x0000003fc4a00000,0x0000003fc4b00000) (1024K)]
  0x00000155684bc670 JavaThread "Memory manager"                    [_thread_blocked, id=1760, stack(0x0000003fc3500000,0x0000003fc3600000) (1024K)]
  0x0000015569098940 JavaThread "Problems report writer"            [_thread_blocked, id=4312, stack(0x0000003fc4c00000,0x0000003fc4d00000) (1024K)]
  0x000001556d67bd80 JavaThread "pool-3-thread-1"                   [_thread_blocked, id=15732, stack(0x0000003fc4d00000,0x0000003fc4e00000) (1024K)]
  0x000001556b0ab4f0 JavaThread "included builds"                   [_thread_blocked, id=11356, stack(0x0000003fc1100000,0x0000003fc1200000) (1024K)]
  0x000001556b0addd0 JavaThread "Execution worker"                  [_thread_blocked, id=2916, stack(0x0000003fc4b00000,0x0000003fc4c00000) (1024K)]
  0x000001556b0a99b0 JavaThread "Execution worker Thread 2"         [_thread_blocked, id=5792, stack(0x0000003fc4e00000,0x0000003fc4f00000) (1024K)]
  0x000001556b0ac290 JavaThread "Execution worker Thread 3"         [_thread_blocked, id=19016, stack(0x0000003fc4f00000,0x0000003fc5000000) (1024K)]
  0x000001556b0aa080 JavaThread "Execution worker Thread 4"         [_thread_blocked, id=1700, stack(0x0000003fc5000000,0x0000003fc5100000) (1024K)]
  0x000001556b0aa750 JavaThread "Execution worker Thread 5"         [_thread_blocked, id=16644, stack(0x0000003fc5100000,0x0000003fc5200000) (1024K)]
  0x000001556b0aeb70 JavaThread "Execution worker Thread 6"         [_thread_blocked, id=2328, stack(0x0000003fc5200000,0x0000003fc5300000) (1024K)]
  0x000001556b0a8540 JavaThread "Execution worker Thread 7"         [_thread_blocked, id=14856, stack(0x0000003fc5300000,0x0000003fc5400000) (1024K)]
  0x000001556b0ac960 JavaThread "Cache worker for execution history cache (C:\xampp\htdocs\monirulvitextrepeater\TextRepeater\.gradle\8.13\executionHistory)"        [_thread_blocked, id=17036, stack(0x0000003fc5400000,0x0000003fc5500000) (1024K)]
  0x000001556b0a8c10 JavaThread "idea-tooling-model-converter"        [_thread_blocked, id=4608, stack(0x0000003fc5500000,0x0000003fc5600000) (1024K)]
  0x00000155649a9700 JavaThread "C2 CompilerThread1"         daemon [_thread_blocked, id=14532, stack(0x0000003fc5600000,0x0000003fc5700000) (1024K)]
Total: 93

Other Threads:
=>0x0000015563042690 VMThread "VM Thread"                           [id=16036, stack(0x0000003fbf200000,0x0000003fbf300000) (1024K)]
  0x000001555eaa5d70 WatcherThread "VM Periodic Task Thread"        [id=15908, stack(0x0000003fbf100000,0x0000003fbf200000) (1024K)]
  0x00000155472de1a0 WorkerThread "GC Thread#0"                     [id=17808, stack(0x0000003fbec00000,0x0000003fbed00000) (1024K)]
  0x00000155633a1b30 WorkerThread "GC Thread#1"                     [id=1608, stack(0x0000003fbff00000,0x0000003fc0000000) (1024K)]
  0x000001556427c570 WorkerThread "GC Thread#2"                     [id=7404, stack(0x0000003fc0000000,0x0000003fc0100000) (1024K)]
  0x00000155642054d0 WorkerThread "GC Thread#3"                     [id=18256, stack(0x0000003fc0100000,0x0000003fc0200000) (1024K)]
  0x00000155642058b0 WorkerThread "GC Thread#4"                     [id=2192, stack(0x0000003fc0200000,0x0000003fc0300000) (1024K)]
  0x000001556309e4f0 WorkerThread "GC Thread#5"                     [id=17088, stack(0x0000003fc0300000,0x0000003fc0400000) (1024K)]
  0x000001556309e8d0 WorkerThread "GC Thread#6"                     [id=7612, stack(0x0000003fc0400000,0x0000003fc0500000) (1024K)]
  0x000001556309ecb0 WorkerThread "GC Thread#7"                     [id=5844, stack(0x0000003fc0500000,0x0000003fc0600000) (1024K)]
  0x000001555bedc4e0 ConcurrentGCThread "G1 Main Marker"            [id=10192, stack(0x0000003fbed00000,0x0000003fbee00000) (1024K)]
  0x000001555bedd670 WorkerThread "G1 Conc#0"                       [id=15848, stack(0x0000003fbee00000,0x0000003fbef00000) (1024K)]
  0x0000015564da5df0 WorkerThread "G1 Conc#1"                       [id=5832, stack(0x0000003fc0f00000,0x0000003fc1000000) (1024K)]
  0x000001555e96a0c0 ConcurrentGCThread "G1 Refine#0"               [id=14616, stack(0x0000003fbef00000,0x0000003fbf000000) (1024K)]
  0x000001555e96aaf0 ConcurrentGCThread "G1 Service"                [id=8956, stack(0x0000003fbf000000,0x0000003fbf100000) (1024K)]
Total: 15

Threads with active compile tasks:
C2 CompilerThread0  13502 11338   !   4       org.gradle.internal.classloader.FilteringClassLoader::loadClass (62 bytes)
C2 CompilerThread1  13502 11354   !   4       java.net.URLClassLoader::findClass (47 bytes)
Total: 2

VM state: at safepoint (normal execution)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ff9b542e3a0] Threads_lock - owner thread: 0x0000015563042690
[0x00007ff9b542e4a0] Heap_lock - owner thread: 0x000001555bedc4e0

Heap address: 0x0000000080000000, size: 2048 MB, Compressed Oops mode: 32-bit

CDS archive(s) not mapped
Compressed class space mapped at: 0x0000000100000000-0x0000000140000000, reserved size: 1073741824
Narrow klass base: 0x0000000000000000, Narrow klass shift: 3, Narrow klass range: 0x140000000

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 5 size 8 Array Of Cards #cards 12 size 40 Howl #buckets 4 coarsen threshold 1843 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 2048
 CPUs: 8 total, 8 available
 Memory: 22476M
 Large Page Support: Disabled
 NUMA Support: Disabled
 Compressed Oops: Enabled (32-bit)
 Heap Region Size: 1M
 Heap Min Capacity: 8M
 Heap Initial Capacity: 352M
 Heap Max Capacity: 2G
 Pre-touch: Disabled
 Parallel Workers: 8
 Concurrent Workers: 2
 Concurrent Refinement Workers: 8
 Periodic GC: Disabled

Heap:
 garbage-first heap   total 242688K, used 156604K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 7 young (7168K), 3 survivors (3072K)
 Metaspace       used 106903K, committed 109184K, reserved 1179648K
  class space    used 14093K, committed 15232K, reserved 1048576K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom
|   0|0x0000000080000000, 0x0000000080100000, 0x0000000080100000|100%|HS|  |TAMS 0x0000000080100000| PB 0x0000000080000000| Complete 
|   1|0x0000000080100000, 0x0000000080200000, 0x0000000080200000|100%|HC|  |TAMS 0x0000000080200000| PB 0x0000000080100000| Complete 
|   2|0x0000000080200000, 0x0000000080300000, 0x0000000080300000|100%|HC|  |TAMS 0x0000000080300000| PB 0x0000000080200000| Complete 
|   3|0x0000000080300000, 0x0000000080400000, 0x0000000080400000|100%| O|  |TAMS 0x0000000080400000| PB 0x0000000080400000| Untracked 
|   4|0x0000000080400000, 0x0000000080500000, 0x0000000080500000|100%| O|  |TAMS 0x0000000080500000| PB 0x0000000080500000| Untracked 
|   5|0x0000000080500000, 0x0000000080600000, 0x0000000080600000|100%| O|  |TAMS 0x0000000080600000| PB 0x0000000080600000| Untracked 
|   6|0x0000000080600000, 0x0000000080700000, 0x0000000080700000|100%|HS|  |TAMS 0x0000000080700000| PB 0x0000000080600000| Complete 
|   7|0x0000000080700000, 0x0000000080800000, 0x0000000080800000|100%| O|  |TAMS 0x0000000080800000| PB 0x0000000080800000| Untracked 
|   8|0x0000000080800000, 0x0000000080900000, 0x0000000080900000|100%| O|  |TAMS 0x0000000080900000| PB 0x0000000080900000| Untracked 
|   9|0x0000000080900000, 0x0000000080a00000, 0x0000000080a00000|100%| O|  |TAMS 0x0000000080a00000| PB 0x0000000080a00000| Untracked 
|  10|0x0000000080a00000, 0x0000000080b00000, 0x0000000080b00000|100%| O|  |TAMS 0x0000000080b00000| PB 0x0000000080b00000| Untracked 
|  11|0x0000000080b00000, 0x0000000080c00000, 0x0000000080c00000|100%| O|  |TAMS 0x0000000080c00000| PB 0x0000000080c00000| Untracked 
|  12|0x0000000080c00000, 0x0000000080d00000, 0x0000000080d00000|100%| O|  |TAMS 0x0000000080d00000| PB 0x0000000080d00000| Untracked 
|  13|0x0000000080d00000, 0x0000000080e00000, 0x0000000080e00000|100%| O|  |TAMS 0x0000000080e00000| PB 0x0000000080e00000| Untracked 
|  14|0x0000000080e00000, 0x0000000080f00000, 0x0000000080f00000|100%| O|  |TAMS 0x0000000080f00000| PB 0x0000000080f00000| Untracked 
|  15|0x0000000080f00000, 0x0000000081000000, 0x0000000081000000|100%| O|  |TAMS 0x0000000081000000| PB 0x0000000081000000| Untracked 
|  16|0x0000000081000000, 0x0000000081100000, 0x0000000081100000|100%| O|  |TAMS 0x0000000081100000| PB 0x0000000081100000| Untracked 
|  17|0x0000000081100000, 0x0000000081200000, 0x0000000081200000|100%| O|  |TAMS 0x0000000081200000| PB 0x0000000081200000| Untracked 
|  18|0x0000000081200000, 0x0000000081300000, 0x0000000081300000|100%| O|  |TAMS 0x0000000081300000| PB 0x0000000081300000| Untracked 
|  19|0x0000000081300000, 0x0000000081400000, 0x0000000081400000|100%| O|  |TAMS 0x0000000081400000| PB 0x0000000081400000| Untracked 
|  20|0x0000000081400000, 0x0000000081500000, 0x0000000081500000|100%| O|  |TAMS 0x0000000081500000| PB 0x0000000081500000| Updating 
|  21|0x0000000081500000, 0x0000000081600000, 0x0000000081600000|100%| O|  |TAMS 0x0000000081600000| PB 0x0000000081600000| Updating 
|  22|0x0000000081600000, 0x0000000081700000, 0x0000000081700000|100%|HS|  |TAMS 0x0000000081700000| PB 0x0000000081600000| Complete 
|  23|0x0000000081700000, 0x0000000081800000, 0x0000000081800000|100%|HC|  |TAMS 0x0000000081800000| PB 0x0000000081700000| Complete 
|  24|0x0000000081800000, 0x0000000081900000, 0x0000000081900000|100%|HC|  |TAMS 0x0000000081900000| PB 0x0000000081800000| Complete 
|  25|0x0000000081900000, 0x0000000081a00000, 0x0000000081a00000|100%|HC|  |TAMS 0x0000000081a00000| PB 0x0000000081900000| Complete 
|  26|0x0000000081a00000, 0x0000000081b00000, 0x0000000081b00000|100%|HC|  |TAMS 0x0000000081b00000| PB 0x0000000081a00000| Complete 
|  27|0x0000000081b00000, 0x0000000081c00000, 0x0000000081c00000|100%|HC|  |TAMS 0x0000000081c00000| PB 0x0000000081b00000| Complete 
|  28|0x0000000081c00000, 0x0000000081d00000, 0x0000000081d00000|100%|HS|  |TAMS 0x0000000081d00000| PB 0x0000000081c00000| Complete 
|  29|0x0000000081d00000, 0x0000000081e00000, 0x0000000081e00000|100%|HC|  |TAMS 0x0000000081e00000| PB 0x0000000081d00000| Complete 
|  30|0x0000000081e00000, 0x0000000081f00000, 0x0000000081f00000|100%|HC|  |TAMS 0x0000000081f00000| PB 0x0000000081e00000| Complete 
|  31|0x0000000081f00000, 0x0000000082000000, 0x0000000082000000|100%|HC|  |TAMS 0x0000000082000000| PB 0x0000000081f00000| Complete 
|  32|0x0000000082000000, 0x0000000082100000, 0x0000000082100000|100%|HS|  |TAMS 0x0000000082100000| PB 0x0000000082000000| Complete 
|  33|0x0000000082100000, 0x0000000082200000, 0x0000000082200000|100%|HC|  |TAMS 0x0000000082200000| PB 0x0000000082100000| Complete 
|  34|0x0000000082200000, 0x0000000082300000, 0x0000000082300000|100%|HS|  |TAMS 0x0000000082300000| PB 0x0000000082200000| Complete 
|  35|0x0000000082300000, 0x0000000082400000, 0x0000000082400000|100%| O|  |TAMS 0x0000000082400000| PB 0x0000000082400000| Untracked 
|  36|0x0000000082400000, 0x0000000082500000, 0x0000000082500000|100%|HS|  |TAMS 0x0000000082500000| PB 0x0000000082400000| Complete 
|  37|0x0000000082500000, 0x0000000082600000, 0x0000000082600000|100%| O|  |TAMS 0x0000000082600000| PB 0x0000000082600000| Untracked 
|  38|0x0000000082600000, 0x0000000082700000, 0x0000000082700000|100%| O|  |TAMS 0x0000000082700000| PB 0x0000000082700000| Updating 
|  39|0x0000000082700000, 0x0000000082800000, 0x0000000082800000|100%| O|  |TAMS 0x0000000082800000| PB 0x0000000082800000| Updating 
|  40|0x0000000082800000, 0x0000000082900000, 0x0000000082900000|100%| O|  |TAMS 0x0000000082900000| PB 0x0000000082900000| Untracked 
|  41|0x0000000082900000, 0x0000000082a00000, 0x0000000082a00000|100%| O|  |TAMS 0x0000000082a00000| PB 0x0000000082a00000| Updating 
|  42|0x0000000082a00000, 0x0000000082b00000, 0x0000000082b00000|100%| O|  |TAMS 0x0000000082b00000| PB 0x0000000082b00000| Untracked 
|  43|0x0000000082b00000, 0x0000000082c00000, 0x0000000082c00000|100%| O|  |TAMS 0x0000000082c00000| PB 0x0000000082c00000| Untracked 
|  44|0x0000000082c00000, 0x0000000082d00000, 0x0000000082d00000|100%| O|  |TAMS 0x0000000082d00000| PB 0x0000000082d00000| Updating 
|  45|0x0000000082d00000, 0x0000000082e00000, 0x0000000082e00000|100%| O|  |TAMS 0x0000000082e00000| PB 0x0000000082e00000| Untracked 
|  46|0x0000000082e00000, 0x0000000082f00000, 0x0000000082f00000|100%|HS|  |TAMS 0x0000000082f00000| PB 0x0000000082e00000| Complete 
|  47|0x0000000082f00000, 0x0000000083000000, 0x0000000083000000|100%|HC|  |TAMS 0x0000000083000000| PB 0x0000000082f00000| Complete 
|  48|0x0000000083000000, 0x0000000083100000, 0x0000000083100000|100%| O|  |TAMS 0x0000000083000000| PB 0x0000000083000000| Untracked 
|  49|0x0000000083100000, 0x0000000083200000, 0x0000000083200000|100%| O|  |TAMS 0x0000000083200000| PB 0x0000000083200000| Untracked 
|  50|0x0000000083200000, 0x0000000083300000, 0x0000000083300000|100%| O|  |TAMS 0x0000000083300000| PB 0x0000000083300000| Untracked 
|  51|0x0000000083300000, 0x0000000083400000, 0x0000000083400000|100%| O|  |TAMS 0x0000000083400000| PB 0x0000000083400000| Untracked 
|  52|0x0000000083400000, 0x0000000083500000, 0x0000000083500000|100%| O|  |TAMS 0x0000000083500000| PB 0x0000000083500000| Updating 
|  53|0x0000000083500000, 0x0000000083600000, 0x0000000083600000|100%|HS|  |TAMS 0x0000000083600000| PB 0x0000000083500000| Complete 
|  54|0x0000000083600000, 0x0000000083700000, 0x0000000083700000|100%|HC|  |TAMS 0x0000000083700000| PB 0x0000000083600000| Complete 
|  55|0x0000000083700000, 0x0000000083800000, 0x0000000083800000|100%|HC|  |TAMS 0x0000000083800000| PB 0x0000000083700000| Complete 
|  56|0x0000000083800000, 0x0000000083900000, 0x0000000083900000|100%|HC|  |TAMS 0x0000000083900000| PB 0x0000000083800000| Complete 
|  57|0x0000000083900000, 0x0000000083a00000, 0x0000000083a00000|100%|HC|  |TAMS 0x0000000083a00000| PB 0x0000000083900000| Complete 
|  58|0x0000000083a00000, 0x0000000083b00000, 0x0000000083b00000|100%|HC|  |TAMS 0x0000000083b00000| PB 0x0000000083a00000| Complete 
|  59|0x0000000083b00000, 0x0000000083c00000, 0x0000000083c00000|100%|HS|  |TAMS 0x0000000083c00000| PB 0x0000000083b00000| Complete 
|  60|0x0000000083c00000, 0x0000000083d00000, 0x0000000083d00000|100%|HC|  |TAMS 0x0000000083d00000| PB 0x0000000083c00000| Complete 
|  61|0x0000000083d00000, 0x0000000083e00000, 0x0000000083e00000|100%|HC|  |TAMS 0x0000000083e00000| PB 0x0000000083d00000| Complete 
|  62|0x0000000083e00000, 0x0000000083f00000, 0x0000000083f00000|100%|HC|  |TAMS 0x0000000083f00000| PB 0x0000000083e00000| Complete 
|  63|0x0000000083f00000, 0x0000000084000000, 0x0000000084000000|100%|HC|  |TAMS 0x0000000084000000| PB 0x0000000083f00000| Complete 
|  64|0x0000000084000000, 0x0000000084100000, 0x0000000084100000|100%|HC|  |TAMS 0x0000000084100000| PB 0x0000000084000000| Complete 
|  65|0x0000000084100000, 0x0000000084200000, 0x0000000084200000|100%| O|  |TAMS 0x0000000084200000| PB 0x0000000084200000| Untracked 
|  66|0x0000000084200000, 0x0000000084300000, 0x0000000084300000|100%| O|  |TAMS 0x0000000084300000| PB 0x0000000084300000| Untracked 
|  67|0x0000000084300000, 0x0000000084400000, 0x0000000084400000|100%| O|  |TAMS 0x0000000084400000| PB 0x0000000084400000| Untracked 
|  68|0x0000000084400000, 0x0000000084500000, 0x0000000084500000|100%| O|  |TAMS 0x0000000084500000| PB 0x0000000084500000| Untracked 
|  69|0x0000000084500000, 0x0000000084600000, 0x0000000084600000|100%| O|  |TAMS 0x0000000084600000| PB 0x0000000084600000| Updating 
|  70|0x0000000084600000, 0x0000000084700000, 0x0000000084700000|100%| O|  |TAMS 0x0000000084700000| PB 0x0000000084700000| Updating 
|  71|0x0000000084700000, 0x0000000084800000, 0x0000000084800000|100%|HS|  |TAMS 0x0000000084800000| PB 0x0000000084700000| Complete 
|  72|0x0000000084800000, 0x0000000084900000, 0x0000000084900000|100%|HS|  |TAMS 0x0000000084900000| PB 0x0000000084800000| Complete 
|  73|0x0000000084900000, 0x0000000084a00000, 0x0000000084a00000|100%|HS|  |TAMS 0x0000000084a00000| PB 0x0000000084900000| Complete 
|  74|0x0000000084a00000, 0x0000000084b00000, 0x0000000084b00000|100%|HS|  |TAMS 0x0000000084b00000| PB 0x0000000084a00000| Complete 
|  75|0x0000000084b00000, 0x0000000084c00000, 0x0000000084c00000|100%|HS|  |TAMS 0x0000000084c00000| PB 0x0000000084b00000| Complete 
|  76|0x0000000084c00000, 0x0000000084d00000, 0x0000000084d00000|100%|HS|  |TAMS 0x0000000084d00000| PB 0x0000000084c00000| Complete 
|  77|0x0000000084d00000, 0x0000000084e00000, 0x0000000084e00000|100%| O|  |TAMS 0x0000000084e00000| PB 0x0000000084e00000| Untracked 
|  78|0x0000000084e00000, 0x0000000084f00000, 0x0000000084f00000|100%| O|  |TAMS 0x0000000084f00000| PB 0x0000000084f00000| Untracked 
|  79|0x0000000084f00000, 0x0000000085000000, 0x0000000085000000|100%| O|  |TAMS 0x0000000085000000| PB 0x0000000085000000| Untracked 
|  80|0x0000000085000000, 0x0000000085100000, 0x0000000085100000|100%| O|  |TAMS 0x0000000085100000| PB 0x0000000085100000| Untracked 
|  81|0x0000000085100000, 0x0000000085200000, 0x0000000085200000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Untracked 
|  82|0x0000000085200000, 0x0000000085300000, 0x0000000085300000|100%| O|  |TAMS 0x0000000085200000| PB 0x0000000085200000| Untracked 
|  83|0x0000000085300000, 0x0000000085400000, 0x0000000085400000|100%| O|  |TAMS 0x0000000085300000| PB 0x0000000085300000| Untracked 
|  84|0x0000000085400000, 0x0000000085500000, 0x0000000085500000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Untracked 
|  85|0x0000000085500000, 0x0000000085600000, 0x0000000085600000|100%| O|  |TAMS 0x0000000085500000| PB 0x0000000085500000| Untracked 
|  86|0x0000000085600000, 0x0000000085700000, 0x0000000085700000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Updating 
|  87|0x0000000085700000, 0x0000000085800000, 0x0000000085800000|100%| O|  |TAMS 0x0000000085700000| PB 0x0000000085700000| Untracked 
|  88|0x0000000085800000, 0x0000000085900000, 0x0000000085900000|100%| O|  |TAMS 0x0000000085900000| PB 0x0000000085900000| Untracked 
|  89|0x0000000085900000, 0x0000000085a00000, 0x0000000085a00000|100%| O|  |TAMS 0x0000000085a00000| PB 0x0000000085a00000| Untracked 
|  90|0x0000000085a00000, 0x0000000085b00000, 0x0000000085b00000|100%| O|  |TAMS 0x0000000085b00000| PB 0x0000000085b00000| Untracked 
|  91|0x0000000085b00000, 0x0000000085c00000, 0x0000000085c00000|100%| O|  |TAMS 0x0000000085c00000| PB 0x0000000085c00000| Updating 
|  92|0x0000000085c00000, 0x0000000085d00000, 0x0000000085d00000|100%| O|  |TAMS 0x0000000085d00000| PB 0x0000000085d00000| Untracked 
|  93|0x0000000085d00000, 0x0000000085e00000, 0x0000000085e00000|100%| O|  |TAMS 0x0000000085e00000| PB 0x0000000085e00000| Updating 
|  94|0x0000000085e00000, 0x0000000085f00000, 0x0000000085f00000|100%| O|  |TAMS 0x0000000085f00000| PB 0x0000000085f00000| Updating 
|  95|0x0000000085f00000, 0x0000000086000000, 0x0000000086000000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Updating 
|  96|0x0000000086000000, 0x0000000086100000, 0x0000000086100000|100%| O|  |TAMS 0x0000000086000000| PB 0x0000000086000000| Untracked 
|  97|0x0000000086100000, 0x0000000086200000, 0x0000000086200000|100%| O|  |TAMS 0x0000000086200000| PB 0x0000000086200000| Untracked 
|  98|0x0000000086200000, 0x0000000086300000, 0x0000000086300000|100%| O|  |TAMS 0x0000000086300000| PB 0x0000000086300000| Untracked 
|  99|0x0000000086300000, 0x0000000086400000, 0x0000000086400000|100%| O|  |TAMS 0x0000000086400000| PB 0x0000000086400000| Untracked 
| 100|0x0000000086400000, 0x0000000086500000, 0x0000000086500000|100%| O|  |TAMS 0x0000000086500000| PB 0x0000000086500000| Untracked 
| 101|0x0000000086500000, 0x0000000086600000, 0x0000000086600000|100%|HS|  |TAMS 0x0000000086600000| PB 0x0000000086500000| Complete 
| 102|0x0000000086600000, 0x0000000086700000, 0x0000000086700000|100%| O|  |TAMS 0x0000000086700000| PB 0x0000000086700000| Untracked 
| 103|0x0000000086700000, 0x0000000086800000, 0x0000000086800000|100%| O|  |TAMS 0x0000000086800000| PB 0x0000000086800000| Untracked 
| 104|0x0000000086800000, 0x0000000086900000, 0x0000000086900000|100%| O|  |TAMS 0x0000000086900000| PB 0x0000000086900000| Untracked 
| 105|0x0000000086900000, 0x0000000086a00000, 0x0000000086a00000|100%| O|  |TAMS 0x0000000086a00000| PB 0x0000000086a00000| Untracked 
| 106|0x0000000086a00000, 0x0000000086b00000, 0x0000000086b00000|100%| O|  |TAMS 0x0000000086b00000| PB 0x0000000086b00000| Updating 
| 107|0x0000000086b00000, 0x0000000086c00000, 0x0000000086c00000|100%| O|  |TAMS 0x0000000086c00000| PB 0x0000000086c00000| Untracked 
| 108|0x0000000086c00000, 0x0000000086d00000, 0x0000000086d00000|100%| O|  |TAMS 0x0000000086d00000| PB 0x0000000086d00000| Untracked 
| 109|0x0000000086d00000, 0x0000000086e00000, 0x0000000086e00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Updating 
| 110|0x0000000086e00000, 0x0000000086f00000, 0x0000000086f00000|100%| O|  |TAMS 0x0000000086e00000| PB 0x0000000086e00000| Untracked 
| 111|0x0000000086f00000, 0x0000000087000000, 0x0000000087000000|100%| O|  |TAMS 0x0000000086f00000| PB 0x0000000086f00000| Untracked 
| 112|0x0000000087000000, 0x0000000087100000, 0x0000000087100000|100%|HS|  |TAMS 0x0000000087100000| PB 0x0000000087000000| Complete 
| 113|0x0000000087100000, 0x0000000087200000, 0x0000000087200000|100%|HC|  |TAMS 0x0000000087200000| PB 0x0000000087100000| Complete 
| 114|0x0000000087200000, 0x0000000087300000, 0x0000000087300000|100%|HS|  |TAMS 0x0000000087300000| PB 0x0000000087200000| Complete 
| 115|0x0000000087300000, 0x0000000087400000, 0x0000000087400000|100%|HC|  |TAMS 0x0000000087400000| PB 0x0000000087300000| Complete 
| 116|0x0000000087400000, 0x0000000087500000, 0x0000000087500000|100%| O|  |TAMS 0x0000000087400000| PB 0x0000000087400000| Untracked 
| 117|0x0000000087500000, 0x0000000087600000, 0x0000000087600000|100%| O|  |TAMS 0x0000000087500000| PB 0x0000000087500000| Untracked 
| 118|0x0000000087600000, 0x0000000087700000, 0x0000000087700000|100%| O|  |TAMS 0x0000000087600000| PB 0x0000000087600000| Untracked 
| 119|0x0000000087700000, 0x0000000087800000, 0x0000000087800000|100%| O|  |TAMS 0x0000000087700000| PB 0x0000000087700000| Untracked 
| 120|0x0000000087800000, 0x000000008782d798, 0x0000000087900000| 17%| O|  |TAMS 0x0000000087800000| PB 0x0000000087800000| Updating 
| 121|0x0000000087900000, 0x0000000087a00000, 0x0000000087a00000|100%|HS|  |TAMS 0x0000000087900000| PB 0x0000000087900000| Complete 
| 122|0x0000000087a00000, 0x0000000087b00000, 0x0000000087b00000|100%|HS|  |TAMS 0x0000000087a00000| PB 0x0000000087a00000| Complete 
| 123|0x0000000087b00000, 0x0000000087b00000, 0x0000000087c00000|  0%| F|  |TAMS 0x0000000087b00000| PB 0x0000000087b00000| Untracked 
| 124|0x0000000087c00000, 0x0000000087c00000, 0x0000000087d00000|  0%| F|  |TAMS 0x0000000087c00000| PB 0x0000000087c00000| Untracked 
| 125|0x0000000087d00000, 0x0000000087d00000, 0x0000000087e00000|  0%| F|  |TAMS 0x0000000087d00000| PB 0x0000000087d00000| Untracked 
| 126|0x0000000087e00000, 0x0000000087f00000, 0x0000000087f00000|100%|HS|  |TAMS 0x0000000087f00000| PB 0x0000000087e00000| Complete 
| 127|0x0000000087f00000, 0x0000000088000000, 0x0000000088000000|100%|HC|  |TAMS 0x0000000088000000| PB 0x0000000087f00000| Complete 
| 128|0x0000000088000000, 0x0000000088100000, 0x0000000088100000|100%|HC|  |TAMS 0x0000000088100000| PB 0x0000000088000000| Complete 
| 129|0x0000000088100000, 0x0000000088200000, 0x0000000088200000|100%|HC|  |TAMS 0x0000000088200000| PB 0x0000000088100000| Complete 
| 130|0x0000000088200000, 0x0000000088300000, 0x0000000088300000|100%|HC|  |TAMS 0x0000000088300000| PB 0x0000000088200000| Complete 
| 131|0x0000000088300000, 0x0000000088400000, 0x0000000088400000|100%|HC|  |TAMS 0x0000000088400000| PB 0x0000000088300000| Complete 
| 132|0x0000000088400000, 0x0000000088500000, 0x0000000088500000|100%|HS|  |TAMS 0x0000000088500000| PB 0x0000000088400000| Complete 
| 133|0x0000000088500000, 0x0000000088600000, 0x0000000088600000|100%|HC|  |TAMS 0x0000000088600000| PB 0x0000000088500000| Complete 
| 134|0x0000000088600000, 0x0000000088700000, 0x0000000088700000|100%|HC|  |TAMS 0x0000000088700000| PB 0x0000000088600000| Complete 
| 135|0x0000000088700000, 0x0000000088800000, 0x0000000088800000|100%|HC|  |TAMS 0x0000000088800000| PB 0x0000000088700000| Complete 
| 136|0x0000000088800000, 0x0000000088900000, 0x0000000088900000|100%|HS|  |TAMS 0x0000000088900000| PB 0x0000000088800000| Complete 
| 137|0x0000000088900000, 0x0000000088900000, 0x0000000088a00000|  0%| F|  |TAMS 0x0000000088900000| PB 0x0000000088900000| Untracked 
| 138|0x0000000088a00000, 0x0000000088a00000, 0x0000000088b00000|  0%| F|  |TAMS 0x0000000088a00000| PB 0x0000000088a00000| Untracked 
| 139|0x0000000088b00000, 0x0000000088b00000, 0x0000000088c00000|  0%| F|  |TAMS 0x0000000088b00000| PB 0x0000000088b00000| Untracked 
| 140|0x0000000088c00000, 0x0000000088c00000, 0x0000000088d00000|  0%| F|  |TAMS 0x0000000088c00000| PB 0x0000000088c00000| Untracked 
| 141|0x0000000088d00000, 0x0000000088d00000, 0x0000000088e00000|  0%| F|  |TAMS 0x0000000088d00000| PB 0x0000000088d00000| Untracked 
| 142|0x0000000088e00000, 0x0000000088e00000, 0x0000000088f00000|  0%| F|  |TAMS 0x0000000088e00000| PB 0x0000000088e00000| Untracked 
| 143|0x0000000088f00000, 0x0000000088f00000, 0x0000000089000000|  0%| F|  |TAMS 0x0000000088f00000| PB 0x0000000088f00000| Untracked 
| 144|0x0000000089000000, 0x0000000089000000, 0x0000000089100000|  0%| F|  |TAMS 0x0000000089000000| PB 0x0000000089000000| Untracked 
| 145|0x0000000089100000, 0x0000000089100000, 0x0000000089200000|  0%| F|  |TAMS 0x0000000089100000| PB 0x0000000089100000| Untracked 
| 146|0x0000000089200000, 0x0000000089200000, 0x0000000089300000|  0%| F|  |TAMS 0x0000000089200000| PB 0x0000000089200000| Untracked 
| 147|0x0000000089300000, 0x0000000089300000, 0x0000000089400000|  0%| F|  |TAMS 0x0000000089300000| PB 0x0000000089300000| Untracked 
| 148|0x0000000089400000, 0x0000000089400000, 0x0000000089500000|  0%| F|  |TAMS 0x0000000089400000| PB 0x0000000089400000| Untracked 
| 149|0x0000000089500000, 0x0000000089500000, 0x0000000089600000|  0%| F|  |TAMS 0x0000000089500000| PB 0x0000000089500000| Untracked 
| 150|0x0000000089600000, 0x0000000089600000, 0x0000000089700000|  0%| F|  |TAMS 0x0000000089600000| PB 0x0000000089600000| Untracked 
| 151|0x0000000089700000, 0x0000000089700000, 0x0000000089800000|  0%| F|  |TAMS 0x0000000089700000| PB 0x0000000089700000| Untracked 
| 152|0x0000000089800000, 0x0000000089800000, 0x0000000089900000|  0%| F|  |TAMS 0x0000000089800000| PB 0x0000000089800000| Untracked 
| 153|0x0000000089900000, 0x0000000089900000, 0x0000000089a00000|  0%| F|  |TAMS 0x0000000089900000| PB 0x0000000089900000| Untracked 
| 154|0x0000000089a00000, 0x0000000089a00000, 0x0000000089b00000|  0%| F|  |TAMS 0x0000000089a00000| PB 0x0000000089a00000| Untracked 
| 155|0x0000000089b00000, 0x0000000089b00000, 0x0000000089c00000|  0%| F|  |TAMS 0x0000000089b00000| PB 0x0000000089b00000| Untracked 
| 156|0x0000000089c00000, 0x0000000089c00000, 0x0000000089d00000|  0%| F|  |TAMS 0x0000000089c00000| PB 0x0000000089c00000| Untracked 
| 157|0x0000000089d00000, 0x0000000089d00000, 0x0000000089e00000|  0%| F|  |TAMS 0x0000000089d00000| PB 0x0000000089d00000| Untracked 
| 158|0x0000000089e00000, 0x0000000089e00000, 0x0000000089f00000|  0%| F|  |TAMS 0x0000000089e00000| PB 0x0000000089e00000| Untracked 
| 159|0x0000000089f00000, 0x0000000089f00000, 0x000000008a000000|  0%| F|  |TAMS 0x0000000089f00000| PB 0x0000000089f00000| Untracked 
| 160|0x000000008a000000, 0x000000008a000000, 0x000000008a100000|  0%| F|  |TAMS 0x000000008a000000| PB 0x000000008a000000| Untracked 
| 161|0x000000008a100000, 0x000000008a100000, 0x000000008a200000|  0%| F|  |TAMS 0x000000008a100000| PB 0x000000008a100000| Untracked 
| 162|0x000000008a200000, 0x000000008a200000, 0x000000008a300000|  0%| F|  |TAMS 0x000000008a200000| PB 0x000000008a200000| Untracked 
| 163|0x000000008a300000, 0x000000008a300000, 0x000000008a400000|  0%| F|  |TAMS 0x000000008a300000| PB 0x000000008a300000| Untracked 
| 164|0x000000008a400000, 0x000000008a400000, 0x000000008a500000|  0%| F|  |TAMS 0x000000008a400000| PB 0x000000008a400000| Untracked 
| 165|0x000000008a500000, 0x000000008a500000, 0x000000008a600000|  0%| F|  |TAMS 0x000000008a500000| PB 0x000000008a500000| Untracked 
| 166|0x000000008a600000, 0x000000008a600000, 0x000000008a700000|  0%| F|  |TAMS 0x000000008a600000| PB 0x000000008a600000| Untracked 
| 167|0x000000008a700000, 0x000000008a700000, 0x000000008a800000|  0%| F|  |TAMS 0x000000008a700000| PB 0x000000008a700000| Untracked 
| 168|0x000000008a800000, 0x000000008a800000, 0x000000008a900000|  0%| F|  |TAMS 0x000000008a800000| PB 0x000000008a800000| Untracked 
| 169|0x000000008a900000, 0x000000008a900000, 0x000000008aa00000|  0%| F|  |TAMS 0x000000008a900000| PB 0x000000008a900000| Untracked 
| 170|0x000000008aa00000, 0x000000008aa00000, 0x000000008ab00000|  0%| F|  |TAMS 0x000000008aa00000| PB 0x000000008aa00000| Untracked 
| 171|0x000000008ab00000, 0x000000008ab00000, 0x000000008ac00000|  0%| F|  |TAMS 0x000000008ab00000| PB 0x000000008ab00000| Untracked 
| 172|0x000000008ac00000, 0x000000008ac00000, 0x000000008ad00000|  0%| F|  |TAMS 0x000000008ac00000| PB 0x000000008ac00000| Untracked 
| 173|0x000000008ad00000, 0x000000008ad00000, 0x000000008ae00000|  0%| F|  |TAMS 0x000000008ad00000| PB 0x000000008ad00000| Untracked 
| 174|0x000000008ae00000, 0x000000008ae00000, 0x000000008af00000|  0%| F|  |TAMS 0x000000008ae00000| PB 0x000000008ae00000| Untracked 
| 175|0x000000008af00000, 0x000000008af00000, 0x000000008b000000|  0%| F|  |TAMS 0x000000008af00000| PB 0x000000008af00000| Untracked 
| 176|0x000000008b000000, 0x000000008b000000, 0x000000008b100000|  0%| F|  |TAMS 0x000000008b000000| PB 0x000000008b000000| Untracked 
| 177|0x000000008b100000, 0x000000008b100000, 0x000000008b200000|  0%| F|  |TAMS 0x000000008b100000| PB 0x000000008b100000| Untracked 
| 178|0x000000008b200000, 0x000000008b200000, 0x000000008b300000|  0%| F|  |TAMS 0x000000008b200000| PB 0x000000008b200000| Untracked 
| 179|0x000000008b300000, 0x000000008b300000, 0x000000008b400000|  0%| F|  |TAMS 0x000000008b300000| PB 0x000000008b300000| Untracked 
| 180|0x000000008b400000, 0x000000008b400000, 0x000000008b500000|  0%| F|  |TAMS 0x000000008b400000| PB 0x000000008b400000| Untracked 
| 181|0x000000008b500000, 0x000000008b500000, 0x000000008b600000|  0%| F|  |TAMS 0x000000008b500000| PB 0x000000008b500000| Untracked 
| 182|0x000000008b600000, 0x000000008b600000, 0x000000008b700000|  0%| F|  |TAMS 0x000000008b600000| PB 0x000000008b600000| Untracked 
| 183|0x000000008b700000, 0x000000008b700000, 0x000000008b800000|  0%| F|  |TAMS 0x000000008b700000| PB 0x000000008b700000| Untracked 
| 184|0x000000008b800000, 0x000000008b800000, 0x000000008b900000|  0%| F|  |TAMS 0x000000008b800000| PB 0x000000008b800000| Untracked 
| 185|0x000000008b900000, 0x000000008b900000, 0x000000008ba00000|  0%| F|  |TAMS 0x000000008b900000| PB 0x000000008b900000| Untracked 
| 186|0x000000008ba00000, 0x000000008ba00000, 0x000000008bb00000|  0%| F|  |TAMS 0x000000008ba00000| PB 0x000000008ba00000| Untracked 
| 187|0x000000008bb00000, 0x000000008bb00000, 0x000000008bc00000|  0%| F|  |TAMS 0x000000008bb00000| PB 0x000000008bb00000| Untracked 
| 188|0x000000008bc00000, 0x000000008bc00000, 0x000000008bd00000|  0%| F|  |TAMS 0x000000008bc00000| PB 0x000000008bc00000| Untracked 
| 189|0x000000008bd00000, 0x000000008bd00000, 0x000000008be00000|  0%| F|  |TAMS 0x000000008bd00000| PB 0x000000008bd00000| Untracked 
| 190|0x000000008be00000, 0x000000008be00000, 0x000000008bf00000|  0%| F|  |TAMS 0x000000008be00000| PB 0x000000008be00000| Untracked 
| 191|0x000000008bf00000, 0x000000008bf00000, 0x000000008c000000|  0%| F|  |TAMS 0x000000008bf00000| PB 0x000000008bf00000| Untracked 
| 192|0x000000008c000000, 0x000000008c000000, 0x000000008c100000|  0%| F|  |TAMS 0x000000008c000000| PB 0x000000008c000000| Untracked 
| 193|0x000000008c100000, 0x000000008c100000, 0x000000008c200000|  0%| F|  |TAMS 0x000000008c100000| PB 0x000000008c100000| Untracked 
| 194|0x000000008c200000, 0x000000008c200000, 0x000000008c300000|  0%| F|  |TAMS 0x000000008c200000| PB 0x000000008c200000| Untracked 
| 195|0x000000008c300000, 0x000000008c300000, 0x000000008c400000|  0%| F|  |TAMS 0x000000008c300000| PB 0x000000008c300000| Untracked 
| 196|0x000000008c400000, 0x000000008c400000, 0x000000008c500000|  0%| F|  |TAMS 0x000000008c400000| PB 0x000000008c400000| Untracked 
| 197|0x000000008c500000, 0x000000008c500000, 0x000000008c600000|  0%| F|  |TAMS 0x000000008c500000| PB 0x000000008c500000| Untracked 
| 198|0x000000008c600000, 0x000000008c600000, 0x000000008c700000|  0%| F|  |TAMS 0x000000008c600000| PB 0x000000008c600000| Untracked 
| 199|0x000000008c700000, 0x000000008c700000, 0x000000008c800000|  0%| F|  |TAMS 0x000000008c700000| PB 0x000000008c700000| Untracked 
| 200|0x000000008c800000, 0x000000008c8c1a00, 0x000000008c900000| 75%| S|CS|TAMS 0x000000008c800000| PB 0x000000008c800000| Complete 
| 201|0x000000008c900000, 0x000000008ca00000, 0x000000008ca00000|100%| S|CS|TAMS 0x000000008c900000| PB 0x000000008c900000| Complete 
| 202|0x000000008ca00000, 0x000000008cb00000, 0x000000008cb00000|100%| S|CS|TAMS 0x000000008ca00000| PB 0x000000008ca00000| Complete 
| 203|0x000000008cb00000, 0x000000008cb00000, 0x000000008cc00000|  0%| F|  |TAMS 0x000000008cb00000| PB 0x000000008cb00000| Untracked 
| 204|0x000000008cc00000, 0x000000008cc00000, 0x000000008cd00000|  0%| F|  |TAMS 0x000000008cc00000| PB 0x000000008cc00000| Untracked 
| 205|0x000000008cd00000, 0x000000008cd00000, 0x000000008ce00000|  0%| F|  |TAMS 0x000000008cd00000| PB 0x000000008cd00000| Untracked 
| 206|0x000000008ce00000, 0x000000008ce00000, 0x000000008cf00000|  0%| F|  |TAMS 0x000000008ce00000| PB 0x000000008ce00000| Untracked 
| 207|0x000000008cf00000, 0x000000008cf00000, 0x000000008d000000|  0%| F|  |TAMS 0x000000008cf00000| PB 0x000000008cf00000| Untracked 
| 208|0x000000008d000000, 0x000000008d000000, 0x000000008d100000|  0%| F|  |TAMS 0x000000008d000000| PB 0x000000008d000000| Untracked 
| 209|0x000000008d100000, 0x000000008d100000, 0x000000008d200000|  0%| F|  |TAMS 0x000000008d100000| PB 0x000000008d100000| Untracked 
| 210|0x000000008d200000, 0x000000008d200000, 0x000000008d300000|  0%| F|  |TAMS 0x000000008d200000| PB 0x000000008d200000| Untracked 
| 211|0x000000008d300000, 0x000000008d300000, 0x000000008d400000|  0%| F|  |TAMS 0x000000008d300000| PB 0x000000008d300000| Untracked 
| 321|0x0000000094100000, 0x0000000094100000, 0x0000000094200000|  0%| F|  |TAMS 0x0000000094100000| PB 0x0000000094100000| Untracked 
| 322|0x0000000094200000, 0x0000000094200000, 0x0000000094300000|  0%| F|  |TAMS 0x0000000094200000| PB 0x0000000094200000| Untracked 
| 323|0x0000000094300000, 0x0000000094300000, 0x0000000094400000|  0%| F|  |TAMS 0x0000000094300000| PB 0x0000000094300000| Untracked 
| 324|0x0000000094400000, 0x0000000094400000, 0x0000000094500000|  0%| F|  |TAMS 0x0000000094400000| PB 0x0000000094400000| Untracked 
| 325|0x0000000094500000, 0x0000000094500000, 0x0000000094600000|  0%| F|  |TAMS 0x0000000094500000| PB 0x0000000094500000| Untracked 
| 326|0x0000000094600000, 0x0000000094600000, 0x0000000094700000|  0%| F|  |TAMS 0x0000000094600000| PB 0x0000000094600000| Untracked 
| 327|0x0000000094700000, 0x0000000094700000, 0x0000000094800000|  0%| F|  |TAMS 0x0000000094700000| PB 0x0000000094700000| Untracked 
| 328|0x0000000094800000, 0x0000000094900000, 0x0000000094900000|100%| E|  |TAMS 0x0000000094800000| PB 0x0000000094800000| Complete 
| 329|0x0000000094900000, 0x0000000094a00000, 0x0000000094a00000|100%| E|CS|TAMS 0x0000000094900000| PB 0x0000000094900000| Complete 
| 330|0x0000000094a00000, 0x0000000094b00000, 0x0000000094b00000|100%| E|CS|TAMS 0x0000000094a00000| PB 0x0000000094a00000| Complete 
| 351|0x0000000095f00000, 0x0000000096000000, 0x0000000096000000|100%| E|CS|TAMS 0x0000000095f00000| PB 0x0000000095f00000| Complete 
|2034|0x00000000ff200000, 0x00000000ff300000, 0x00000000ff300000|100%| O|  |TAMS 0x00000000ff2ec180| PB 0x00000000ff2ec180| Updating 
|2035|0x00000000ff300000, 0x00000000ff400000, 0x00000000ff400000|100%| O|  |TAMS 0x00000000ff400000| PB 0x00000000ff400000| Untracked 
|2036|0x00000000ff400000, 0x00000000ff500000, 0x00000000ff500000|100%| O|  |TAMS 0x00000000ff500000| PB 0x00000000ff500000| Untracked 
|2037|0x00000000ff500000, 0x00000000ff600000, 0x00000000ff600000|100%| O|  |TAMS 0x00000000ff600000| PB 0x00000000ff600000| Untracked 
|2038|0x00000000ff600000, 0x00000000ff700000, 0x00000000ff700000|100%| O|  |TAMS 0x00000000ff700000| PB 0x00000000ff700000| Untracked 
|2039|0x00000000ff700000, 0x00000000ff800000, 0x00000000ff800000|100%| O|  |TAMS 0x00000000ff800000| PB 0x00000000ff800000| Untracked 
|2040|0x00000000ff800000, 0x00000000ff900000, 0x00000000ff900000|100%| O|  |TAMS 0x00000000ff900000| PB 0x00000000ff900000| Untracked 
|2041|0x00000000ff900000, 0x00000000ffa00000, 0x00000000ffa00000|100%| O|  |TAMS 0x00000000ffa00000| PB 0x00000000ffa00000| Untracked 
|2042|0x00000000ffa00000, 0x00000000ffb00000, 0x00000000ffb00000|100%| O|  |TAMS 0x00000000ffb00000| PB 0x00000000ffb00000| Untracked 
|2043|0x00000000ffb00000, 0x00000000ffc00000, 0x00000000ffc00000|100%| O|  |TAMS 0x00000000ffc00000| PB 0x00000000ffc00000| Untracked 
|2044|0x00000000ffc00000, 0x00000000ffd00000, 0x00000000ffd00000|100%| O|  |TAMS 0x00000000ffd00000| PB 0x00000000ffd00000| Updating 
|2045|0x00000000ffd00000, 0x00000000ffe00000, 0x00000000ffe00000|100%| O|  |TAMS 0x00000000ffe00000| PB 0x00000000ffe00000| Updating 
|2046|0x00000000ffe00000, 0x00000000fff00000, 0x00000000fff00000|100%| O|  |TAMS 0x00000000fff00000| PB 0x00000000fff00000| Updating 
|2047|0x00000000fff00000, 0x0000000100000000, 0x0000000100000000|100%| O|  |TAMS 0x0000000100000000| PB 0x0000000100000000| Updating 

Card table byte_map: [0x0000015559aa0000,0x0000015559ea0000] _byte_map_base: 0x00000155596a0000

Marking Bits: (CMBitMap*) 0x00000155472de720
 Bits: [0x0000015559ea0000, 0x000001555bea0000)

Polling page: 0x0000015544d00000

Metaspace:

Usage:
  Non-class:     90.63 MB used.
      Class:     13.76 MB used.
       Both:    104.40 MB used.

Virtual space:
  Non-class space:      128.00 MB reserved,      91.75 MB ( 72%) committed,  2 nodes.
      Class space:        1.00 GB reserved,      14.88 MB (  1%) committed,  1 nodes.
             Both:        1.12 GB reserved,     106.62 MB (  9%) committed. 

Chunk freelists:
   Non-Class:  3.78 MB
       Class:  1.09 MB
        Both:  4.88 MB

MaxMetaspaceSize: unlimited
CompressedClassSpaceSize: 1.00 GB
Initial GC threshold: 21.00 MB
Current GC threshold: 148.38 MB
CDS: off
 - commit_granule_bytes: 65536.
 - commit_granule_words: 8192.
 - virtual_space_node_default_size: 8388608.
 - enlarge_chunks_in_place: 1.
 - use_allocation_guard: 0.


Internal statistics:

num_allocs_failed_limit: 12.
num_arena_births: 4122.
num_arena_deaths: 0.
num_vsnodes_births: 3.
num_vsnodes_deaths: 0.
num_space_committed: 1705.
num_space_uncommitted: 0.
num_chunks_returned_to_freelist: 12.
num_chunks_taken_from_freelist: 8726.
num_chunk_merges: 12.
num_chunk_splits: 5719.
num_chunks_enlarged: 3546.
num_inconsistent_stats: 0.

CodeHeap 'non-profiled nmethods': size=120000Kb used=5759Kb max_used=6224Kb free=114240Kb
 bounds [0x0000015551e40000, 0x0000015552460000, 0x0000015559370000]
CodeHeap 'profiled nmethods': size=120000Kb used=17768Kb max_used=18441Kb free=102232Kb
 bounds [0x000001554a370000, 0x000001554b580000, 0x00000155518a0000]
CodeHeap 'non-nmethods': size=5760Kb used=2830Kb max_used=2869Kb free=2929Kb
 bounds [0x00000155518a0000, 0x0000015551b80000, 0x0000015551e40000]
 total_blobs=10635 nmethods=9678 adapters=861
 compilation: enabled
              stopped_count=0, restarted_count=0
 full_count=0

Compilation events (20 events):
Event: 13.430 Thread 0x000001556325ec80 nmethod 11348 0x000001554b570690 code [0x000001554b570840, 0x000001554b570a18]
Event: 13.430 Thread 0x000001556325ec80 11349       1       org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$TypeSpecificMappingAction::isNoOp (2 bytes)
Event: 13.430 Thread 0x000001556325ec80 nmethod 11349 0x0000015552453790 code [0x0000015552453920, 0x00000155524539e8]
Event: 13.430 Thread 0x000001556325ec80 11350       1       org.gradle.tooling.internal.adapter.MethodInvocation::getParameterTypes (5 bytes)
Event: 13.430 Thread 0x000001556325ec80 nmethod 11350 0x0000015552453a90 code [0x0000015552453c20, 0x0000015552453ce8]
Event: 13.430 Thread 0x000001556325ec80 11351       1       org.gradle.internal.impldep.com.google.common.base.Present::orNull (5 bytes)
Event: 13.430 Thread 0x000001556325ec80 nmethod 11351 0x0000015552453d90 code [0x0000015552453f20, 0x0000015552453fe8]
Event: 13.430 Thread 0x000001556325ec80 11352       1       org.gradle.tooling.internal.adapter.MethodInvocation::getParameters (5 bytes)
Event: 13.430 Thread 0x000001556325ec80 nmethod 11352 0x0000015552454090 code [0x0000015552454220, 0x00000155524542e8]
Event: 13.435 Thread 0x000001556325ec80 11353       3       org.gradle.internal.io.BufferCaster::cast (2 bytes)
Event: 13.435 Thread 0x000001556325ec80 nmethod 11353 0x000001554b570b10 code [0x000001554b570ca0, 0x000001554b570d90]
Event: 13.446 Thread 0x00000155649a9700 11354   !   4       java.net.URLClassLoader::findClass (47 bytes)
Event: 13.448 Thread 0x000001556325ec80 11355       3       org.gradle.api.internal.project.DefaultProjectStateRegistry$ProjectStateImpl::getMutableModel (8 bytes)
Event: 13.448 Thread 0x000001556325ec80 nmethod 11355 0x000001554b570e10 code [0x000001554b570fc0, 0x000001554b571190]
Event: 13.448 Thread 0x000001556325ec80 11356       3       org.gradle.api.internal.project.ProjectLifecycleController::getMutableModel (15 bytes)
Event: 13.448 Thread 0x000001556325ec80 nmethod 11356 0x000001554b571290 code [0x000001554b571440, 0x000001554b5715a8]
Event: 13.452 Thread 0x000001556325ec80 11358   !   3       sun.nio.ch.SocketChannelImpl::configureSocketNonBlockingIfVirtualThread (85 bytes)
Event: 13.452 Thread 0x000001556325ec80 nmethod 11358 0x000001554b571690 code [0x000001554b5718a0, 0x000001554b571f10]
Event: 13.453 Thread 0x000001556325ec80 11359       3       org.gradle.internal.build.AbstractBuildState::getBuildController (13 bytes)
Event: 13.453 Thread 0x000001556325ec80 nmethod 11359 0x000001554b572190 code [0x000001554b572340, 0x000001554b572608]

GC Heap History (20 events):
Event: 7.186 GC heap before
{Heap before GC invocations=15 (full 0):
 garbage-first heap   total 223232K, used 200393K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 130 young (133120K), 2 survivors (2048K)
 Metaspace       used 63005K, committed 64704K, reserved 1114112K
  class space    used 8526K, committed 9344K, reserved 1048576K
}
Event: 7.194 GC heap after
{Heap after GC invocations=16 (full 0):
 garbage-first heap   total 223232K, used 81389K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 15 young (15360K), 15 survivors (15360K)
 Metaspace       used 63005K, committed 64704K, reserved 1114112K
  class space    used 8526K, committed 9344K, reserved 1048576K
}
Event: 8.361 GC heap before
{Heap before GC invocations=16 (full 0):
 garbage-first heap   total 223232K, used 176621K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 109 young (111616K), 15 survivors (15360K)
 Metaspace       used 67741K, committed 69568K, reserved 1114112K
  class space    used 9168K, committed 10048K, reserved 1048576K
}
Event: 8.372 GC heap after
{Heap after GC invocations=17 (full 0):
 garbage-first heap   total 223232K, used 89975K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 67741K, committed 69568K, reserved 1114112K
  class space    used 9168K, committed 10048K, reserved 1048576K
}
Event: 8.886 GC heap before
{Heap before GC invocations=17 (full 0):
 garbage-first heap   total 223232K, used 189303K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 103 young (105472K), 11 survivors (11264K)
 Metaspace       used 68829K, committed 70656K, reserved 1114112K
  class space    used 9267K, committed 10176K, reserved 1048576K
}
Event: 8.892 GC heap after
{Heap after GC invocations=18 (full 0):
 garbage-first heap   total 223232K, used 102269K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 12 young (12288K), 12 survivors (12288K)
 Metaspace       used 68829K, committed 70656K, reserved 1114112K
  class space    used 9267K, committed 10176K, reserved 1048576K
}
Event: 9.567 GC heap before
{Heap before GC invocations=18 (full 0):
 garbage-first heap   total 223232K, used 171901K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 81 young (82944K), 12 survivors (12288K)
 Metaspace       used 76206K, committed 78080K, reserved 1179648K
  class space    used 10053K, committed 11008K, reserved 1048576K
}
Event: 9.572 GC heap after
{Heap after GC invocations=19 (full 0):
 garbage-first heap   total 223232K, used 107187K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 4 young (4096K), 4 survivors (4096K)
 Metaspace       used 76206K, committed 78080K, reserved 1179648K
  class space    used 10053K, committed 11008K, reserved 1048576K
}
Event: 10.560 GC heap before
{Heap before GC invocations=20 (full 0):
 garbage-first heap   total 223232K, used 199347K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 90 young (92160K), 4 survivors (4096K)
 Metaspace       used 84047K, committed 86016K, reserved 1179648K
  class space    used 11108K, committed 12096K, reserved 1048576K
}
Event: 10.565 GC heap after
{Heap after GC invocations=21 (full 0):
 garbage-first heap   total 223232K, used 114688K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 84047K, committed 86016K, reserved 1179648K
  class space    used 11108K, committed 12096K, reserved 1048576K
}
Event: 11.240 GC heap before
{Heap before GC invocations=21 (full 0):
 garbage-first heap   total 223232K, used 196608K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 90 young (92160K), 10 survivors (10240K)
 Metaspace       used 88259K, committed 90304K, reserved 1179648K
  class space    used 11681K, committed 12672K, reserved 1048576K
}
Event: 11.249 GC heap after
{Heap after GC invocations=22 (full 0):
 garbage-first heap   total 223232K, used 118419K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 88259K, committed 90304K, reserved 1179648K
  class space    used 11681K, committed 12672K, reserved 1048576K
}
Event: 11.322 GC heap before
{Heap before GC invocations=22 (full 0):
 garbage-first heap   total 223232K, used 125587K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 14 young (14336K), 6 survivors (6144K)
 Metaspace       used 88821K, committed 90880K, reserved 1179648K
  class space    used 11762K, committed 12800K, reserved 1048576K
}
Event: 11.325 GC heap after
{Heap after GC invocations=23 (full 0):
 garbage-first heap   total 223232K, used 119180K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 6 young (6144K), 6 survivors (6144K)
 Metaspace       used 88821K, committed 90880K, reserved 1179648K
  class space    used 11762K, committed 12800K, reserved 1048576K
}
Event: 12.255 GC heap before
{Heap before GC invocations=24 (full 0):
 garbage-first heap   total 223232K, used 203148K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 88 young (90112K), 6 survivors (6144K)
 Metaspace       used 95740K, committed 97856K, reserved 1179648K
  class space    used 12853K, committed 13888K, reserved 1048576K
}
Event: 12.261 GC heap after
{Heap after GC invocations=25 (full 0):
 garbage-first heap   total 223232K, used 122584K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 10 young (10240K), 10 survivors (10240K)
 Metaspace       used 95740K, committed 97856K, reserved 1179648K
  class space    used 12853K, committed 13888K, reserved 1048576K
}
Event: 13.294 GC heap before
{Heap before GC invocations=25 (full 0):
 garbage-first heap   total 223232K, used 218840K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 85 young (87040K), 10 survivors (10240K)
 Metaspace       used 105031K, committed 107264K, reserved 1179648K
  class space    used 13979K, committed 15104K, reserved 1048576K
}
Event: 13.307 GC heap after
{Heap after GC invocations=26 (full 0):
 garbage-first heap   total 242688K, used 147376K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 11 young (11264K), 11 survivors (11264K)
 Metaspace       used 105031K, committed 107264K, reserved 1179648K
  class space    used 13979K, committed 15104K, reserved 1048576K
}
Event: 13.395 GC heap before
{Heap before GC invocations=26 (full 0):
 garbage-first heap   total 242688K, used 157616K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 22 young (22528K), 11 survivors (11264K)
 Metaspace       used 105653K, committed 107904K, reserved 1179648K
  class space    used 14035K, committed 15168K, reserved 1048576K
}
Event: 13.401 GC heap after
{Heap after GC invocations=27 (full 0):
 garbage-first heap   total 242688K, used 151484K [0x0000000080000000, 0x0000000100000000)
  region size 1024K, 3 young (3072K), 3 survivors (3072K)
 Metaspace       used 105653K, committed 107904K, reserved 1179648K
  class space    used 14035K, committed 15168K, reserved 1048576K
}

Dll operation events (3 events):
Event: 0.008 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll
Event: 0.014 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
Event: 0.560 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\verify.dll

Deoptimization events (20 events):
Event: 13.171 Thread 0x0000015563b9bda0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000155523426ac relative=0x0000000000000d4c
Event: 13.171 Thread 0x0000015563b9bda0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000155523426ac method=com.google.common.cache.LocalCache$Segment.get(Ljava/lang/Object;I)Ljava/lang/Object; @ 4 c2
Event: 13.171 Thread 0x0000015563b9bda0 DEOPT PACKING pc=0x00000155523426ac sp=0x0000003fc0bf9a20
Event: 13.171 Thread 0x0000015563b9bda0 DEOPT UNPACKING pc=0x00000155518f46a2 sp=0x0000003fc0bf9908 mode 2
Event: 13.175 Thread 0x000001556b0a8c10 Uncommon trap: trap_request=0xffffff3d fr.pc=0x0000015552297d1c relative=0x00000000000002dc
Event: 13.175 Thread 0x000001556b0a8c10 Uncommon trap: reason=unstable_fused_if action=reinterpret pc=0x0000015552297d1c method=java.lang.CharacterDataLatin1.equalsIgnoreCase(BB)Z @ 16 c2
Event: 13.175 Thread 0x000001556b0a8c10 DEOPT PACKING pc=0x0000015552297d1c sp=0x0000003fc55fe030
Event: 13.175 Thread 0x000001556b0a8c10 DEOPT UNPACKING pc=0x00000155518f46a2 sp=0x0000003fc55fde88 mode 2
Event: 13.175 Thread 0x000001556b0a8c10 Uncommon trap: trap_request=0xffffff3d fr.pc=0x0000015551f55308 relative=0x0000000000000168
Event: 13.175 Thread 0x000001556b0a8c10 Uncommon trap: reason=unstable_fused_if action=reinterpret pc=0x0000015551f55308 method=java.lang.CharacterDataLatin1.equalsIgnoreCase(BB)Z @ 16 c2
Event: 13.175 Thread 0x000001556b0a8c10 DEOPT PACKING pc=0x0000015551f55308 sp=0x0000003fc55fdf60
Event: 13.175 Thread 0x000001556b0a8c10 DEOPT UNPACKING pc=0x00000155518f46a2 sp=0x0000003fc55fde80 mode 2
Event: 13.175 Thread 0x000001556b0a8c10 Uncommon trap: trap_request=0xffffff3d fr.pc=0x0000015551f33c08 relative=0x0000000000000068
Event: 13.175 Thread 0x000001556b0a8c10 Uncommon trap: reason=unstable_fused_if action=reinterpret pc=0x0000015551f33c08 method=java.lang.CharacterDataLatin1.equalsIgnoreCase(BB)Z @ 16 c2
Event: 13.175 Thread 0x000001556b0a8c10 DEOPT PACKING pc=0x0000015551f33c08 sp=0x0000003fc55fdef0
Event: 13.175 Thread 0x000001556b0a8c10 DEOPT UNPACKING pc=0x00000155518f46a2 sp=0x0000003fc55fde80 mode 2
Event: 13.176 Thread 0x0000015563b9bda0 Uncommon trap: trap_request=0xffffff45 fr.pc=0x00000155522eeb84 relative=0x0000000000000384
Event: 13.176 Thread 0x0000015563b9bda0 Uncommon trap: reason=unstable_if action=reinterpret pc=0x00000155522eeb84 method=com.google.common.cache.LocalCache$Segment.recordWrite(Lcom/google/common/cache/ReferenceEntry;IJ)V @ 22 c2
Event: 13.176 Thread 0x0000015563b9bda0 DEOPT PACKING pc=0x00000155522eeb84 sp=0x0000003fc0bf9670
Event: 13.176 Thread 0x0000015563b9bda0 DEOPT UNPACKING pc=0x00000155518f46a2 sp=0x0000003fc0bf9628 mode 2

Classes loaded (20 events):
Event: 12.610 Loading class java/util/TreeMap$NavigableSubMap done
Event: 12.610 Loading class java/util/TreeMap$AscendingSubMap done
Event: 12.611 Loading class java/util/TreeMap$NavigableSubMap$SubMapKeyIterator
Event: 12.611 Loading class java/util/TreeMap$NavigableSubMap$SubMapIterator
Event: 12.611 Loading class java/util/TreeMap$NavigableSubMap$SubMapIterator done
Event: 12.611 Loading class java/util/TreeMap$NavigableSubMap$SubMapKeyIterator done
Event: 12.688 Loading class java/util/stream/ReduceOps$5
Event: 12.688 Loading class java/util/stream/ReduceOps$5 done
Event: 12.689 Loading class java/util/stream/ReduceOps$CountingSink$OfRef
Event: 12.689 Loading class java/util/stream/ReduceOps$CountingSink
Event: 12.689 Loading class java/util/stream/ReduceOps$CountingSink done
Event: 12.689 Loading class java/util/stream/ReduceOps$CountingSink$OfRef done
Event: 12.795 Loading class java/util/TreeMap$KeySpliterator
Event: 12.795 Loading class java/util/TreeMap$TreeMapSpliterator
Event: 12.795 Loading class java/util/TreeMap$TreeMapSpliterator done
Event: 12.795 Loading class java/util/TreeMap$KeySpliterator done
Event: 12.857 Loading class java/io/PushbackInputStream
Event: 12.858 Loading class java/io/PushbackInputStream done
Event: 13.086 Loading class java/util/TreeMap$ValueSpliterator
Event: 13.086 Loading class java/util/TreeMap$ValueSpliterator done

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (20 events):
Event: 12.803 Thread 0x0000015563b9d210 Exception <a 'java/lang/NoSuchMethodError'{0x000000008b068590}: static Lorg/gradle/internal/build/event/types/DefaultTaskStartedProgressEvent;.<clinit>()V> (0x000000008b068590) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.804 Thread 0x0000015563b9d210 Exception <a 'java/lang/NoSuchMethodError'{0x000000008b073a48}: static Lorg/gradle/internal/build/event/types/DefaultTaskDescriptor;.<clinit>()V> (0x000000008b073a48) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.810 Thread 0x0000015563b9d210 Exception <a 'java/lang/NoSuchMethodError'{0x000000008b082680}: static Lorg/gradle/internal/build/event/types/DefaultTaskFinishedProgressEvent;.<clinit>()V> (0x000000008b082680) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.811 Thread 0x0000015563b9d210 Exception <a 'java/lang/NoSuchMethodError'{0x000000008b0c8eb0}: static Lorg/gradle/internal/build/event/types/DefaultTaskSuccessResult;.<clinit>()V> (0x000000008b0c8eb0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.812 Thread 0x0000015563b9d210 Exception <a 'java/lang/NoSuchMethodError'{0x000000008b0ca5f0}: static Lorg/gradle/internal/build/event/types/AbstractTaskResult;.<clinit>()V> (0x000000008b0ca5f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.840 Thread 0x000001556b0a8c10 Implicit null exception at 0x0000015552123007 to 0x00000155521230cc
Event: 12.875 Thread 0x000001556b0a8c10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008aa07f40}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object)'> (0x000000008aa07f40) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 12.876 Thread 0x000001556b0a8c10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008aa19958}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int)'> (0x000000008aa19958) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 12.876 Thread 0x000001556b0a8c10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008aa1cce0}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStaticInit(java.lang.Object, int)'> (0x000000008aa1cce0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 12.948 Thread 0x000001556b0a8c10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a4299f0}: static Lcom/intellij/gradle/toolingExtension/impl/javaModel/manifestModel/DefaultJavaGradleManifestModel;.<clinit>()V> (0x000000008a4299f0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.950 Thread 0x000001556b0a8c10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a439000}: static Lorg/jetbrains/kotlin/android/synthetic/idea/AndroidExtensionsGradleModelImpl;.<clinit>()V> (0x000000008a439000) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.951 Thread 0x000001556b0a8c10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a4414b0}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/parcelize/ParcelizeGradleModelImpl;.<clinit>()V> (0x000000008a4414b0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.952 Thread 0x000001556b0a8c10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a4494e8}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/assignment/AssignmentModelImpl;.<clinit>()V> (0x000000008a4494e8) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.953 Thread 0x000001556b0a8c10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a458e80}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/samWithReceiver/SamWithReceiverModelImpl;.<clinit>()V> (0x000000008a458e80) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.954 Thread 0x000001556b0a8c10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a461df0}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/noarg/NoArgModelImpl;.<clinit>()V> (0x000000008a461df0) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.955 Thread 0x000001556b0a8c10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a46a910}: static Lorg/jetbrains/kotlin/idea/gradleTooling/model/allopen/AllOpenModelImpl;.<clinit>()V> (0x000000008a46a910) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.964 Thread 0x000001556b0a8c10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a306790}: static Lorg/jetbrains/plugins/gradle/tooling/internal/VersionCatalogsModelImpl;.<clinit>()V> (0x000000008a306790) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 12.976 Thread 0x000001556b0a8c10 Exception <a 'java/lang/NoSuchMethodError'{0x000000008a3b5158}: static Lorg/jetbrains/plugins/gradle/tooling/serialization/internal/adapter/kotlin/dsl/InternalKotlinDslScriptsModel;.<clinit>()V> (0x000000008a3b5158) 
thrown [s\src\hotspot\share\prims\jni.cpp, line 1139]
Event: 13.082 Thread 0x0000015563b9bda0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000089c54b08}: 'void java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object)'> (0x0000000089c54b08) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]
Event: 13.088 Thread 0x0000015563b9bda0 Exception <a 'java/lang/NoSuchMethodError'{0x0000000089c8bad0}: 'java.lang.Object java.lang.invoke.DirectMethodHandle$Holder.invokeStatic(java.lang.Object, int, java.lang.Object)'> (0x0000000089c8bad0) 
thrown [s\src\hotspot\share\interpreter\linkResolver.cpp, line 784]

ZGC Phase Switch (0 events):
No events

VM Operations (20 events):
Event: 12.735 Executing VM operation: ICBufferFull done
Event: 12.798 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 12.799 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 12.801 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 12.801 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 12.807 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 12.808 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 13.091 Executing VM operation: ICBufferFull
Event: 13.091 Executing VM operation: ICBufferFull done
Event: 13.186 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 13.187 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 13.188 Executing VM operation: HandshakeAllThreads (Deoptimize)
Event: 13.189 Executing VM operation: HandshakeAllThreads (Deoptimize) done
Event: 13.241 Executing VM operation: ICBufferFull
Event: 13.242 Executing VM operation: ICBufferFull done
Event: 13.293 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause)
Event: 13.307 Executing VM operation: G1CollectForAllocation (G1 Evacuation Pause) done
Event: 13.395 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation)
Event: 13.401 Executing VM operation: G1TryInitiateConcMark (G1 Humongous Allocation) done
Event: 13.462 Executing VM operation: G1PauseRemark

Memory protections (0 events):
No events

Nmethod flushes (20 events):
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a4c4710
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a4c4c90
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a5eb410
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a5ebe90
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a6f8390
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a702290
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a7d4090
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a80c510
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a812510
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a813810
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a93b890
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a93c190
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a999510
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554a9a2510
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554aad4890
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554adefa10
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554adf6d90
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554af82790
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554b3d7a90
Event: 13.467 Thread 0x0000015563042690 flushing  nmethod 0x000001554b3d8610

Events (20 events):
Event: 11.634 Thread 0x000001556b140830 Thread exited: 0x000001556b140830
Event: 11.767 Thread 0x0000015564b59140 Thread exited: 0x0000015564b59140
Event: 12.035 Thread 0x000001556325ec80 Thread added: 0x00000155693b2ca0
Event: 12.315 Thread 0x000001556325ec80 Thread added: 0x00000155649ae4b0
Event: 12.463 Thread 0x0000015563b9bda0 Thread added: 0x000001556d67bd80
Event: 12.527 Thread 0x00000155649ae4b0 Thread exited: 0x00000155649ae4b0
Event: 12.607 Thread 0x0000015563b9bda0 Thread added: 0x000001556d677960
Event: 12.608 Thread 0x00000155693b2ca0 Thread exited: 0x00000155693b2ca0
Event: 12.624 Thread 0x000001556d677960 Thread exited: 0x000001556d677960
Event: 12.738 Thread 0x0000015563b9bda0 Thread added: 0x000001556b0ab4f0
Event: 12.746 Thread 0x000001556b0ab4f0 Thread added: 0x000001556b0addd0
Event: 12.746 Thread 0x000001556b0ab4f0 Thread added: 0x000001556b0a99b0
Event: 12.746 Thread 0x000001556b0ab4f0 Thread added: 0x000001556b0ac290
Event: 12.746 Thread 0x000001556b0ab4f0 Thread added: 0x000001556b0aa080
Event: 12.746 Thread 0x000001556b0ab4f0 Thread added: 0x000001556b0aa750
Event: 12.746 Thread 0x000001556b0ab4f0 Thread added: 0x000001556b0aeb70
Event: 12.746 Thread 0x000001556b0ab4f0 Thread added: 0x000001556b0a8540
Event: 12.772 Thread 0x000001556b0ab4f0 Thread added: 0x000001556b0ac960
Event: 12.840 Thread 0x0000015563b9bda0 Thread added: 0x000001556b0a8c10
Event: 12.957 Thread 0x000001556325ec80 Thread added: 0x00000155649a9700


Dynamic libraries:
0x00007ff645790000 - 0x00007ff64579a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.exe
0x00007ffa35f30000 - 0x00007ffa36128000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffa35030000 - 0x00007ffa350f2000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffa33740000 - 0x00007ffa33a36000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffa30220000 - 0x00007ffa302b4000 	C:\WINDOWS\SYSTEM32\apphelp.dll
0x00007ffa1ea70000 - 0x00007ffa1ef06000 	C:\WINDOWS\SYSTEM32\AcLayers.DLL
0x00007ffa34310000 - 0x00007ffa343ae000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffa34e90000 - 0x00007ffa3502d000 	C:\WINDOWS\System32\USER32.dll
0x00007ffa33dc0000 - 0x00007ffa33de2000 	C:\WINDOWS\System32\win32u.dll
0x00007ffa357f0000 - 0x00007ffa3581b000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffa33c00000 - 0x00007ffa33d19000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffa33d20000 - 0x00007ffa33dbd000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffa33640000 - 0x00007ffa33740000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffa34720000 - 0x00007ffa34e8e000 	C:\WINDOWS\System32\SHELL32.dll
0x00007ffa34160000 - 0x00007ffa341bb000 	C:\WINDOWS\System32\SHLWAPI.dll
0x00007ffa34000000 - 0x00007ffa340b1000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffa340c0000 - 0x00007ffa3415f000 	C:\WINDOWS\System32\sechost.dll
0x00007ffa35100000 - 0x00007ffa35223000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffa33af0000 - 0x00007ffa33b17000 	C:\WINDOWS\System32\bcrypt.dll
0x0000015544c80000 - 0x0000015544c83000 	C:\WINDOWS\SYSTEM32\sfc.dll
0x00007ffa126e0000 - 0x00007ffa12784000 	C:\WINDOWS\SYSTEM32\WINSPOOL.DRV
0x00007ffa35230000 - 0x00007ffa35583000 	C:\WINDOWS\System32\combase.dll
0x00007ffa356b0000 - 0x00007ffa3575d000 	C:\WINDOWS\System32\shcore.dll
0x00007ffa10fe0000 - 0x00007ffa10ff2000 	C:\WINDOWS\SYSTEM32\sfc_os.DLL
0x00007ffa30590000 - 0x00007ffa305a1000 	C:\WINDOWS\SYSTEM32\SortWindows61.dll
0x00007ffa35dd0000 - 0x00007ffa35dff000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffa240f0000 - 0x00007ffa24108000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffa181c0000 - 0x00007ffa181db000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffa228a0000 - 0x00007ffa22b3a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7\COMCTL32.dll
0x00007ffa240e0000 - 0x00007ffa240ec000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffa0ba00000 - 0x00007ffa0ba8d000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ff9b4880000 - 0x00007ff9b550b000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffa343b0000 - 0x00007ffa3441b000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffa33420000 - 0x00007ffa3346b000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffa28320000 - 0x00007ffa28347000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffa2b410000 - 0x00007ffa2b41a000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffa33400000 - 0x00007ffa33412000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffa31e30000 - 0x00007ffa31e42000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffa23210000 - 0x00007ffa2321a000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffa31bc0000 - 0x00007ffa31dc1000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffa21760000 - 0x00007ffa21794000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffa33b20000 - 0x00007ffa33ba2000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffa217a0000 - 0x00007ffa217ae000 	C:\Program Files\Android\Android Studio\jbr\bin\instrument.dll
0x00007ffa18160000 - 0x00007ffa18180000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll
0x00007ffa14050000 - 0x00007ffa14068000 	C:\Program Files\Android\Android Studio\jbr\bin\zip.dll
0x00007ffa31410000 - 0x00007ffa31bb3000 	C:\WINDOWS\SYSTEM32\windows.storage.dll
0x00007ffa32f20000 - 0x00007ffa32f4b000 	C:\WINDOWS\SYSTEM32\Wldp.dll
0x00007ffa35e20000 - 0x00007ffa35eed000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffa334f0000 - 0x00007ffa33515000 	C:\WINDOWS\SYSTEM32\profapi.dll
0x00007ffa180a0000 - 0x00007ffa180b0000 	C:\Program Files\Android\Android Studio\jbr\bin\net.dll
0x00007ffa2f470000 - 0x00007ffa2f57a000 	C:\WINDOWS\SYSTEM32\WINHTTP.dll
0x00007ffa32c80000 - 0x00007ffa32cea000 	C:\WINDOWS\system32\mswsock.dll
0x00007ffa14030000 - 0x00007ffa14046000 	C:\Program Files\Android\Android Studio\jbr\bin\nio.dll
0x00007ffa18080000 - 0x00007ffa18090000 	C:\Program Files\Android\Android Studio\jbr\bin\verify.dll
0x00007ffa23360000 - 0x00007ffa23387000 	C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64\native-platform.dll
0x00007ff9f0020000 - 0x00007ff9f0098000 	C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu\gradle-fileevents.dll
0x00007ffa14020000 - 0x00007ffa14029000 	C:\Program Files\Android\Android Studio\jbr\bin\management.dll
0x00007ffa14010000 - 0x00007ffa1401b000 	C:\Program Files\Android\Android Studio\jbr\bin\management_ext.dll
0x00007ffa34220000 - 0x00007ffa34228000 	C:\WINDOWS\System32\PSAPI.DLL
0x00007ffa32960000 - 0x00007ffa3299b000 	C:\WINDOWS\SYSTEM32\IPHLPAPI.DLL
0x00007ffa345d0000 - 0x00007ffa345d8000 	C:\WINDOWS\System32\NSI.dll
0x00007ffa13f80000 - 0x00007ffa13f89000 	C:\Program Files\Android\Android Studio\jbr\bin\extnet.dll
0x00007ffa32e70000 - 0x00007ffa32e88000 	C:\WINDOWS\SYSTEM32\CRYPTSP.dll
0x00007ffa325a0000 - 0x00007ffa325d8000 	C:\WINDOWS\system32\rsaenh.dll
0x00007ffa334b0000 - 0x00007ffa334de000 	C:\WINDOWS\SYSTEM32\USERENV.dll
0x00007ffa32e90000 - 0x00007ffa32e9c000 	C:\WINDOWS\SYSTEM32\CRYPTBASE.dll
0x00007ffa2f990000 - 0x00007ffa2f997000 	C:\WINDOWS\system32\wshunix.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.19041.5915_none_60b4b9d171f9c7c7;C:\Program Files\Android\Android Studio\jbr\bin\server;C:\Users\<USER>\.gradle\native\1def1411415f61bf3af743bc5b6707747c0891f09f0c88961ee8f79bc544acac\windows-amd64;C:\Users\<USER>\.gradle\native\0.2.7\x86_64-windows-gnu

VM Arguments:
jvm_args: --add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.lang.invoke=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.api=ALL-UNNAMED --add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.prefs/java.util.prefs=ALL-UNNAMED --add-opens=java.base/java.nio.charset=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.util.concurrent.atomic=ALL-UNNAMED --add-opens=java.xml/javax.xml.namespace=ALL-UNNAMED -Xmx2048m -Dfile.encoding=UTF-8 -Duser.country=US -Duser.language=en -Duser.variant -javaagent:C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\agents\gradle-instrumentation-agent-8.13.jar 
java_command: org.gradle.launcher.daemon.bootstrap.GradleDaemon 8.13
java_class_path (initial): C:\Users\<USER>\.gradle\wrapper\dists\gradle-8.13-bin\5xuhj0ry160q40clulazy9h7d\gradle-8.13\lib\gradle-daemon-main-8.13.jar
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 1048576                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 369098752                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 2147483648                                {product} {command line}
   size_t MaxNewSize                               = 1287651328                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 1048576                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 2147483648                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Program Files\Python313\Scripts\;C:\Program Files\Python313\;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\flutter sdk\flutter\bin;C:\Program Files\Java\jdk-21\bin;C:\OpenSSH-Win64\OpenSSH-Win64;C:\Windows\System32;C:\Program Files\Git\cmd;C:\Program Files\Git\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\xampp\php\php.exe;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\scrcpy;C:\ProgramData\ComposerSetup\bin;C:\Program Files\nodejs\;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:;C:\Users\<USER>\AppData\Roaming\Microsoft\Windows\Start Menu\Programs\Python 3.13;C:\Program Files\Void\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\xampp\php;C:\Program Files\Java\jdk1.8.0_111\bin;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\PyCharm Community Edition 2022.1.3\bin;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Program Files\Java\jdk1.8.0_291\bin;C:\Program Files\JetBrains\PhpStorm 2024.1.3\bin;;C:\dart-sdk\bin;C:\flutter sdk\flutter\bin;C:\Users\<USER>\AppData\Roaming\Composer\vendor\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Microsoft\WinGet\Packages\Genymobile.scrcpy_Microsoft.Winget.Source_8wekyb3d8bbwe\scrcpy-win64-v3.1;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin
USERNAME=ntc
OS=Windows_NT
PROCESSOR_IDENTIFIER=AMD64 Family 23 Model 24 Stepping 1, AuthenticAMD
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 28, weak refs: 0

JNI global refs memory usage: 835, weak refs: 833

Process memory usage:
Resident Set Size: 585556K (2% of 23015712K total physical memory with 3998900K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 36700K
Loader bootstrap                                                                       : 26202K
Loader org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: 25382K
Loader org.gradle.initialization.MixInLegacyTypesClassLoader                           : 12389K
Loader org.gradle.internal.classloader.VisitableURLClassLoader                         : 4786K
Loader jdk.internal.loader.ClassLoaders$PlatformClassLoader                            : 503K
Loader jdk.internal.reflect.DelegatingClassLoader                                      : 384K
Loader org.gradle.groovy.scripts.internal.DefaultScriptCompilationHandler$ScriptClassLoader: 249K
Loader jdk.internal.loader.ClassLoaders$AppClassLoader                                 : 235K
Loader org.codehaus.groovy.runtime.callsite.CallSiteClassLoader                        : 70784B

Classes loaded by more than one classloader:
Class org.gradle.internal.IoActions                                                   : loaded 3 times (x 67B)
Class org.gradle.internal.Cast                                                        : loaded 3 times (x 67B)
Class org.gradle.api.Action                                                           : loaded 3 times (x 66B)
Class org.gradle.api.GradleException                                                  : loaded 3 times (x 78B)
Class org.gradle.api.UncheckedIOException                                             : loaded 3 times (x 78B)
Class kotlin.sequences.FlatteningSequence                                             : loaded 2 times (x 71B)
Class kotlin.NotImplementedError                                                      : loaded 2 times (x 78B)
Class org.gradle.internal.classloader.FilteringClassLoader                            : loaded 2 times (x 101B)
Class [Lcom.google.common.base.AbstractIterator$State;                                : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$IsEither                                     : loaded 2 times (x 107B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$2                      : loaded 2 times (x 81B)
Class com.amazon.ion.impl._Private_IonValue                                           : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$Companion: loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__ReversedViewsKt                               : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt                                 : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.ClassLoaderHierarchy                            : loaded 2 times (x 66B)
Class build_eoun890h7qlxwfhq6ek4y6yum$_run_closure1                                   : loaded 2 times (x 135B)
Class com.android.builder.model.v2.models.VariantDependenciesAdjacencyList            : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableEnumSet                                      : loaded 2 times (x 142B)
Class com.google.common.util.concurrent.ExecutionError                                : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableList$1                                       : loaded 2 times (x 93B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$3                      : loaded 2 times (x 81B)
Class com.amazon.ion.IonException                                                     : loaded 2 times (x 80B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap$ObjectFactory            : loaded 2 times (x 66B)
Class org.gradle.tooling.model.build.GradleEnvironment                                : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$InvocationHandlerImpl: loaded 2 times (x 73B)
Class org.gradle.api.internal.classpath.ManifestUtil                                  : loaded 2 times (x 67B)
Class com.google.common.collect.RegularImmutableMap                                   : loaded 2 times (x 117B)
Class com.google.common.collect.ImmutableMap$MapViewOfValuesAsSingletonSets           : loaded 2 times (x 122B)
Class org.jetbrains.plugins.gradle.model.UnresolvedExternalDependency                 : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$4                      : loaded 2 times (x 81B)
Class com.amazon.ion.system.IonBinaryWriterBuilder                                    : loaded 2 times (x 95B)
Class org.jetbrains.plugins.gradle.model.GradleExtensions                             : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.AnnotationProcessingModel                    : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.TargetTypeProvider                          : loaded 2 times (x 66B)
Class [Lorg.objectweb.asm.Symbol;                                                     : loaded 2 times (x 65B)
Class org.objectweb.asm.ClassVisitor                                                  : loaded 2 times (x 84B)
Class com.google.common.collect.UnmodifiableListIterator                              : loaded 2 times (x 91B)
Class com.intellij.util.ThrowableConsumer                                             : loaded 2 times (x 66B)
Class kotlin.sequences.TransformingSequence                                           : loaded 2 times (x 71B)
Class org.gradle.tooling.internal.adapter.MethodInvocation                            : loaded 2 times (x 81B)
Class org.gradle.internal.operations.MultipleBuildOperationFailures                   : loaded 2 times (x 91B)
Class com.google.common.base.Platform$JdkPatternCompiler                              : loaded 2 times (x 71B)
Class org.objectweb.asm.MethodTooLargeException                                       : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.CacheBuilder$OneWeigher;                              : loaded 2 times (x 65B)
Class com.google.common.base.Suppliers                                                : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.idea.gradleTooling.AndroidAwareGradleModelProvider$Companion: loaded 2 times (x 67B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$StreamCloseMode;                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl.lite.IonSymbolLite                                          : loaded 2 times (x 242B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1     : loaded 2 times (x 76B)
Class com.intellij.util.containers.IntObjectHashMap$ArrayProducer                     : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.noarg.NoArgModel                  : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.DefaultClassLoaderFactory                       : loaded 2 times (x 78B)
Class com.google.common.base.CharMatcher$None                                         : loaded 2 times (x 108B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$2     : loaded 2 times (x 76B)
Class kotlin.sequences.SequencesKt___SequencesKt                                      : loaded 2 times (x 67B)
Class kotlin.collections.AbstractList$Companion                                       : loaded 2 times (x 67B)
Class org.gradle.internal.classloader.ClassLoaderVisitor                              : loaded 2 times (x 72B)
Class [Lcom.google.common.collect.ImmutableMapEntry;                                  : loaded 2 times (x 65B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService: loaded 2 times (x 73B)
Class kotlin.sequences.SequencesKt___SequencesKt$flatMap$2                            : loaded 2 times (x 121B)
Class org.gradle.api.internal.classpath.EffectiveClassPath                            : loaded 2 times (x 86B)
Class com.android.builder.model.v2.models.AndroidDsl                                  : loaded 2 times (x 66B)
Class com.google.common.collect.ListMultimap                                          : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableEnumMap                                      : loaded 2 times (x 122B)
Class com.amazon.ion.impl.lite.IonSystemLite                                          : loaded 2 times (x 271B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode                    : loaded 2 times (x 78B)
Class com.amazon.ion.impl.bin._Private_IonRawWriter                                   : loaded 2 times (x 66B)
Class com.amazon.ion.IonWriter                                                        : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.gradle.DefaultProjectIdentifier                     : loaded 2 times (x 82B)
Class com.google.common.collect.Sets$1                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.AnnotationVisitor                                             : loaded 2 times (x 74B)
Class com.google.common.base.Splitter                                                 : loaded 2 times (x 68B)
Class com.amazon.ion.impl._Private_RecyclingQueue$ElementFactory                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_IonContainer                                       : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIntHashMap                         : loaded 2 times (x 105B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.allopen.AllOpenModel              : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathProvider                                : loaded 2 times (x 72B)
Class com.android.builder.model.v2.models.BasicAndroidProject                         : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$2                                                : loaded 2 times (x 135B)
Class com.amazon.ion.impl._Private_RecyclingStack$$Iterator                           : loaded 2 times (x 95B)
Class com.amazon.ion.impl.SharedSymbolTable                                           : loaded 2 times (x 89B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter                                      : loaded 2 times (x 165B)
Class com.amazon.ion.SymbolToken                                                      : loaded 2 times (x 66B)
Class kotlin.sequences.SequenceScope                                                  : loaded 2 times (x 69B)
Class kotlin.sequences.Sequence                                                       : loaded 2 times (x 66B)
Class [Lorg.gradle.api.internal.ClassPathProvider;                                    : loaded 2 times (x 65B)
Class org.gradle.api.internal.ClassPathProvider                                       : loaded 2 times (x 66B)
Class com.google.common.collect.Sets$3                                                : loaded 2 times (x 135B)
Class com.google.common.util.concurrent.AbstractFuture$SafeAtomicHelper               : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$LocalLoadingCache                            : loaded 2 times (x 130B)
Class com.google.common.base.AbstractIterator$State                                   : loaded 2 times (x 75B)
Class com.amazon.ion.impl.lite.IonBoolLite                                            : loaded 2 times (x 174B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHash$NULL                          : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt___CollectionsKt$asSequence$$inlined$Sequence$1 : loaded 2 times (x 71B)
Class org.gradle.internal.service.UnknownServiceException                             : loaded 2 times (x 79B)
Class com.google.common.collect.Sets$4                                                : loaded 2 times (x 135B)
Class org.objectweb.asm.ModuleVisitor                                                 : loaded 2 times (x 77B)
Class com.google.common.cache.LocalCache$Strength$1                                   : loaded 2 times (x 77B)
Class org.gradle.internal.impldep.gnu.trove.PrimeFinder                               : loaded 2 times (x 67B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.kapt.KaptGradleModel              : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinDslScriptAdditionalTask           : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ClasspathEntryModel                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.SettableFuture                                : loaded 2 times (x 110B)
Class com.google.common.cache.LocalCache$Strength$2                                   : loaded 2 times (x 77B)
Class com.amazon.ion.IonTimestamp                                                     : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider$PooledBlockAllocator       : loaded 2 times (x 77B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService: loaded 2 times (x 73B)
Class org.gradle.api.JavaVersion                                                      : loaded 2 times (x 75B)
Class org.gradle.tooling.model.Dependency                                             : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$Strength$3                                   : loaded 2 times (x 77B)
Class com.google.common.collect.MapDifference                                         : loaded 2 times (x 66B)
Class com.google.common.base.Splitter$1                                               : loaded 2 times (x 73B)
Class com.amazon.ion.IonDatagram                                                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingStack$ElementFactory                      : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1$1   : loaded 2 times (x 74B)
Class com.amazon.ion.impl._Private_ByteTransferSink                                   : loaded 2 times (x 66B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.ResolutionState  : loaded 2 times (x 75B)
Class kotlin.collections.ArraysKt___ArraysJvmKt                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__CollectionsKt                                 : loaded 2 times (x 67B)
Class org.objectweb.asm.FieldWriter                                                   : loaded 2 times (x 74B)
Class com.google.common.cache.CacheBuilder$NullListener                               : loaded 2 times (x 78B)
Class org.jetbrains.plugins.gradle.model.ExternalLibraryDependency                    : loaded 2 times (x 66B)
Class kotlin.text.Regex$Companion                                                     : loaded 2 times (x 67B)
Class org.gradle.internal.classpath.TransformedClassPath                              : loaded 2 times (x 92B)
Class org.gradle.internal.classloader.InstrumentingClassLoader                        : loaded 2 times (x 66B)
Class com.google.common.cache.CacheLoader$FunctionToCacheLoader                       : loaded 2 times (x 71B)
Class com.google.common.collect.Iterators$ArrayItr                                    : loaded 2 times (x 93B)
Class com.google.common.collect.ImmutableCollection$Builder                           : loaded 2 times (x 72B)
Class com.amazon.ion.IonValue                                                         : loaded 2 times (x 66B)
Class kotlin.jvm.internal.FunctionReference                                           : loaded 2 times (x 118B)
Class [Lkotlin.coroutines.intrinsics.CoroutineSingletons;                             : loaded 2 times (x 65B)
Class kotlin.jvm.internal.FunctionBase                                                : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntriesList                                                    : loaded 2 times (x 219B)
Class com.google.common.base.Joiner                                                   : loaded 2 times (x 75B)
Class com.google.common.base.MoreObjects                                              : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerType                        : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonTextWriterBuilder$Mutable                       : loaded 2 times (x 98B)
Class kotlin.collections.CollectionsKt__IteratorsJVMKt                                : loaded 2 times (x 67B)
Class com.google.common.collect.Platform                                              : loaded 2 times (x 67B)
Class [Lorg.objectweb.asm.Type;                                                       : loaded 2 times (x 65B)
Class com.google.common.collect.UnmodifiableIterator                                  : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.model.IntelliJSettings                             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinSourceSetContainer                : loaded 2 times (x 66B)
Class kotlin.Result                                                                   : loaded 2 times (x 68B)
Class org.gradle.internal.service.ServiceLocator                                      : loaded 2 times (x 66B)
Class com.google.common.base.Ascii                                                    : loaded 2 times (x 67B)
Class com.google.common.collect.SingletonImmutableBiMap                               : loaded 2 times (x 139B)
Class com.google.common.cache.LocalCache$1                                            : loaded 2 times (x 85B)
Class com.google.common.collect.AbstractMapEntry                                      : loaded 2 times (x 77B)
Class com.amazon.ion.impl.IonWriterSystem                                             : loaded 2 times (x 167B)
Class com.amazon.ion.impl.bin.Symbols$2                                               : loaded 2 times (x 103B)
Class com.amazon.ion.impl.bin._Private_IonManagedWriter                               : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.android.synthetic.idea.AndroidExtensionsGradleModel        : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.util.GradleVersionUtil                     : loaded 2 times (x 67B)
Class kotlin.sequences.TransformingSequence$iterator$1                                : loaded 2 times (x 75B)
Class kotlin.Pair                                                                     : loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$SupportedPropertyInvoker: loaded 2 times (x 72B)
Class org.gradle.tooling.model.UnsupportedMethodException                             : loaded 2 times (x 78B)
Class com.google.common.collect.Sets$ImprovedAbstractSet                              : loaded 2 times (x 131B)
Class com.google.common.collect.ImmutableList$Builder                                 : loaded 2 times (x 73B)
Class com.google.common.collect.AbstractMultimap                                      : loaded 2 times (x 119B)
Class com.google.common.cache.LocalCache$2                                            : loaded 2 times (x 138B)
Class com.amazon.ion.impl.lite.IonContainerLite                                       : loaded 2 times (x 244B)
Class com.amazon.ion.IonBufferConfiguration$OversizedSymbolTableHandler               : loaded 2 times (x 66B)
Class com.amazon.ion.system.IonWriterBuilder                                          : loaded 2 times (x 70B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ToolingStreamApiUtils        : loaded 2 times (x 67B)
Class com.amazon.ion.facet.Faceted                                                    : loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt__MutableCollectionsKt                          : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper$1           : loaded 2 times (x 72B)
Class com.google.common.collect.RegularImmutableAsList                                : loaded 2 times (x 213B)
Class com.amazon.ion.impl.bin.utf8.Poolable                                           : loaded 2 times (x 75B)
Class com.amazon.ion.IonSymbol                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.THash                                     : loaded 2 times (x 77B)
Class kotlin.reflect.KCallable                                                        : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Trusted                        : loaded 2 times (x 66B)
Class com.google.common.collect.Iterators                                             : loaded 2 times (x 67B)
Class com.amazon.ion.impl.lite.IonStructLite                                          : loaded 2 times (x 294B)
Class com.amazon.ion.impl._Private_ReaderWriter                                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.AbstractSymbolTable                                     : loaded 2 times (x 103B)
Class com.amazon.ion.Decimal                                                          : loaded 2 times (x 129B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode$1  : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService: loaded 2 times (x 73B)
Class org.gradle.tooling.internal.adapter.CollectionMapper                            : loaded 2 times (x 69B)
Class com.google.common.util.concurrent.UncheckedExecutionException                   : loaded 2 times (x 78B)
Class com.google.common.collect.Lists$StringAsImmutableList                           : loaded 2 times (x 203B)
Class com.amazon.ion.impl.lite.IonFloatLite                                           : loaded 2 times (x 182B)
Class com.amazon.ion.impl._Private_IonTextWriterBuilder                               : loaded 2 times (x 98B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode$2  : loaded 2 times (x 76B)
Class org.gradle.internal.impldep.gnu.trove.Equality                                  : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.ExternalDependencyId                               : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingStack                                     : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonReaderBuilder                                   : loaded 2 times (x 91B)
Class com.amazon.ion.IonMutableCatalog                                                : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter                      : loaded 2 times (x 76B)
Class org.gradle.internal.InternalTransformer                                         : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet$1                     : loaded 2 times (x 78B)
Class org.objectweb.asm.ClassWriter                                                   : loaded 2 times (x 102B)
Class com.google.common.cache.CacheLoader$UnsupportedLoadingOperationException        : loaded 2 times (x 78B)
Class com.google.common.base.CharMatcher$And                                          : loaded 2 times (x 108B)
Class org.gradle.internal.impldep.gnu.trove.TObjectCanonicalHashingStrategy           : loaded 2 times (x 76B)
Class kotlin.collections.MapsKt__MapsJVMKt                                            : loaded 2 times (x 67B)
Class kotlin.collections.EmptyList                                                    : loaded 2 times (x 170B)
Class org.gradle.internal.time.DefaultTimer                                           : loaded 2 times (x 76B)
Class org.gradle.internal.classloader.ClasspathUtil$1                                 : loaded 2 times (x 72B)
Class com.google.common.base.Objects                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.FieldVisitor                                                  : loaded 2 times (x 73B)
Class com.google.common.collect.IndexedImmutableSet                                   : loaded 2 times (x 148B)
Class com.google.common.collect.ImmutableMap$1                                        : loaded 2 times (x 77B)
Class com.google.common.collect.BiMap                                                 : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider$PooledBlockAllocator$1     : loaded 2 times (x 75B)
Class com.amazon.ion.impl.lite.IonStringLite                                          : loaded 2 times (x 208B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewKey              : loaded 2 times (x 68B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry$DefaultModule           : loaded 2 times (x 82B)
Class com.google.common.primitives.Ints                                               : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIterator                                      : loaded 2 times (x 78B)
Class com.amazon.ion.impl.bin.Block                                                   : loaded 2 times (x 75B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.lombok.LombokModel                : loaded 2 times (x 66B)
Class kotlin.ResultKt                                                                 : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$PropertyCachingMethodInvoker: loaded 2 times (x 72B)
Class com.android.builder.model.v2.models.ProjectSyncIssues                           : loaded 2 times (x 66B)
Class com.google.common.collect.Multiset                                              : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimapGwtSerializationDependencies         : loaded 2 times (x 168B)
Class com.google.common.math.MathPreconditions                                        : loaded 2 times (x 67B)
Class com.google.common.collect.ObjectArrays                                          : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractIndexedListIterator                           : loaded 2 times (x 92B)
Class org.jetbrains.plugins.gradle.model.ExternalTask                                 : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoderPool$1                            : loaded 2 times (x 72B)
Class com.amazon.ion.impl.lite.IonLoaderLite                                          : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite.IonContext                                             : loaded 2 times (x 66B)
Class com.amazon.ion.impl.IonWriterSystemText                                         : loaded 2 times (x 186B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode;    : loaded 2 times (x 65B)
Class com.amazon.ion.ValueFactory                                                     : loaded 2 times (x 66B)
Class kotlin.collections.ArraysKt__ArraysKt                                           : loaded 2 times (x 67B)
Class kotlin.Result$Failure                                                           : loaded 2 times (x 68B)
Class org.gradle.internal.time.DefaultCountdownTimer                                  : loaded 2 times (x 84B)
Class com.google.common.collect.ImmutableMapEntrySet                                  : loaded 2 times (x 148B)
Class com.amazon.ion.impl._Private_IonReaderBuilder$TwoElementSequenceInputStream     : loaded 2 times (x 88B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolResolver                   : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_SymbolToken                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode    : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.util.ObjectCollector                       : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.GradleTaskModel                              : loaded 2 times (x 66B)
Class kotlin.collections.SetsKt__SetsJVMKt                                            : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$AdaptingMethodInvoker: loaded 2 times (x 72B)
Class org.gradle.tooling.internal.gradle.DefaultBuildIdentifier                       : loaded 2 times (x 75B)
Class kotlin.Function                                                                 : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.Module                                        : loaded 2 times (x 66B)
Class com.google.common.cache.Weigher                                                 : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_IonReaderBuilder$Mutable                           : loaded 2 times (x 91B)
Class com.intellij.openapi.externalSystem.model.project.IExternalSystemSourceType     : loaded 2 times (x 66B)
Class org.gradle.api.internal.classpath.UnknownModuleException                        : loaded 2 times (x 78B)
Class com.google.common.base.Stopwatch                                                : loaded 2 times (x 68B)
Class com.google.common.cache.LocalCache$Strength                                     : loaded 2 times (x 77B)
Class com.google.common.cache.Cache                                                   : loaded 2 times (x 66B)
Class settings_cqr77nl3gl6lg9b2yujwf1q4o$_run_closure1                                : loaded 2 times (x 135B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ProjectDependenciesSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIdentityHashingStrategy            : loaded 2 times (x 74B)
Class com.intellij.openapi.externalSystem.model.project.dependencies.ProjectDependencies: loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Cancellation                   : loaded 2 times (x 68B)
Class com.google.common.collect.ImmutableMap$Builder                                  : loaded 2 times (x 78B)
Class com.google.common.base.NullnessCasts                                            : loaded 2 times (x 67B)
Class com.google.common.cache.RemovalListener                                         : loaded 2 times (x 66B)
Class settings_cqr77nl3gl6lg9b2yujwf1q4o$_run_closure1$_closure2                      : loaded 2 times (x 135B)
Class com.amazon.ion.IonBufferConfiguration                                           : loaded 2 times (x 69B)
Class com.amazon.ion.impl.bin.Symbols                                                 : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService: loaded 2 times (x 78B)
Class com.android.ide.gradle.model.artifacts.AdditionalClassifierArtifactsModel       : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.gradle.GradleProjectIdentity                        : loaded 2 times (x 66B)
Class org.gradle.api.internal.DefaultClassPathRegistry                                : loaded 2 times (x 72B)
Class com.google.common.primitives.IntsMethodsForWeb                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Multimap                                              : loaded 2 times (x 66B)
Class settings_cqr77nl3gl6lg9b2yujwf1q4o                                              : loaded 2 times (x 175B)
Class [Lcom.amazon.ion.impl.bin.AbstractIonWriter$WriteValueOptimization;             : loaded 2 times (x 65B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.api.internal.ClassPathRegistry                                       : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList              : loaded 2 times (x 195B)
Class com.google.common.base.ExtraObjectsMethodsForWeb                                : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState                        : loaded 2 times (x 81B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.adapter.InternalBuildIdentifier: loaded 2 times (x 71B)
Class com.amazon.ion.impl._Private_IonWriter                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder                  : loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService$WriteContext: loaded 2 times (x 69B)
Class com.android.builder.model.v2.models.AndroidProject                              : loaded 2 times (x 66B)
Class com.android.builder.model.v2.AndroidModel                                       : loaded 2 times (x 66B)
Class com.google.common.cache.CacheBuilder                                            : loaded 2 times (x 68B)
Class com.google.common.collect.SingletonImmutableList                                : loaded 2 times (x 203B)
Class [Lcom.amazon.ion.SymbolToken;                                                   : loaded 2 times (x 65B)
Class kotlin.collections.SetsKt                                                       : loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.internal.classpath.ClassPath                                         : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableList$ReverseImmutableList                    : loaded 2 times (x 205B)
Class com.google.common.base.JdkPattern                                               : loaded 2 times (x 71B)
Class org.jetbrains.plugins.gradle.model.ExternalProjectDependency                    : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState$1                      : loaded 2 times (x 81B)
Class kotlin.reflect.KFunction                                                        : loaded 2 times (x 66B)
Class kotlin.collections.ArrayDeque                                                   : loaded 2 times (x 203B)
Class org.objectweb.asm.SymbolTable$Entry                                             : loaded 2 times (x 70B)
Class com.google.common.util.concurrent.AbstractFuture$AtomicHelper                   : loaded 2 times (x 74B)
Class com.amazon.ion.system.IonSystemBuilder$Mutable                                  : loaded 2 times (x 71B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolResolverBuilder            : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService$WriteContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinGradleModel                       : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt__MapsKt                                               : loaded 2 times (x 67B)
Class org.objectweb.asm.Edge                                                          : loaded 2 times (x 68B)
Class com.google.common.base.Suppliers$SupplierOfInstance                             : loaded 2 times (x 75B)
Class com.google.common.collect.Iterators$1                                           : loaded 2 times (x 77B)
Class com.amazon.ion.impl._Private_RecyclingStack$Recycler                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$LocalSymbolTableView             : loaded 2 times (x 103B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ThrowingRunnable                     : loaded 2 times (x 66B)
Class org.gradle.tooling.model.Model                                                  : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$ComputingValueReference                      : loaded 2 times (x 92B)
Class com.google.common.collect.SingletonImmutableSet                                 : loaded 2 times (x 142B)
Class com.google.common.collect.ImmutableMap$IteratorBasedImmutableMap                : loaded 2 times (x 122B)
Class com.amazon.ion.UnknownSymbolException                                           : loaded 2 times (x 82B)
Class com.android.ide.gradle.model.composites.BuildMap                                : loaded 2 times (x 66B)
Class kotlin.Metadata                                                                 : loaded 2 times (x 66B)
Class kotlin.UninitializedPropertyAccessException                                     : loaded 2 times (x 78B)
Class com.android.builder.model.v2.models.Versions                                    : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$Segment                                      : loaded 2 times (x 150B)
Class org.jetbrains.plugins.gradle.model.IntelliJProjectSettings                      : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.annotation.AnnotationBasedPluginModel: loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MethodInvocationCache: loaded 2 times (x 69B)
Class com.android.builder.model.v2.models.ndk.NativeModule                            : loaded 2 times (x 66B)
Class com.google.common.collect.AbstractListMultimap                                  : loaded 2 times (x 168B)
Class [Lorg.objectweb.asm.SymbolTable$Entry;                                          : loaded 2 times (x 65B)
Class com.google.common.cache.CacheLoader                                             : loaded 2 times (x 70B)
Class com.google.common.collect.Iterators$4                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableBiMap                                        : loaded 2 times (x 139B)
Class org.jetbrains.plugins.gradle.model.ExternalDependency                           : loaded 2 times (x 66B)
Class com.intellij.util.containers.IntObjectHashMap                                   : loaded 2 times (x 68B)
Class org.gradle.internal.installation.GradleInstallation$1                           : loaded 2 times (x 71B)
Class org.gradle.api.internal.classpath.ModuleRegistry                                : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.internal.InternalFutureFailureAccess          : loaded 2 times (x 68B)
Class com.google.common.collect.Iterators$5                                           : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$JdkBackedSetBuilderImpl                  : loaded 2 times (x 72B)
Class com.google.common.base.Splitter$SplittingIterator                               : loaded 2 times (x 80B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$1                    : loaded 2 times (x 76B)
Class com.amazon.ion.impl.LocalSymbolTableAsStruct$Factory                            : loaded 2 times (x 75B)
Class [Lcom.amazon.ion.IonType;                                                       : loaded 2 times (x 65B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$SourceSetDependencyModelReadContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyModel.DependencyReadContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.model.VersionCatalogsModel                         : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ObjectGraphAdapter                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$UnsafeAtomicHelper             : loaded 2 times (x 74B)
Class com.google.common.cache.LocalCache$LocalManualCache$1                           : loaded 2 times (x 71B)
Class com.google.common.base.CharMatcher$ForPredicate                                 : loaded 2 times (x 108B)
Class org.jetbrains.plugins.gradle.model.ProjectImportModelProvider                   : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.ExternalSourceSet                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$2                    : loaded 2 times (x 76B)
Class com.amazon.ion.IonNull                                                          : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.warmUp.GradleTaskWarmUpRequest  : loaded 2 times (x 66B)
Class kotlin.text.RegexKt                                                             : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsKt                         : loaded 2 times (x 67B)
Class com.google.common.collect.ImmutableCollection                                   : loaded 2 times (x 121B)
Class com.google.common.base.CharMatcher$AnyOf                                        : loaded 2 times (x 108B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerInfo                        : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$3                    : loaded 2 times (x 76B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$StreamFlushMode                      : loaded 2 times (x 75B)
Class com.amazon.ion.IonBinaryWriter                                                  : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetArtifactIndex.GradleSourceSetArtifactBuildRequest: loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt__CollectionsJVMKt                              : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.DefaultModuleRegistry                         : loaded 2 times (x 82B)
Class com.google.common.util.concurrent.ListenableFuture                              : loaded 2 times (x 66B)
Class com.google.common.cache.ReferenceEntry                                          : loaded 2 times (x 66B)
Class com.google.common.base.Converter                                                : loaded 2 times (x 86B)
Class com.google.common.collect.ImmutableSet$CachingAsList                            : loaded 2 times (x 144B)
Class com.google.common.collect.Lists                                                 : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState$4                    : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$1: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.model.ExternalProject                              : loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt__IteratorsKt                                   : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt                                                    : loaded 2 times (x 67B)
Class com.google.common.collect.AbstractMapBasedMultimap                              : loaded 2 times (x 135B)
Class com.google.common.util.concurrent.AbstractFuture                                : loaded 2 times (x 102B)
Class com.google.common.base.FunctionalEquivalence                                    : loaded 2 times (x 79B)
Class com.google.common.collect.Iterators$9                                           : loaded 2 times (x 77B)
Class com.google.common.collect.ImmutableAsList                                       : loaded 2 times (x 205B)
Class com.amazon.ion.impl.bin.utf8.Pool                                               : loaded 2 times (x 70B)
Class com.amazon.ion.impl._Private_SymtabExtendsCache                                 : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$2: loaded 2 times (x 79B)
Class org.objectweb.asm.RecordComponentVisitor                                        : loaded 2 times (x 73B)
Class com.google.common.cache.CacheLoader$SupplierToCacheLoader                       : loaded 2 times (x 71B)
Class com.amazon.ion.impl._Private_IonSymbol                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonValueLite                                           : loaded 2 times (x 143B)
Class com.amazon.ion.IonContainer                                                     : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.PooledBlockAllocatorProvider                            : loaded 2 times (x 69B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$3: loaded 2 times (x 79B)
Class kotlin.coroutines.jvm.internal.RestrictedContinuationImpl                       : loaded 2 times (x 83B)
Class com.google.common.collect.Maps$EntryTransformer                                 : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.AndroidAwareGradleModelProvider         : loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$4: loaded 2 times (x 79B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$Companion: loaded 2 times (x 67B)
Class kotlin.jvm.internal.markers.KMutableList                                        : loaded 2 times (x 66B)
Class org.gradle.internal.time.MonotonicClock                                         : loaded 2 times (x 73B)
Class org.gradle.internal.service.ServiceLookupException                              : loaded 2 times (x 78B)
Class [Lcom.google.common.cache.LocalCache$EntryFactory;                              : loaded 2 times (x 65B)
Class com.amazon.ion.BufferConfiguration$Builder                                      : loaded 2 times (x 74B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$5: loaded 2 times (x 79B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService$SourceSetDependencyModelWriteContext: loaded 2 times (x 68B)
Class org.gradle.internal.classloader.ClassLoaderFactory                              : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LocalManualCache                             : loaded 2 times (x 95B)
Class com.google.common.base.CharMatcher$IsNot                                        : loaded 2 times (x 107B)
Class com.google.common.base.CommonPattern                                            : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.IntList                                                 : loaded 2 times (x 73B)
Class com.amazon.ion.impl._Private_RecyclingQueue$ElementIterator                     : loaded 2 times (x 78B)
Class com.amazon.ion.impl.lite._Private_LiteDomTrampoline                             : loaded 2 times (x 67B)
Class com.amazon.ion.IonType                                                          : loaded 2 times (x 75B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$6: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap                          : loaded 2 times (x 69B)
Class org.gradle.internal.service.CachingServiceLocator                               : loaded 2 times (x 78B)
Class com.google.common.collect.AbstractMapBasedMultimap$KeySet                       : loaded 2 times (x 133B)
Class com.google.common.math.IntMath                                                  : loaded 2 times (x 67B)
Class com.amazon.ion.impl.lite.ValueFactoryLite                                       : loaded 2 times (x 214B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$7: loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.model.RepositoryModels                             : loaded 2 times (x 66B)
Class kotlin.jvm.internal.CallableReference                                           : loaded 2 times (x 102B)
Class com.google.common.collect.ImmutableMapEntry                                     : loaded 2 times (x 81B)
Class com.amazon.ion.impl.bin.WriteBuffer                                             : loaded 2 times (x 74B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter                                  : loaded 2 times (x 162B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext$8: loaded 2 times (x 79B)
Class [Lorg.gradle.api.JavaVersion;                                                   : loaded 2 times (x 65B)
Class org.gradle.tooling.model.idea.IdeaDependencyScope                               : loaded 2 times (x 66B)
Class org.gradle.internal.time.TimeSource                                             : loaded 2 times (x 66B)
Class org.gradle.tooling.model.kotlin.dsl.KotlinDslScriptsModel                       : loaded 2 times (x 66B)
Class com.google.common.base.Equivalence                                              : loaded 2 times (x 78B)
Class com.google.common.base.Function                                                 : loaded 2 times (x 66B)
Class com.google.common.collect.ImmutableMapEntry$NonTerminalImmutableMapEntry        : loaded 2 times (x 81B)
Class com.amazon.ion.impl.lite.IonBlobLite                                            : loaded 2 times (x 215B)
Class com.amazon.ion.impl.LocalSymbolTable                                            : loaded 2 times (x 117B)
Class org.jetbrains.plugins.gradle.tooling.serialization.SerializationService         : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.gradle.GradleBuildIdentity                          : loaded 2 times (x 66B)
Class kotlin.enums.EnumEntries                                                        : loaded 2 times (x 66B)
Class com.google.common.collect.IndexedImmutableSet$1                                 : loaded 2 times (x 209B)
Class com.google.common.cache.CacheBuilder$OneWeigher                                 : loaded 2 times (x 78B)
Class com.google.common.collect.RegularImmutableList                                  : loaded 2 times (x 209B)
Class com.amazon.ion.IonList                                                          : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.adapter.Supplier    : loaded 2 times (x 66B)
Class ijInit1_cxra44aza6j7soyehk88aj27j                                               : loaded 2 times (x 175B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MixInMappingAction   : loaded 2 times (x 76B)
Class org.gradle.internal.exceptions.ResolutionProvider                               : loaded 2 times (x 66B)
Class com.amazon.ion.IonSystem                                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolContext            : loaded 2 times (x 68B)
Class org.gradle.tooling.model.idea.IdeaCompilerOutput                                : loaded 2 times (x 66B)
Class org.gradle.tooling.model.ProjectIdentifier                                      : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.ClasspathUtil                                   : loaded 2 times (x 67B)
Class build_eoun890h7qlxwfhq6ek4y6yum                                                 : loaded 2 times (x 176B)
Class com.google.common.base.Charsets                                                 : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.Uninterruptibles                              : loaded 2 times (x 67B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService: loaded 2 times (x 73B)
Class com.google.common.collect.Maps                                                  : loaded 2 times (x 67B)
Class com.amazon.ion.impl.lite.IonIntLite                                             : loaded 2 times (x 185B)
Class com.amazon.ion.util._Private_FastAppendable                                     : loaded 2 times (x 66B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode;                 : loaded 2 times (x 65B)
Class org.gradle.internal.time.TimeSource$1                                           : loaded 2 times (x 73B)
Class kotlin.jvm.internal.DefaultConstructorMarker                                    : loaded 2 times (x 67B)
Class org.gradle.api.specs.Spec                                                       : loaded 2 times (x 66B)
Class [Lcom.google.common.collect.ImmutableEntry;                                     : loaded 2 times (x 65B)
Class com.amazon.ion.impl.lite.IonSexpLite                                            : loaded 2 times (x 486B)
Class com.amazon.ion.impl.lite.IonListLite                                            : loaded 2 times (x 486B)
Class com.amazon.ion.IonFloat                                                         : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$LoadingValueReference                        : loaded 2 times (x 92B)
Class com.amazon.ion.impl.LocalSymbolTable$Factory                                    : loaded 2 times (x 73B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode       : loaded 2 times (x 76B)
Class com.intellij.gradle.toolingExtension.impl.model.buildScriptClasspathModel.GradleBuildScriptClasspathSerializationService: loaded 2 times (x 73B)
Class sync_studio_tooling7_8cximjnbjani6zst72kn79uiy                                  : loaded 2 times (x 175B)
Class com.google.common.base.Platform                                                 : loaded 2 times (x 67B)
Class com.amazon.ion.impl.LocalSymbolTableAsStruct                                    : loaded 2 times (x 122B)
Class com.amazon.ion.impl.lite.IonTimestampLite                                       : loaded 2 times (x 188B)
Class org.jetbrains.plugins.gradle.model.FileCollectionDependency                     : loaded 2 times (x 66B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHashingStrategy                    : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.utilDummyModel.DummyModel       : loaded 2 times (x 66B)
Class org.gradle.internal.time.Clock                                                  : loaded 2 times (x 66B)
Class com.google.common.collect.Maps$8                                                : loaded 2 times (x 78B)
Class com.amazon.ion.IonString                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleSourceSetDependencyModel               : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt__SequencesJVMKt                                    : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.RestrictedSuspendLambda                          : loaded 2 times (x 87B)
Class org.gradle.tooling.internal.adapter.TypeInspector                               : loaded 2 times (x 69B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$NoOpDecoration       : loaded 2 times (x 75B)
Class org.gradle.tooling.model.Element                                                : loaded 2 times (x 66B)
Class kotlin.collections.AbstractList                                                 : loaded 2 times (x 193B)
Class kotlin.collections.AbstractCollection                                           : loaded 2 times (x 113B)
Class com.google.common.collect.Sets$SetView                                          : loaded 2 times (x 134B)
Class com.google.common.cache.LocalCache$StrongValueReference                         : loaded 2 times (x 86B)
Class com.google.common.util.concurrent.AbstractFuture$SynchronizedHelper             : loaded 2 times (x 74B)
Class com.amazon.ion.BufferConfiguration                                              : loaded 2 times (x 69B)
Class com.amazon.ion.system.SimpleCatalog                                             : loaded 2 times (x 87B)
Class com.intellij.gradle.toolingExtension.impl.model.taskModel.GradleTaskSerialisationService: loaded 2 times (x 72B)
Class com.android.ide.gradle.model.LegacyV1AgpVersionModel                            : loaded 2 times (x 66B)
Class kotlin.text.Regex                                                               : loaded 2 times (x 68B)
Class kotlin.sequences.SequencesKt__SequenceBuilderKt$sequence$$inlined$Sequence$1    : loaded 2 times (x 71B)
Class org.objectweb.asm.Type                                                          : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.util.IntObjectMap$1                        : loaded 2 times (x 73B)
Class org.gradle.internal.impldep.com.google.common.base.Preconditions                : loaded 2 times (x 67B)
Class org.gradle.tooling.model.internal.ImmutableDomainObjectSet                      : loaded 2 times (x 151B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$InstrumentingVisitableURLClassLoader: loaded 2 times (x 119B)
Class org.objectweb.asm.ByteVector                                                    : loaded 2 times (x 75B)
Class [Lcom.google.common.cache.CacheBuilder$NullListener;                            : loaded 2 times (x 65B)
Class com.google.common.math.IntMath$1                                                : loaded 2 times (x 67B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$StreamFlushMode;                   : loaded 2 times (x 65B)
Class com.amazon.ion.UnsupportedIonVersionException                                   : loaded 2 times (x 81B)
Class kotlin.ranges.RangesKt__RangesKt                                                : loaded 2 times (x 67B)
Class kotlin.collections.SetsKt___SetsKt                                              : loaded 2 times (x 67B)
Class kotlin.sequences.SequencesKt__SequencesKt                                       : loaded 2 times (x 67B)
Class kotlin.coroutines.Continuation                                                  : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.VisitableURLClassLoader$Spec                    : loaded 2 times (x 70B)
Class org.gradle.internal.classpath.DefaultClassPath                                  : loaded 2 times (x 86B)
Class org.objectweb.asm.Symbol                                                        : loaded 2 times (x 69B)
Class com.google.common.base.AbstractIterator                                         : loaded 2 times (x 76B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState                      : loaded 2 times (x 76B)
Class com.amazon.ion.IonStruct                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleBuildScriptClasspathModel              : loaded 2 times (x 66B)
Class kotlin.coroutines.jvm.internal.BaseContinuationImpl                             : loaded 2 times (x 83B)
Class kotlin.Result$Companion                                                         : loaded 2 times (x 67B)
Class org.gradle.util.internal.DefaultGradleVersion                                   : loaded 2 times (x 80B)
Class com.google.common.base.CharMatcher$Ascii                                        : loaded 2 times (x 108B)
Class com.google.common.collect.ImmutableSet                                          : loaded 2 times (x 141B)
Class com.google.common.collect.RegularImmutableMap$Values                            : loaded 2 times (x 203B)
Class com.google.common.base.CharMatcher$BitSetMatcher                                : loaded 2 times (x 108B)
Class org.gradle.internal.classloader.ClassLoaderSpec                                 : loaded 2 times (x 67B)
Class org.objectweb.asm.AnnotationWriter                                              : loaded 2 times (x 75B)
Class com.google.common.cache.CacheStats                                              : loaded 2 times (x 67B)
Class com.amazon.ion.impl.IonWriterSystemTextMarkup                                   : loaded 2 times (x 188B)
Class com.amazon.ion.impl.bin.BlockAllocator                                          : loaded 2 times (x 76B)
Class kotlin.jvm.internal.FunctionReferenceImpl                                       : loaded 2 times (x 118B)
Class kotlin.collections.CollectionsKt___CollectionsJvmKt                             : loaded 2 times (x 67B)
Class org.objectweb.asm.RecordComponentWriter                                         : loaded 2 times (x 74B)
Class com.google.common.util.concurrent.AbstractFuture$Listener                       : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$Or                                           : loaded 2 times (x 108B)
Class com.amazon.ion.impl._Private_RecyclingQueue                                     : loaded 2 times (x 76B)
Class com.amazon.ion.system.IonWriterBuilderBase                                      : loaded 2 times (x 79B)
Class kotlin.TuplesKt                                                                 : loaded 2 times (x 67B)
Class kotlin.jvm.internal.markers.KMutableIterable                                    : loaded 2 times (x 66B)
Class kotlin.sequences.SequencesKt___SequencesJvmKt                                   : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$DefaultViewBuilder   : loaded 2 times (x 76B)
Class kotlin.Unit                                                                     : loaded 2 times (x 67B)
Class org.objectweb.asm.Attribute                                                     : loaded 2 times (x 73B)
Class com.google.common.base.Supplier                                                 : loaded 2 times (x 66B)
Class kotlin.ranges.RangesKt                                                          : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewGraphDetails$1   : loaded 2 times (x 73B)
Class org.gradle.internal.classloader.VisitableURLClassLoader                         : loaded 2 times (x 113B)
Class com.google.common.util.concurrent.AbstractFuture$Waiter                         : loaded 2 times (x 68B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$1                                    : loaded 2 times (x 73B)
Class [Lcom.amazon.ion.SymbolTable;                                                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.IonBinaryWriterAdapter$Factory                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.BlockedBuffer$BufferedOutputStream                          : loaded 2 times (x 86B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService$ReadContext: loaded 2 times (x 68B)
Class kotlin.collections.ArraysUtilJVM                                                : loaded 2 times (x 67B)
Class kotlin.sequences.FlatteningSequence$iterator$1                                  : loaded 2 times (x 75B)
Class kotlin.jvm.functions.Function0                                                  : loaded 2 times (x 66B)
Class com.google.common.collect.RegularImmutableMap$KeySet                            : loaded 2 times (x 148B)
Class com.google.common.base.Equivalence$Identity                                     : loaded 2 times (x 78B)
Class org.gradle.tooling.model.idea.IdeaDependency                                    : loaded 2 times (x 66B)
Class com.google.common.base.Strings                                                  : loaded 2 times (x 67B)
Class com.google.common.collect.Sets                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.Handler                                                       : loaded 2 times (x 68B)
Class com.google.common.base.Ticker                                                   : loaded 2 times (x 68B)
Class com.google.common.base.CharMatcher$FastMatcher                                  : loaded 2 times (x 107B)
Class com.amazon.ion.impl.bin.AbstractIonWriter$WriteValueOptimization                : loaded 2 times (x 75B)
Class kotlin.collections.AbstractMutableList                                          : loaded 2 times (x 202B)
Class kotlin.jvm.functions.Function1                                                  : loaded 2 times (x 66B)
Class [Lcom.google.common.cache.LocalCache$Segment;                                   : loaded 2 times (x 65B)
Class [Lcom.google.common.cache.LocalCache$Strength;                                  : loaded 2 times (x 65B)
Class com.google.common.cache.CacheBuilder$1                                          : loaded 2 times (x 81B)
Class com.google.common.collect.ImmutableMap                                          : loaded 2 times (x 116B)
Class com.google.common.base.Predicate                                                : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.BlockAllocatorProvider                                  : loaded 2 times (x 68B)
Class org.jetbrains.plugins.gradle.tooling.serialization.internal.IdeaProjectSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetDependencyModel.GradleSourceSetDependencySerialisationService: loaded 2 times (x 73B)
Class org.jetbrains.plugins.gradle.model.GradleSourceSetModel                         : loaded 2 times (x 66B)
Class kotlin.jvm.functions.Function2                                                  : loaded 2 times (x 66B)
Class org.gradle.internal.classpath.DefaultClassPath$ImmutableUniqueList$Builder      : loaded 2 times (x 71B)
Class com.google.common.cache.LocalCache                                              : loaded 2 times (x 183B)
Class com.google.common.cache.CacheBuilder$2                                          : loaded 2 times (x 75B)
Class com.google.common.base.CharMatcher$NegatedFastMatcher                           : loaded 2 times (x 109B)
Class com.amazon.ion.impl.lite.IonSequenceLite                                        : loaded 2 times (x 428B)
Class com.amazon.ion.impl._Private_Utils$1                                            : loaded 2 times (x 85B)
Class com.amazon.ion.impl.SymbolTableAsStruct                                         : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt___MapsJvmKt                                           : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap$AbsentValueProvider     : loaded 2 times (x 66B)
Class com.google.common.collect.ArrayListMultimap                                     : loaded 2 times (x 168B)
Class com.google.common.cache.CacheBuilder$3                                          : loaded 2 times (x 68B)
Class [Lcom.google.common.collect.AbstractMapEntry;                                   : loaded 2 times (x 65B)
Class com.google.common.collect.ImmutableEntry                                        : loaded 2 times (x 78B)
Class com.amazon.ion.impl.bin.AbstractIonWriter                                       : loaded 2 times (x 157B)
Class org.jetbrains.plugins.gradle.tooling.util.ObjectCollector$Processor             : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.samWithReceiver.SamWithReceiverModel: loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt___CollectionsKt                                : loaded 2 times (x 67B)
Class kotlin.annotation.Target                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.KotlinDslScriptsModelSerializationService: loaded 2 times (x 77B)
Class org.gradle.internal.exceptions.NonGradleCauseExceptionsHolder                   : loaded 2 times (x 66B)
Class org.gradle.tooling.model.idea.IdeaModuleDependency                              : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ViewBuilder                                 : loaded 2 times (x 66B)
Class com.google.common.collect.CollectPreconditions                                  : loaded 2 times (x 67B)
Class com.amazon.ion.system.IonReaderBuilder                                          : loaded 2 times (x 89B)
Class org.jetbrains.plugins.gradle.model.AnnotationProcessingConfig                   : loaded 2 times (x 66B)
Class com.amazon.ion.IonReader                                                        : loaded 2 times (x 66B)
Class kotlin.reflect.KDeclarationContainer                                            : loaded 2 times (x 66B)
Class kotlin.annotation.Retention                                                     : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.Versions$Version                            : loaded 2 times (x 66B)
Class com.google.common.collect.SortedMapDifference                                   : loaded 2 times (x 66B)
Class com.amazon.ion.IonClob                                                          : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.ModelBuilderService$Parameter              : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt___MapsKt                                              : loaded 2 times (x 67B)
Class kotlin.collections.ArraysKt___ArraysKt                                          : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$WriteThroughEntry                            : loaded 2 times (x 75B)
Class com.google.common.cache.LocalCache$HashIterator                                 : loaded 2 times (x 82B)
Class com.google.common.cache.LocalCache$EntryFactory                                 : loaded 2 times (x 79B)
Class com.amazon.ion.impl.lite.IonLobLite                                             : loaded 2 times (x 180B)
Class com.amazon.ion.impl.lite.IonTextLite                                            : loaded 2 times (x 177B)
Class com.amazon.ion.IonNumber                                                        : loaded 2 times (x 66B)
Class org.gradle.internal.impldep.gnu.trove.TObjectIntProcedure                       : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt__MapWithDefaultKt                                     : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ReflectionMethodInvoker: loaded 2 times (x 72B)
Class com.google.common.cache.LocalCache$AbstractReferenceEntry                       : loaded 2 times (x 103B)
Class com.google.common.base.Splitter$1$1                                             : loaded 2 times (x 82B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$UserState;                     : loaded 2 times (x 65B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoder                                  : loaded 2 times (x 76B)
Class com.amazon.ion.IonBool                                                          : loaded 2 times (x 66B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.assignment.AssignmentModel        : loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt__MutableCollectionsJVMKt                       : loaded 2 times (x 67B)
Class com.google.common.base.PairwiseEquivalence                                      : loaded 2 times (x 79B)
Class kotlin.enums.EnumEntriesKt                                                      : loaded 2 times (x 67B)
Class com.android.builder.model.v2.models.VariantDependencies                         : loaded 2 times (x 66B)
Class org.objectweb.asm.SymbolTable                                                   : loaded 2 times (x 68B)
Class com.google.common.cache.CacheLoader$InvalidCacheLoadException                   : loaded 2 times (x 78B)
Class com.google.common.collect.ImmutableSet$RegularSetBuilderImpl                    : loaded 2 times (x 73B)
Class com.amazon.ion.IonLoader                                                        : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleProperty                               : loaded 2 times (x 66B)
Class org.gradle.internal.classloader.SystemClassLoaderSpec                           : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$StrongEntry                                  : loaded 2 times (x 104B)
Class com.google.common.util.concurrent.AbstractFuture$TrustedFuture                  : loaded 2 times (x 110B)
Class com.google.common.cache.LoadingCache                                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_Utils                                              : loaded 2 times (x 67B)
Class kotlin.collections.EmptyMap                                                     : loaded 2 times (x 105B)
Class kotlin.reflect.KAnnotatedElement                                                : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMutableCollection                                  : loaded 2 times (x 66B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt                                       : loaded 2 times (x 67B)
Class org.gradle.internal.exceptions.MultiCauseException                              : loaded 2 times (x 66B)
Class org.gradle.internal.time.CountdownTimer                                         : loaded 2 times (x 66B)
Class kotlin.jvm.internal.Intrinsics                                                  : loaded 2 times (x 67B)
Class org.objectweb.asm.MethodWriter                                                  : loaded 2 times (x 102B)
Class [Lcom.google.common.cache.Weigher;                                              : loaded 2 times (x 65B)
Class com.google.common.collect.RegularImmutableMap$BucketOverflowException           : loaded 2 times (x 78B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService$ReadContext: loaded 2 times (x 69B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyModel.DependencyWriteContext: loaded 2 times (x 68B)
Class org.jetbrains.kotlin.idea.gradleTooling.model.parcelize.ParcelizeGradleModel    : loaded 2 times (x 66B)
Class kotlin.collections.MapsKt                                                       : loaded 2 times (x 67B)
Class kotlin.collections.CollectionsKt__IterablesKt                                   : loaded 2 times (x 67B)
Class kotlin.coroutines.intrinsics.CoroutineSingletons                                : loaded 2 times (x 75B)
Class org.gradle.tooling.model.ProjectModel                                           : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.GradleConfiguration                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoderPool                              : loaded 2 times (x 70B)
Class com.amazon.ion.impl._Private_IonSystem                                          : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration$OversizedValueHandler                        : loaded 2 times (x 66B)
Class kotlin.sequences.SequenceBuilderIterator                                        : loaded 2 times (x 81B)
Class kotlin.KotlinNullPointerException                                               : loaded 2 times (x 79B)
Class com.google.common.collect.RegularImmutableBiMap                                 : loaded 2 times (x 145B)
Class com.google.common.cache.LocalCache$ValueReference                               : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher$Negated                                      : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$Is                                           : loaded 2 times (x 107B)
Class com.amazon.ion.IonDecimal                                                       : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.model.tests.ExternalTestsModel                     : loaded 2 times (x 66B)
Class kotlin.ranges.RangesKt___RangesKt                                               : loaded 2 times (x 67B)
Class org.gradle.tooling.model.idea.IdeaLanguageLevel                                 : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$SafeMethodInvoker    : loaded 2 times (x 72B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap$WeakKey                 : loaded 2 times (x 75B)
Class org.gradle.internal.exceptions.DefaultMultiCauseException                       : loaded 2 times (x 91B)
Class com.google.common.base.PatternCompiler                                          : loaded 2 times (x 66B)
Class com.google.common.util.concurrent.AbstractFuture$Failure                        : loaded 2 times (x 68B)
Class com.google.common.base.Equivalence$Equals                                       : loaded 2 times (x 78B)
Class com.google.common.collect.Hashing                                               : loaded 2 times (x 67B)
Class com.google.common.base.Preconditions                                            : loaded 2 times (x 67B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$StreamCloseMode                      : loaded 2 times (x 75B)
Class com.amazon.ion.impl._Private_IonBinaryWriterBuilder$Mutable                     : loaded 2 times (x 111B)
Class com.intellij.gradle.toolingExtension.impl.model.projectModel.GradleExternalProjectSerializationService: loaded 2 times (x 73B)
Class kotlin.collections.ArraysKt                                                     : loaded 2 times (x 67B)
Class kotlin.collections.ArrayDeque$Companion                                         : loaded 2 times (x 67B)
Class org.gradle.tooling.model.HierarchicalElement                                    : loaded 2 times (x 66B)
Class com.google.common.base.Joiner$1                                                 : loaded 2 times (x 76B)
Class org.objectweb.asm.Label                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.CurrentFrame                                                  : loaded 2 times (x 69B)
Class org.objectweb.asm.ClassTooLargeException                                        : loaded 2 times (x 79B)
Class com.google.common.collect.PeekingIterator                                       : loaded 2 times (x 66B)
Class com.google.common.base.CharMatcher                                              : loaded 2 times (x 107B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PatchPoint                           : loaded 2 times (x 69B)
Class com.amazon.ion.IonBlob                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.IonLob                                                           : loaded 2 times (x 66B)
Class com.amazon.ion.IonSequence                                                      : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.ExternalTestsSerializationService: loaded 2 times (x 73B)
Class kotlin.collections.SetsKt__SetsKt                                               : loaded 2 times (x 67B)
Class kotlin.coroutines.EmptyCoroutineContext                                         : loaded 2 times (x 73B)
Class org.gradle.internal.time.Time                                                   : loaded 2 times (x 67B)
Class com.android.builder.model.v2.models.ProjectGraph                                : loaded 2 times (x 66B)
Class com.google.common.base.Joiner$2                                                 : loaded 2 times (x 75B)
Class org.objectweb.asm.Frame                                                         : loaded 2 times (x 69B)
Class org.objectweb.asm.MethodVisitor                                                 : loaded 2 times (x 101B)
Class com.google.common.cache.LocalCache$EntryFactory$1                               : loaded 2 times (x 79B)
Class [Lcom.google.common.cache.RemovalListener;                                      : loaded 2 times (x 65B)
Class com.google.common.base.CharMatcher$1                                            : loaded 2 times (x 109B)
Class com.google.common.base.CharMatcher$NamedFastMatcher                             : loaded 2 times (x 108B)
Class com.amazon.ion.system.IonTextWriterBuilder                                      : loaded 2 times (x 94B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportedSymbolResolverMode$1$1$1 : loaded 2 times (x 72B)
Class com.amazon.ion.impl._Private_LocalSymbolTableFactory                            : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.AnnotationProcessingModelSerializationService: loaded 2 times (x 73B)
Class kotlin.collections.ArraysKt__ArraysJVMKt                                        : loaded 2 times (x 67B)
Class com.google.common.cache.LocalCache$EntryFactory$2                               : loaded 2 times (x 79B)
Class com.amazon.ion.impl.lite.IonNullLite                                            : loaded 2 times (x 171B)
Class [Lcom.intellij.openapi.externalSystem.model.project.dependencies.ResolutionState;: loaded 2 times (x 65B)
Class org.gradle.internal.impldep.gnu.trove.TObjectHash                               : loaded 2 times (x 90B)
Class org.jetbrains.plugins.gradle.model.DependencyAccessorsModel                     : loaded 2 times (x 66B)
Class kotlin.jvm.internal.CallableReference$NoReceiver                                : loaded 2 times (x 67B)
Class org.gradle.api.internal.classpath.GlobalCacheRootsProvider                      : loaded 2 times (x 66B)
Class org.objectweb.asm.ModuleWriter                                                  : loaded 2 times (x 78B)
Class com.google.common.cache.LocalCache$EntryFactory$3                               : loaded 2 times (x 79B)
Class com.google.common.base.Ticker$1                                                 : loaded 2 times (x 68B)
Class com.google.common.collect.Maps$BiMapConverter                                   : loaded 2 times (x 86B)
Class com.google.common.collect.ImmutableMapEntrySet$RegularEntrySet                  : loaded 2 times (x 148B)
Class com.amazon.ion.impl.lite.IonClobLite                                            : loaded 2 times (x 216B)
Class com.amazon.ion.impl._Private_ValueFactory                                       : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$1                  : loaded 2 times (x 78B)
Class [Lcom.amazon.ion.impl.bin._Private_IonManagedBinaryWriterBuilder$AllocatorMode; : loaded 2 times (x 65B)
Class org.jetbrains.kotlin.idea.gradleTooling.KotlinMPPGradleModel                    : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.dependencyDownloadPolicyModel.GradleDependencyDownloadPolicy: loaded 2 times (x 66B)
Class kotlin.NoWhenBranchMatchedException                                             : loaded 2 times (x 78B)
Class org.gradle.tooling.model.BuildIdentifier                                        : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$4                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$Builder                                  : loaded 2 times (x 81B)
Class com.google.common.base.CharMatcher$InRange                                      : loaded 2 times (x 107B)
Class com.amazon.ion.IonInt                                                           : loaded 2 times (x 66B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$2                  : loaded 2 times (x 78B)
Class org.jetbrains.kotlin.idea.gradleTooling.PrepareKotlinIdeImportTaskModel         : loaded 2 times (x 66B)
Class com.android.builder.model.v2.models.VariantDependenciesFlatList                 : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$5                               : loaded 2 times (x 79B)
Class com.amazon.ion.impl.bin.IonRawBinaryWriter$PreallocationMode$3                  : loaded 2 times (x 78B)
Class com.android.ide.gradle.model.GradlePluginModel                                  : loaded 2 times (x 66B)
Class org.gradle.tooling.model.build.JavaEnvironment                                  : loaded 2 times (x 66B)
Class com.google.common.cache.LocalCache$EntryFactory$6                               : loaded 2 times (x 79B)
Class org.jetbrains.plugins.gradle.model.GradleExtension                              : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.IonDecimalLite                                         : loaded 2 times (x 184B)
Class com.amazon.ion.IonBufferConfiguration$Builder                                   : loaded 2 times (x 74B)
Class org.gradle.internal.time.Timer                                                  : loaded 2 times (x 66B)
Class kotlin.jvm.internal.markers.KMappedMarker                                       : loaded 2 times (x 66B)
Class org.gradle.internal.installation.CurrentGradleInstallationLocator               : loaded 2 times (x 67B)
Class com.google.common.collect.Maps$KeySet                                           : loaded 2 times (x 133B)
Class com.google.common.cache.LocalCache$EntryFactory$7                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$EmptySetBuilderImpl                      : loaded 2 times (x 72B)
Class com.google.common.collect.ImmutableList                                         : loaded 2 times (x 202B)
Class [Lcom.amazon.ion.impl.bin.IonRawBinaryWriter$ContainerType;                     : loaded 2 times (x 65B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewGraphDetails     : loaded 2 times (x 68B)
Class org.gradle.tooling.model.idea.IdeaProject                                       : loaded 2 times (x 66B)
Class org.gradle.tooling.model.DomainObjectSet                                        : loaded 2 times (x 66B)
Class org.gradle.internal.service.DefaultServiceLocator                               : loaded 2 times (x 79B)
Class com.google.common.cache.LocalCache$EntryFactory$8                               : loaded 2 times (x 79B)
Class com.google.common.collect.ImmutableSet$SetBuilderImpl                           : loaded 2 times (x 72B)
Class com.amazon.ion.impl.bin.utf8.Utf8StringEncoder$Result                           : loaded 2 times (x 70B)
Class com.amazon.ion.impl.bin.utf8.Pool$Allocator                                     : loaded 2 times (x 66B)
Class com.amazon.ion.IonText                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.impl.SymbolTokenImpl                                             : loaded 2 times (x 77B)
Class com.amazon.ion.SubstituteSymbolTableException                                   : loaded 2 times (x 80B)
Class kotlin.coroutines.jvm.internal.SuspendFunction                                  : loaded 2 times (x 66B)
Class org.gradle.util.GradleVersion                                                   : loaded 2 times (x 79B)
Class org.gradle.internal.installation.CurrentGradleInstallation                      : loaded 2 times (x 69B)
Class com.google.common.cache.CacheLoader$1                                           : loaded 2 times (x 71B)
Class com.google.common.collect.ImmutableList$SubList                                 : loaded 2 times (x 205B)
Class com.amazon.ion.impl.bin.IonManagedBinaryWriter$ImportDescriptor                 : loaded 2 times (x 72B)
Class com.amazon.ion.SymbolTable                                                      : loaded 2 times (x 66B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.taskModel.GradleTaskSerialisationService$Companion: loaded 2 times (x 67B)
Class org.jetbrains.plugins.gradle.javaModel.JavaGradleManifestModel                  : loaded 2 times (x 66B)
Class kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt                      : loaded 2 times (x 67B)
Class kotlin.coroutines.jvm.internal.CoroutineStackFrame                              : loaded 2 times (x 66B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$MethodInvocationCache$MethodInvocationKey: loaded 2 times (x 69B)
Class org.gradle.util.internal.GUtil                                                  : loaded 2 times (x 67B)
Class com.google.common.util.concurrent.AbstractFuture$SetFuture                      : loaded 2 times (x 71B)
Class com.google.common.cache.AbstractCache$StatsCounter                              : loaded 2 times (x 66B)
Class [Lcom.amazon.ion.impl.bin.IonManagedBinaryWriter$SymbolState;                   : loaded 2 times (x 65B)
Class com.amazon.ion.impl._Private_IonWriterBase                                      : loaded 2 times (x 158B)
Class com.amazon.ion.system.IonSystemBuilder                                          : loaded 2 times (x 71B)
Class com.amazon.ion.IonCatalog                                                       : loaded 2 times (x 66B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$SourceSetModelReadContext: loaded 2 times (x 68B)
Class com.intellij.gradle.toolingExtension.impl.model.utilTurnOffDefaultTasksModel.TurnOffDefaultTasks: loaded 2 times (x 66B)
Class kotlin.collections.CollectionsKt                                                : loaded 2 times (x 67B)
Class com.google.common.collect.Iterators$MergingIterator                             : loaded 2 times (x 77B)
Class com.google.common.collect.RegularImmutableSet                                   : loaded 2 times (x 145B)
Class com.intellij.gradle.toolingExtension.impl.model.sourceSetModel.GradleSourceSetSerialisationService$SourceSetModelWriteContext: loaded 2 times (x 68B)
Class kotlin.coroutines.CoroutineContext                                              : loaded 2 times (x 66B)
Class kotlin.coroutines.jvm.internal.DebugProbesKt                                    : loaded 2 times (x 67B)
Class org.gradle.tooling.internal.adapter.WeakIdentityHashMap                         : loaded 2 times (x 72B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$1                    : loaded 2 times (x 71B)
Class org.gradle.tooling.internal.adapter.ProtocolToModelAdapter$ViewDecoration       : loaded 2 times (x 66B)
Class org.gradle.internal.installation.GradleInstallation                             : loaded 2 times (x 71B)
Class com.google.common.collect.Iterables                                             : loaded 2 times (x 67B)
Class com.google.common.base.Splitter$Strategy                                        : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_RecyclingQueue$Recycler                            : loaded 2 times (x 66B)
Class com.amazon.ion.impl.lite.ContainerlessContext                                   : loaded 2 times (x 76B)
Class org.jetbrains.plugins.gradle.tooling.serialization.GradleExtensionsSerializationService$ReadContext: loaded 2 times (x 68B)
Class org.gradle.tooling.internal.adapter.MethodInvoker                               : loaded 2 times (x 66B)
Class com.amazon.ion.UnexpectedEofException                                           : loaded 2 times (x 80B)
Class com.amazon.ion.IonSexp                                                          : loaded 2 times (x 66B)
Class com.amazon.ion.ReadOnlyValueException                                           : loaded 2 times (x 80B)
Class com.amazon.ion.impl._Private_LocalSymbolTable                                   : loaded 2 times (x 66B)
Class com.amazon.ion.BufferConfiguration$DataHandler                                  : loaded 2 times (x 66B)
Class com.amazon.ion.impl._Private_IonBinaryWriterBuilder                             : loaded 2 times (x 111B)
Class org.jetbrains.plugins.gradle.tooling.serialization.RepositoriesModelSerializationService$WriteContext: loaded 2 times (x 68B)
Class com.android.ide.gradle.model.LegacyAndroidGradlePluginProperties                : loaded 2 times (x 66B)
Class kotlin.jvm.KotlinReflectionNotSupportedError                                    : loaded 2 times (x 78B)
Class org.gradle.tooling.model.gradle.GradleScript                                    : loaded 2 times (x 66B)

---------------  S Y S T E M  ---------------

OS:
 Windows 10 , 64 bit Build 19041 (10.0.19041.5915)
OS uptime: 0 days 9:05 hours

CPU: total 8 (initial active 8) (8 cores per cpu, 2 threads per core) family 23 model 24 stepping 1 microcode 0x0, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4a, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, clmul, bmi1, bmi2, adx, sha, fma, vzeroupper, clflush, clflushopt, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 3700, Current Mhz: 3700, Mhz Limit: 3700

Memory: 4k page, system-wide physical 22476M (3902M free)
TotalPageFile size 22476M (AvailPageFile size 9M)
current process WorkingSet (physical memory assigned to process): 572M, peak: 572M
current process commit charge ("private bytes"): 605M, peak: 619M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+-13368085-b895.109) for windows-amd64 JRE (21.0.6+-13368085-b895.109), built on 2025-04-16T17:01:31Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
