package com.anginatech.textrepeater.models;

import com.google.gson.annotations.SerializedName;

/**
 * User Registration Request Model
 */
public class UserRegistrationRequest {
    
    @SerializedName("device_id")
    private String deviceId;
    
    @SerializedName("fcm_token")
    private String fcmToken;
    
    @SerializedName("app_version")
    private String appVersion;
    
    @SerializedName("android_version")
    private String androidVersion;
    
    @SerializedName("device_model")
    private String deviceModel;

    // Constructors
    public UserRegistrationRequest() {}

    public UserRegistrationRequest(String deviceId, String fcmToken, String appVersion, 
                                 String androidVersion, String deviceModel) {
        this.deviceId = deviceId;
        this.fcmToken = fcmToken;
        this.appVersion = appVersion;
        this.androidVersion = androidVersion;
        this.deviceModel = deviceModel;
    }

    // Getters and Setters
    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getFcmToken() {
        return fcmToken;
    }

    public void setFcmToken(String fcmToken) {
        this.fcmToken = fcmToken;
    }

    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public String getAndroidVersion() {
        return androidVersion;
    }

    public void setAndroidVersion(String androidVersion) {
        this.androidVersion = androidVersion;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }
}
