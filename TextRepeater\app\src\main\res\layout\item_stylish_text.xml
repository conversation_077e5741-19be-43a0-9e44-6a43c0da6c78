<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/mother_layout_color"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/itemLayoutStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:clickable="true"
        android:foreground="?attr/selectableItemBackground"
        >


        <TextView
            android:id="@+id/textView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="55dp"
            android:layout_marginEnd="16dp"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="start"
            android:marqueeRepeatLimit="marquee_forever"
            android:paddingTop="30dp"
            android:paddingBottom="30dp"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:text="Hello tadfjafafhafgbfsfgbsfsfasfafjhsasfhshfsahfhsahfasfasf"
            android:textColor="@color/primary_text"
            android:textSize="17sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <TextView
            android:id="@+id/textPositionStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/allerta"
            android:paddingTop="32dp"
            android:paddingBottom="32dp"
            android:text="1"
            android:textColor="@color/love"
            android:layout_marginStart="22dp"
            android:textSize="17sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/emoji_view_color"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.989"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>



</androidx.constraintlayout.widget.ConstraintLayout>

