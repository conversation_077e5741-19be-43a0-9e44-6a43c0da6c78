<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Decoration_Text_Activity"
    android:background="@color/mother_layout_color"
    >


    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/decoration_MaterialToolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:menu="@menu/settings_item"
        app:navigationIcon="@drawable/baseline_arrow_back_ios_24"
        app:navigationIconTint="@color/toolbar_text_color"
        app:title="@string/item_title5"
        app:titleTextColor="@color/toolbar_text_color" />





    <EditText
        android:id="@+id/decoration_editEnterText"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="30dp"
        android:layout_marginBottom="4dp"
        android:background="@drawable/edittext_backround"
        android:drawableStart="@drawable/baseline_text_fields_24"
        android:hint=" Enter Text"
        android:layout_marginTop="10dp"
        android:inputType="text"
        android:maxLength="300"
        android:paddingLeft="16dp"
        android:paddingEnd="10dp"
        android:textColor="@color/primary_text"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/decoration_MaterialToolbar"
        app:layout_constraintVertical_bias="0.0" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/decoration_RecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:clipToPadding="false"
        android:paddingBottom="120dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/decoration_editEnterText"
        />






</androidx.constraintlayout.widget.ConstraintLayout>