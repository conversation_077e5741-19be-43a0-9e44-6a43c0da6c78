<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    http_response_code(200);
    exit();
}

require_once '../config/database.php';
require_once '../classes/AdMobManager.php';
require_once '../classes/NotificationManager.php';

class API {
    private $conn;
    private $admobManager;
    private $notificationManager;
    
    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
        $this->admobManager = new AdMobManager();
        $this->notificationManager = new NotificationManager();
    }
    
    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        $path = $_GET['path'] ?? '';
        
        try {
            switch ($path) {
                case 'ads/config':
                    if ($method === 'GET') {
                        return $this->getAdConfig();
                    }
                    break;
                    
                case 'ads/track':
                    if ($method === 'POST') {
                        return $this->trackAdEvent();
                    }
                    break;
                    
                case 'user/register':
                    if ($method === 'POST') {
                        return $this->registerUser();
                    }
                    break;
                    
                case 'user/update':
                    if ($method === 'POST') {
                        return $this->updateUser();
                    }
                    break;
                    
                case 'app/settings':
                    if ($method === 'GET') {
                        return $this->getAppSettings();
                    }
                    break;
                    
                case 'notification/delivered':
                    if ($method === 'POST') {
                        return $this->markNotificationDelivered();
                    }
                    break;
                    
                case 'notification/clicked':
                    if ($method === 'POST') {
                        return $this->markNotificationClicked();
                    }
                    break;
                    
                default:
                    return $this->errorResponse('Endpoint not found', 404);
            }
        } catch (Exception $e) {
            return $this->errorResponse('Internal server error: ' . $e->getMessage(), 500);
        }
        
        return $this->errorResponse('Method not allowed', 405);
    }
    
    /**
     * Get AdMob configuration
     */
    private function getAdConfig() {
        $result = $this->admobManager->getApiConfig();
        
        if ($result['success']) {
            return $this->successResponse($result['data']);
        } else {
            return $this->errorResponse($result['message'], 404);
        }
    }
    
    /**
     * Track ad events (impressions, clicks, etc.)
     */
    private function trackAdEvent() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $required_fields = ['device_id', 'ad_type', 'event_type'];
        foreach ($required_fields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                return $this->errorResponse("Missing required field: $field", 400);
            }
        }
        
        try {
            $query = "INSERT INTO ad_analytics 
                     (device_id, ad_type, ad_unit_id, event_type, revenue, currency, session_id, app_version) 
                     VALUES 
                     (:device_id, :ad_type, :ad_unit_id, :event_type, :revenue, :currency, :session_id, :app_version)";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':device_id', $input['device_id']);
            $stmt->bindParam(':ad_type', $input['ad_type']);
            $stmt->bindParam(':ad_unit_id', $input['ad_unit_id'] ?? '');
            $stmt->bindParam(':event_type', $input['event_type']);
            $stmt->bindParam(':revenue', $input['revenue'] ?? 0);
            $stmt->bindParam(':currency', $input['currency'] ?? 'USD');
            $stmt->bindParam(':session_id', $input['session_id'] ?? '');
            $stmt->bindParam(':app_version', $input['app_version'] ?? '');
            
            if ($stmt->execute()) {
                return $this->successResponse(['message' => 'Event tracked successfully']);
            } else {
                return $this->errorResponse('Failed to track event', 500);
            }
        } catch (Exception $e) {
            return $this->errorResponse('Database error: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Register new user
     */
    private function registerUser() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['device_id']) || empty($input['device_id'])) {
            return $this->errorResponse('Device ID is required', 400);
        }
        
        try {
            // Check if user already exists
            $query = "SELECT id FROM app_users WHERE device_id = :device_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':device_id', $input['device_id']);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                // Update existing user
                $query = "UPDATE app_users SET 
                         fcm_token = :fcm_token,
                         app_version = :app_version,
                         android_version = :android_version,
                         device_model = :device_model,
                         last_seen = NOW(),
                         total_sessions = total_sessions + 1
                         WHERE device_id = :device_id";
            } else {
                // Insert new user
                $query = "INSERT INTO app_users 
                         (device_id, fcm_token, app_version, android_version, device_model) 
                         VALUES 
                         (:device_id, :fcm_token, :app_version, :android_version, :device_model)";
            }
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':device_id', $input['device_id']);
            $stmt->bindParam(':fcm_token', $input['fcm_token'] ?? '');
            $stmt->bindParam(':app_version', $input['app_version'] ?? '');
            $stmt->bindParam(':android_version', $input['android_version'] ?? '');
            $stmt->bindParam(':device_model', $input['device_model'] ?? '');
            
            if ($stmt->execute()) {
                return $this->successResponse(['message' => 'User registered successfully']);
            } else {
                return $this->errorResponse('Failed to register user', 500);
            }
        } catch (Exception $e) {
            return $this->errorResponse('Database error: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Update user information
     */
    private function updateUser() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!isset($input['device_id']) || empty($input['device_id'])) {
            return $this->errorResponse('Device ID is required', 400);
        }
        
        try {
            $query = "UPDATE app_users SET 
                     fcm_token = :fcm_token,
                     last_seen = NOW()
                     WHERE device_id = :device_id";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':device_id', $input['device_id']);
            $stmt->bindParam(':fcm_token', $input['fcm_token'] ?? '');
            
            if ($stmt->execute()) {
                return $this->successResponse(['message' => 'User updated successfully']);
            } else {
                return $this->errorResponse('Failed to update user', 500);
            }
        } catch (Exception $e) {
            return $this->errorResponse('Database error: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Get app settings
     */
    private function getAppSettings() {
        try {
            $query = "SELECT setting_key, setting_value, setting_type 
                     FROM app_settings 
                     WHERE is_public = 1";
            
            $stmt = $this->conn->prepare($query);
            $stmt->execute();
            $settings = $stmt->fetchAll();
            
            $result = [];
            foreach ($settings as $setting) {
                $value = $setting['setting_value'];
                
                // Convert value based on type
                switch ($setting['setting_type']) {
                    case 'boolean':
                        $value = filter_var($value, FILTER_VALIDATE_BOOLEAN);
                        break;
                    case 'integer':
                        $value = intval($value);
                        break;
                    case 'json':
                        $value = json_decode($value, true);
                        break;
                }
                
                $result[$setting['setting_key']] = $value;
            }
            
            return $this->successResponse($result);
        } catch (Exception $e) {
            return $this->errorResponse('Database error: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Mark notification as delivered
     */
    private function markNotificationDelivered() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $required_fields = ['notification_id', 'device_id'];
        foreach ($required_fields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                return $this->errorResponse("Missing required field: $field", 400);
            }
        }
        
        try {
            $query = "UPDATE notification_delivery SET 
                     status = 'delivered', 
                     delivered_at = NOW() 
                     WHERE notification_id = :notification_id AND device_id = :device_id";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':notification_id', $input['notification_id']);
            $stmt->bindParam(':device_id', $input['device_id']);
            
            if ($stmt->execute()) {
                return $this->successResponse(['message' => 'Delivery status updated']);
            } else {
                return $this->errorResponse('Failed to update delivery status', 500);
            }
        } catch (Exception $e) {
            return $this->errorResponse('Database error: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Mark notification as clicked
     */
    private function markNotificationClicked() {
        $input = json_decode(file_get_contents('php://input'), true);
        
        $required_fields = ['notification_id', 'device_id'];
        foreach ($required_fields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                return $this->errorResponse("Missing required field: $field", 400);
            }
        }
        
        try {
            $query = "UPDATE notification_delivery SET 
                     status = 'clicked', 
                     delivered_at = NOW() 
                     WHERE notification_id = :notification_id AND device_id = :device_id";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':notification_id', $input['notification_id']);
            $stmt->bindParam(':device_id', $input['device_id']);
            
            if ($stmt->execute()) {
                // Also update notification click count
                $query = "UPDATE notifications SET 
                         total_clicked = total_clicked + 1 
                         WHERE id = :notification_id";
                
                $stmt = $this->conn->prepare($query);
                $stmt->bindParam(':notification_id', $input['notification_id']);
                $stmt->execute();
                
                return $this->successResponse(['message' => 'Click tracked successfully']);
            } else {
                return $this->errorResponse('Failed to track click', 500);
            }
        } catch (Exception $e) {
            return $this->errorResponse('Database error: ' . $e->getMessage(), 500);
        }
    }
    
    /**
     * Success response helper
     */
    private function successResponse($data, $code = 200) {
        http_response_code($code);
        return json_encode([
            'success' => true,
            'data' => $data
        ]);
    }
    
    /**
     * Error response helper
     */
    private function errorResponse($message, $code = 400) {
        http_response_code($code);
        return json_encode([
            'success' => false,
            'error' => $message
        ]);
    }
}

// Handle the request
$api = new API();
echo $api->handleRequest();
?>
