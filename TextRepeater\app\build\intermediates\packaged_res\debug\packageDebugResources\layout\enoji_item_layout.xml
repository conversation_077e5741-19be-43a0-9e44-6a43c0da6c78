<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
             android:layout_height="match_parent"
    >

    <TextView
        android:id="@+id/emojiTextView"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:textSize="23sp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="10dp"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        />






</androidx.constraintlayout.widget.ConstraintLayout>