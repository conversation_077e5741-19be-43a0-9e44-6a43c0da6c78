<?php
/**
 * AdMob Ads Endpoint Handler
 * Handles all AdMob related API requests
 */

// Check if AdMobManager class is already loaded, if not load it
if (!class_exists('AdMobManager')) {
    require_once __DIR__ . '/../../classes/AdMobManager.php';
}

class AdsEndpoint {
    private $conn;
    private $response;
    private $admobManager;

    public function __construct() {
        $this->conn = getAPIDatabase();
        $this->response = new APIResponse();
        $this->admobManager = new AdMobManager();
    }

    /**
     * Handle ads endpoint requests
     */
    public function handle($method, $pathParts) {
        $action = $pathParts[1] ?? '';

        switch ($action) {
            case 'config':
                return $this->handleConfig($method);

            case 'track':
                return $this->handleTracking($method);

            case 'analytics':
                return $this->handleAnalytics($method);

            case 'units':
                return $this->handleAdUnits($method);

            default:
                return $this->response->notFound('Ads endpoint');
        }
    }

    /**
     * Handle ad configuration requests
     */
    private function handleConfig($method) {
        if ($method !== 'GET') {
            return $this->response->error('Method not allowed', 405);
        }

        try {
            $result = $this->admobManager->getApiConfig();

            if ($result['success']) {
                // Add cache headers for configuration
                header('Cache-Control: public, max-age=' . ADMOB_CACHE_DURATION);
                header('ETag: "' . md5(json_encode($result['data'])) . '"');

                return $this->response->success($result['data']);
            } else {
                return $this->response->error($result['message'], 404);
            }

        } catch (Exception $e) {
            return $this->response->serverError('Failed to get ad configuration');
        }
    }

    /**
     * Handle ad tracking requests
     */
    private function handleTracking($method) {
        if ($method !== 'POST') {
            return $this->response->error('Method not allowed', 405);
        }

        $input = $this->response->getJsonInput();
        if (!$input) {
            return $this->response->error('Invalid JSON input', 400);
        }

        // Validate required fields
        $requiredFields = ['device_id', 'ad_type', 'event_type'];
        $errors = $this->response->validateRequired($input, $requiredFields);

        if (!empty($errors)) {
            return $this->response->validationError($errors);
        }

        try {
            $query = "INSERT INTO ad_analytics
                     (device_id, ad_type, ad_unit_id, event_type, revenue, currency,
                      session_id, app_version, placement, network, created_at)
                     VALUES
                     (:device_id, :ad_type, :ad_unit_id, :event_type, :revenue, :currency,
                      :session_id, :app_version, :placement, :network, NOW())";

            $stmt = $this->conn->prepare($query);

            // Assign values to variables for bindParam
            $adUnitId = $input['ad_unit_id'] ?? '';
            $revenue = $input['revenue'] ?? 0;
            $currency = $input['currency'] ?? 'USD';
            $sessionId = $input['session_id'] ?? '';
            $appVersion = $input['app_version'] ?? '';
            $placement = $input['placement'] ?? '';
            $network = $input['network'] ?? 'admob';

            $stmt->bindParam(':device_id', $input['device_id']);
            $stmt->bindParam(':ad_type', $input['ad_type']);
            $stmt->bindParam(':ad_unit_id', $adUnitId);
            $stmt->bindParam(':event_type', $input['event_type']);
            $stmt->bindParam(':revenue', $revenue);
            $stmt->bindParam(':currency', $currency);
            $stmt->bindParam(':session_id', $sessionId);
            $stmt->bindParam(':app_version', $appVersion);
            $stmt->bindParam(':placement', $placement);
            $stmt->bindParam(':network', $network);

            if ($stmt->execute()) {
                return $this->response->success([
                    'message' => 'Event tracked successfully',
                    'event_id' => $this->conn->lastInsertId()
                ]);
            } else {
                return $this->response->serverError('Failed to track event');
            }

        } catch (Exception $e) {
            return $this->response->serverError('Database error occurred');
        }
    }

    /**
     * Handle analytics requests
     */
    private function handleAnalytics($method) {
        if ($method !== 'GET') {
            return $this->response->error('Method not allowed', 405);
        }

        $deviceId = $_GET['device_id'] ?? '';
        $startDate = $_GET['start_date'] ?? date('Y-m-d', strtotime('-7 days'));
        $endDate = $_GET['end_date'] ?? date('Y-m-d');

        if (empty($deviceId)) {
            return $this->response->error('Device ID is required', 400);
        }

        try {
            $query = "SELECT
                        ad_type,
                        event_type,
                        COUNT(*) as count,
                        SUM(revenue) as total_revenue,
                        DATE(created_at) as date
                     FROM ad_analytics
                     WHERE device_id = :device_id
                       AND DATE(created_at) BETWEEN :start_date AND :end_date
                     GROUP BY ad_type, event_type, DATE(created_at)
                     ORDER BY created_at DESC";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':device_id', $deviceId);
            $stmt->bindParam(':start_date', $startDate);
            $stmt->bindParam(':end_date', $endDate);
            $stmt->execute();

            $analytics = $stmt->fetchAll();

            return $this->response->success([
                'analytics' => $analytics,
                'period' => [
                    'start_date' => $startDate,
                    'end_date' => $endDate
                ]
            ]);

        } catch (Exception $e) {
            return $this->response->serverError('Failed to get analytics');
        }
    }

    /**
     * Handle ad units requests
     */
    private function handleAdUnits($method) {
        if ($method !== 'GET') {
            return $this->response->error('Method not allowed', 405);
        }

        try {
            $query = "SELECT
                        ad_unit_id,
                        ad_type,
                        placement,
                        is_active,
                        test_mode
                     FROM admob_config
                     WHERE is_active = 1
                     ORDER BY ad_type, placement";

            $stmt = $this->conn->prepare($query);
            $stmt->execute();

            $adUnits = $stmt->fetchAll();

            // Group by ad type for easier consumption
            $groupedUnits = [];
            foreach ($adUnits as $unit) {
                $groupedUnits[$unit['ad_type']][] = $unit;
            }

            return $this->response->success([
                'ad_units' => $groupedUnits,
                'total_units' => count($adUnits)
            ]);

        } catch (Exception $e) {
            return $this->response->serverError('Failed to get ad units');
        }
    }
}
?>
