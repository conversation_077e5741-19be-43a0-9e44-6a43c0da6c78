        </div> <!-- End content-area -->
    </div> <!-- End main-content -->
    
    <!-- JavaScript Dependencies -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    
    <script>
        // Global JavaScript functions
        
        // Toggle sidebar for mobile
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('show');
        }
        
        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.getElementById('sidebar');
            const toggleBtn = document.querySelector('.mobile-toggle');
            
            if (window.innerWidth <= 768) {
                if (!sidebar.contains(event.target) && !toggleBtn.contains(event.target)) {
                    sidebar.classList.remove('show');
                }
            }
        });
        
        // Auto-hide alerts
        function autoHideAlerts() {
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
                alerts.forEach(function(alert) {
                    alert.style.transition = 'opacity 0.5s ease';
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 500);
                });
            }, 5000);
        }
        
        // Initialize auto-hide alerts
        autoHideAlerts();
        
        // Loading state management
        function showLoading(element) {
            const originalText = element.innerHTML;
            element.innerHTML = '<span class="loading-spinner me-2"></span>Loading...';
            element.disabled = true;
            element.dataset.originalText = originalText;
        }
        
        function hideLoading(element) {
            element.innerHTML = element.dataset.originalText;
            element.disabled = false;
        }
        
        // AJAX helper function
        function makeAjaxRequest(url, method = 'GET', data = null, successCallback = null, errorCallback = null) {
            const xhr = new XMLHttpRequest();
            xhr.open(method, url, true);
            
            if (method === 'POST') {
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            }
            
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (successCallback) {
                                successCallback(response);
                            }
                        } catch (e) {
                            if (successCallback) {
                                successCallback(xhr.responseText);
                            }
                        }
                    } else {
                        if (errorCallback) {
                            errorCallback(xhr.status, xhr.statusText);
                        } else {
                            showAlert('Error', 'Request failed: ' + xhr.statusText, 'error');
                        }
                    }
                }
            };
            
            xhr.send(data);
        }
        
        // Show alert using SweetAlert2
        function showAlert(title, text, icon = 'info', confirmButtonText = 'OK') {
            return Swal.fire({
                title: title,
                text: text,
                icon: icon,
                confirmButtonText: confirmButtonText,
                confirmButtonColor: '#667eea'
            });
        }
        
        // Confirm dialog
        function confirmAction(title, text, confirmCallback, cancelCallback = null) {
            Swal.fire({
                title: title,
                text: text,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#667eea',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, proceed!',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    if (confirmCallback) {
                        confirmCallback();
                    }
                } else if (cancelCallback) {
                    cancelCallback();
                }
            });
        }
        
        // Format numbers with commas
        function formatNumber(num) {
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }
        
        // Format currency
        function formatCurrency(amount, currency = 'USD') {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: currency
            }).format(amount);
        }
        
        // Format date
        function formatDate(dateString, options = {}) {
            const defaultOptions = {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            };
            
            const finalOptions = { ...defaultOptions, ...options };
            return new Date(dateString).toLocaleDateString('en-US', finalOptions);
        }
        
        // Copy to clipboard
        function copyToClipboard(text, successMessage = 'Copied to clipboard!') {
            navigator.clipboard.writeText(text).then(function() {
                showAlert('Success', successMessage, 'success');
            }).catch(function(err) {
                console.error('Could not copy text: ', err);
                showAlert('Error', 'Failed to copy to clipboard', 'error');
            });
        }
        
        // Validate email
        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        // Validate URL
        function isValidUrl(url) {
            try {
                new URL(url);
                return true;
            } catch (e) {
                return false;
            }
        }
        
        // Initialize tooltips
        function initTooltips() {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }
        
        // Initialize popovers
        function initPopovers() {
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        }
        
        // Initialize components when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            initTooltips();
            initPopovers();
            
            // Initialize flatpickr for date inputs
            flatpickr('.datetime-picker', {
                enableTime: true,
                dateFormat: "Y-m-d H:i",
                time_24hr: true
            });
            
            flatpickr('.date-picker', {
                dateFormat: "Y-m-d"
            });
            
            flatpickr('.time-picker', {
                enableTime: true,
                noCalendar: true,
                dateFormat: "H:i",
                time_24hr: true
            });
        });
        
        // Handle form submissions with loading states
        document.addEventListener('submit', function(e) {
            const form = e.target;
            if (form.classList.contains('ajax-form')) {
                e.preventDefault();
                
                const submitBtn = form.querySelector('button[type="submit"]');
                if (submitBtn) {
                    showLoading(submitBtn);
                }
                
                // Add your AJAX form handling logic here
                setTimeout(() => {
                    if (submitBtn) {
                        hideLoading(submitBtn);
                    }
                }, 2000);
            }
        });
        
        // Session timeout warning
        let sessionWarningShown = false;
        const sessionTimeout = <?php echo Config::SESSION_TIMEOUT; ?> * 1000; // Convert to milliseconds
        const warningTime = sessionTimeout - (5 * 60 * 1000); // 5 minutes before timeout
        
        setTimeout(function() {
            if (!sessionWarningShown) {
                sessionWarningShown = true;
                Swal.fire({
                    title: 'Session Expiring',
                    text: 'Your session will expire in 5 minutes. Do you want to extend it?',
                    icon: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Extend Session',
                    cancelButtonText: 'Logout',
                    confirmButtonColor: '#667eea',
                    cancelButtonColor: '#d33'
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Extend session by making a simple AJAX request
                        makeAjaxRequest('api/extend-session.php', 'POST', null, 
                            function(response) {
                                showAlert('Success', 'Session extended successfully', 'success');
                                sessionWarningShown = false;
                            }
                        );
                    } else {
                        window.location.href = 'logout.php';
                    }
                });
            }
        }, warningTime);
    </script>
    
    <!-- Page-specific JavaScript -->
    <?php if (isset($page_scripts)): ?>
        <?php echo $page_scripts; ?>
    <?php endif; ?>
</body>
</html>
