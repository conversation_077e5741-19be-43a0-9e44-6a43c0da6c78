package com.anginatech.textrepeater.models;

import com.google.gson.annotations.SerializedName;

/**
 * App Settings Response Model
 */
public class AppSettingsResponse {
    
    @SerializedName("app_version")
    private String appVersion;
    
    @SerializedName("maintenance_mode")
    private boolean maintenanceMode;
    
    @SerializedName("force_update")
    private boolean forceUpdate;
    
    @SerializedName("min_supported_version")
    private String minSupportedVersion;
    
    @SerializedName("ad_refresh_interval")
    private int adRefreshInterval;
    
    @SerializedName("notification_enabled")
    private boolean notificationEnabled;

    // Constructors
    public AppSettingsResponse() {}

    // Getters and Setters
    public String getAppVersion() {
        return appVersion;
    }

    public void setAppVersion(String appVersion) {
        this.appVersion = appVersion;
    }

    public boolean isMaintenanceMode() {
        return maintenanceMode;
    }

    public void setMaintenanceMode(boolean maintenanceMode) {
        this.maintenanceMode = maintenanceMode;
    }

    public boolean isForceUpdate() {
        return forceUpdate;
    }

    public void setForceUpdate(boolean forceUpdate) {
        this.forceUpdate = forceUpdate;
    }

    public String getMinSupportedVersion() {
        return minSupportedVersion;
    }

    public void setMinSupportedVersion(String minSupportedVersion) {
        this.minSupportedVersion = minSupportedVersion;
    }

    public int getAdRefreshInterval() {
        return adRefreshInterval;
    }

    public void setAdRefreshInterval(int adRefreshInterval) {
        this.adRefreshInterval = adRefreshInterval;
    }

    public boolean isNotificationEnabled() {
        return notificationEnabled;
    }

    public void setNotificationEnabled(boolean notificationEnabled) {
        this.notificationEnabled = notificationEnabled;
    }
}
