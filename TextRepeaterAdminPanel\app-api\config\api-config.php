<?php
/**
 * API Configuration
 * Contains all configuration settings for the Text Repeater App API
 */

// API Version
define('API_VERSION', '1.0.0');

// Database configuration - using the existing admin panel database
require_once __DIR__ . '/../../config/database.php';

// API Security Settings
define('API_KEY_REQUIRED', false); // Set to true if you want to require API keys
define('API_RATE_LIMIT', 1000); // Requests per hour per IP
define('API_TIMEOUT', 30); // Request timeout in seconds

// Logging settings
define('API_LOG_REQUESTS', true);
define('API_LOG_ERRORS', true);
define('API_LOG_FILE', '../logs/api.log');

// AdMob API Settings
define('ADMOB_API_ENABLED', true);
define('ADMOB_CACHE_DURATION', 300); // 5 minutes cache for ad config

// Notification Settings
define('FCM_SERVER_KEY', ''); // Add your FCM server key here
define('FCM_SENDER_ID', ''); // Add your FCM sender ID here

// App Settings
define('APP_NAME', 'Text Repeater');
define('APP_PACKAGE', 'com.monirulvi.textrepeater');
define('MIN_APP_VERSION', '1.0.0');

// Response format settings
define('API_RESPONSE_FORMAT', 'json');
define('API_PRETTY_PRINT', false);

// CORS Settings
$allowedOrigins = [
    'http://localhost',
    'https://localhost',
    // Add your domain here
];

// Error reporting for API
if (defined('API_DEBUG') && API_DEBUG) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

/**
 * Get database connection for API
 */
function getAPIDatabase() {
    try {
        $database = new Database();
        return $database->getConnection();
    } catch (Exception $e) {
        error_log("API Database connection failed: " . $e->getMessage());
        return null;
    }
}

/**
 * Check if API is in maintenance mode
 */
function isMaintenanceMode() {
    return file_exists('../maintenance.flag');
}

/**
 * Get API configuration as array
 */
function getAPIConfig() {
    return [
        'version' => API_VERSION,
        'app_name' => APP_NAME,
        'package' => APP_PACKAGE,
        'min_version' => MIN_APP_VERSION,
        'features' => [
            'admob' => ADMOB_API_ENABLED,
            'notifications' => !empty(FCM_SERVER_KEY),
            'analytics' => true,
            'user_management' => true
        ],
        'limits' => [
            'rate_limit' => API_RATE_LIMIT,
            'timeout' => API_TIMEOUT
        ]
    ];
}
?>
