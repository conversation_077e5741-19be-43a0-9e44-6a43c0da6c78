package com.anginatech.textrepeater.network;

import com.anginatech.textrepeater.models.AdConfigResponse;
import com.anginatech.textrepeater.models.AppSettingsResponse;
import com.anginatech.textrepeater.models.TextContentResponse;
import com.anginatech.textrepeater.models.UserRegistrationRequest;
import com.anginatech.textrepeater.models.UserRegistrationResponse;
import com.anginatech.textrepeater.models.AdTrackingRequest;
import com.anginatech.textrepeater.models.ApiResponse;
import com.anginatech.textrepeater.models.SyncResponse;

import retrofit2.Call;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Query;

/**
 * Modern Retrofit API Service Interface
 * Replaces the slow Volley implementation with faster, more efficient networking
 */
public interface ApiService {

    /**
     * Get AdMob configuration
     * Fast cached response with automatic retry
     */
    @GET("index.php")
    Call<ApiResponse<AdConfigResponse>> getAdMobConfig(@Query("path") String path);

    /**
     * Get app settings
     * Cached response for better performance
     */
    @GET("index.php")
    Call<ApiResponse<AppSettingsResponse>> getAppSettings(@Query("path") String path);

    /**
     * Get text content (romantic, sad, funny)
     * Optimized for large data sets with compression
     */
    @GET("index.php")
    Call<ApiResponse<TextContentResponse>> getTextContent(@Query("path") String path);

    /**
     * Register user with optimized payload
     */
    @POST("index.php")
    Call<ApiResponse<UserRegistrationResponse>> registerUser(
            @Query("path") String path,
            @Body UserRegistrationRequest request
    );

    /**
     * Track ad events with batch support
     */
    @POST("index.php")
    Call<ApiResponse<Void>> trackAdEvent(
            @Query("path") String path,
            @Body AdTrackingRequest request
    );

    /**
     * Health check for API monitoring
     */
    @GET("index.php")
    Call<ApiResponse<Object>> healthCheck(@Query("path") String path);

    /**
     * Get dynamic categories and content
     * Supports pagination for better performance
     */
    @GET("index.php")
    Call<ApiResponse<TextContentResponse>> getDynamicContent(
            @Query("path") String path,
            @Query("page") Integer page,
            @Query("limit") Integer limit
    );

    /**
     * Get complete sync data for app synchronization
     * Returns all categories and content for full data refresh
     */
    @GET("index.php")
    Call<ApiResponse<SyncResponse>> getSyncData(@Query("path") String path);
}
