package com.anginatech.textrepeater.models;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * Text Content Response Model for romantic, sad, funny messages
 */
public class TextContentResponse {
    
    @SerializedName("romantic")
    private List<TextMessage> romantic;
    
    @SerializedName("sad")
    private List<TextMessage> sad;
    
    @SerializedName("funny")
    private List<TextMessage> funny;
    
    @SerializedName("categories")
    private List<Category> categories;
    
    @SerializedName("content")
    private Object content; // Dynamic content object

    // Constructors
    public TextContentResponse() {}

    // Getters and Setters
    public List<TextMessage> getRomantic() {
        return romantic;
    }

    public void setRomantic(List<TextMessage> romantic) {
        this.romantic = romantic;
    }

    public List<TextMessage> getSad() {
        return sad;
    }

    public void setSad(List<TextMessage> sad) {
        this.sad = sad;
    }

    public List<TextMessage> getFunny() {
        return funny;
    }

    public void setFunny(List<TextMessage> funny) {
        this.funny = funny;
    }

    public List<Category> getCategories() {
        return categories;
    }

    public void setCategories(List<Category> categories) {
        this.categories = categories;
    }

    public Object getContent() {
        return content;
    }

    public void setContent(Object content) {
        this.content = content;
    }

    /**
     * Text Message nested class
     */
    public static class TextMessage {
        @SerializedName("id")
        private String id;
        
        @SerializedName("message")
        private String message;
        
        @SerializedName("sort_order")
        private int sortOrder;
        
        @SerializedName("created_at")
        private String createdAt;

        // Constructors
        public TextMessage() {}

        public TextMessage(String id, String message) {
            this.id = id;
            this.message = message;
        }

        // Getters and Setters
        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public int getSortOrder() {
            return sortOrder;
        }

        public void setSortOrder(int sortOrder) {
            this.sortOrder = sortOrder;
        }

        public String getCreatedAt() {
            return createdAt;
        }

        public void setCreatedAt(String createdAt) {
            this.createdAt = createdAt;
        }
    }
}
