package com.anginatech.textrepeater.models;

import java.io.Serializable;

/**
 * Text message model for dynamic content
 */
public class TextMessage implements Serializable {
    private static final long serialVersionUID = 1L;
    private int id;
    private String message;
    private String categoryName;

    public TextMessage() {
    }

    public TextMessage(int id, String message) {
        this.id = id;
        this.message = message;
    }

    public TextMessage(int id, String message, String categoryName) {
        this.id = id;
        this.message = message;
        this.categoryName = categoryName;
    }

    // Getters and Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    @Override
    public String toString() {
        return "TextMessage{" +
                "id=" + id +
                ", message='" + message + '\'' +
                ", categoryName='" + categoryName + '\'' +
                '}';
    }
}
