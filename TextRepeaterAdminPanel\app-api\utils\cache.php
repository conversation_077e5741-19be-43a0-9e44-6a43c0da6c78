<?php
/**
 * Simple File-based Cache System for API Performance Optimization
 * Provides fast response caching to improve API performance
 */

class APICache {
    private $cacheDir;
    private $defaultTTL;

    public function __construct($cacheDir = '../cache', $defaultTTL = 300) {
        $this->cacheDir = $cacheDir;
        $this->defaultTTL = $defaultTTL; // 5 minutes default
        
        // Create cache directory if it doesn't exist
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }

    /**
     * Generate cache key from request parameters
     */
    private function generateCacheKey($key) {
        return md5($key) . '.cache';
    }

    /**
     * Get cache file path
     */
    private function getCacheFilePath($key) {
        return $this->cacheDir . '/' . $this->generateCacheKey($key);
    }

    /**
     * Store data in cache
     */
    public function set($key, $data, $ttl = null) {
        try {
            $ttl = $ttl ?? $this->defaultTTL;
            $cacheData = [
                'data' => $data,
                'expires' => time() + $ttl,
                'created' => time()
            ];

            $filePath = $this->getCacheFilePath($key);
            $result = file_put_contents($filePath, serialize($cacheData), LOCK_EX);
            
            return $result !== false;
        } catch (Exception $e) {
            error_log("Cache set error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get data from cache
     */
    public function get($key) {
        try {
            $filePath = $this->getCacheFilePath($key);
            
            if (!file_exists($filePath)) {
                return null;
            }

            $cacheData = unserialize(file_get_contents($filePath));
            
            if (!$cacheData || !isset($cacheData['expires'])) {
                return null;
            }

            // Check if cache has expired
            if (time() > $cacheData['expires']) {
                $this->delete($key);
                return null;
            }

            return $cacheData['data'];
        } catch (Exception $e) {
            error_log("Cache get error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if cache key exists and is valid
     */
    public function has($key) {
        return $this->get($key) !== null;
    }

    /**
     * Delete cache entry
     */
    public function delete($key) {
        try {
            $filePath = $this->getCacheFilePath($key);
            if (file_exists($filePath)) {
                return unlink($filePath);
            }
            return true;
        } catch (Exception $e) {
            error_log("Cache delete error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clear all cache
     */
    public function clear() {
        try {
            $files = glob($this->cacheDir . '/*.cache');
            foreach ($files as $file) {
                unlink($file);
            }
            return true;
        } catch (Exception $e) {
            error_log("Cache clear error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get cache statistics
     */
    public function getStats() {
        try {
            $files = glob($this->cacheDir . '/*.cache');
            $totalSize = 0;
            $validEntries = 0;
            $expiredEntries = 0;

            foreach ($files as $file) {
                $totalSize += filesize($file);
                
                $cacheData = unserialize(file_get_contents($file));
                if ($cacheData && isset($cacheData['expires'])) {
                    if (time() <= $cacheData['expires']) {
                        $validEntries++;
                    } else {
                        $expiredEntries++;
                    }
                }
            }

            return [
                'total_files' => count($files),
                'total_size' => $totalSize,
                'valid_entries' => $validEntries,
                'expired_entries' => $expiredEntries,
                'cache_dir' => $this->cacheDir
            ];
        } catch (Exception $e) {
            error_log("Cache stats error: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Clean expired cache entries
     */
    public function cleanExpired() {
        try {
            $files = glob($this->cacheDir . '/*.cache');
            $cleaned = 0;

            foreach ($files as $file) {
                $cacheData = unserialize(file_get_contents($file));
                if ($cacheData && isset($cacheData['expires'])) {
                    if (time() > $cacheData['expires']) {
                        unlink($file);
                        $cleaned++;
                    }
                }
            }

            return $cleaned;
        } catch (Exception $e) {
            error_log("Cache clean error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * Cache with callback - get from cache or execute callback and cache result
     */
    public function remember($key, $callback, $ttl = null) {
        $data = $this->get($key);
        
        if ($data !== null) {
            return $data;
        }

        $data = $callback();
        $this->set($key, $data, $ttl);
        
        return $data;
    }
}

/**
 * Global cache instance
 */
function getAPICache() {
    static $cache = null;
    if ($cache === null) {
        $cache = new APICache();
    }
    return $cache;
}

/**
 * Helper function to generate cache key for API requests
 */
function generateAPICacheKey($endpoint, $params = []) {
    $keyData = [
        'endpoint' => $endpoint,
        'params' => $params,
        'version' => API_VERSION ?? '1.0.0'
    ];
    return 'api_' . md5(json_encode($keyData));
}

/**
 * Middleware function to add cache headers
 */
function addCacheHeaders($maxAge = 300) {
    header('Cache-Control: public, max-age=' . $maxAge);
    header('Expires: ' . gmdate('D, d M Y H:i:s', time() + $maxAge) . ' GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
}

/**
 * Enable gzip compression for better performance
 */
function enableGzipCompression() {
    if (!ob_get_level() && extension_loaded('zlib') && !headers_sent()) {
        ob_start('ob_gzhandler');
    }
}
?>
