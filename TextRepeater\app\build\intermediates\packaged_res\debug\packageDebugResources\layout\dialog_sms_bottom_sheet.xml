<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/drawer_color_item"
    android:orientation="vertical"
    >


    <com.google.android.material.bottomsheet.BottomSheetDragHandleView
        android:id="@+id/bottomSheet"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"



        />

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayoutDialog1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/mother_layout_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/bottomSheet"
        app:tabIndicatorColor="@color/love"
        app:tabIndicatorGravity="bottom"
        app:tabIndicatorHeight="3dp"
        app:tabPaddingBottom="14dp"
        app:tabPaddingEnd="12dp"
        app:tabPaddingStart="12dp"
        app:tabPaddingTop="14dp"
        app:tabSelectedTextColor="@color/love"
        app:tabTextColor="@color/primary_text"
        app:tabTextAppearance="@style/DialogTabLayoutTextAppearance"
        app:tabMode="scrollable"
        app:tabGravity="start"
        app:tabMinWidth="100dp"
        app:tabMaxWidth="180dp"
        app:tabContentStart="16dp"
        app:tabRippleColor="@color/love"
        app:tabUnboundedRipple="false"
        >

    </com.google.android.material.tabs.TabLayout>


    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/viewPagerDialog1"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/mother_layout_color"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayoutDialog"
        android:paddingBottom="30dp"
        />




</LinearLayout>