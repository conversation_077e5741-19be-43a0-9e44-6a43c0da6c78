<?php
/**
 * API Response Utility
 * Standardizes API response format and handling
 */

class APIResponse {

    private static $cachedInput = null;

    /**
     * Send success response
     */
    public function success($data = null, $message = '', $code = 200) {
        http_response_code($code);

        $response = [
            'success' => true,
            'timestamp' => date('c'),
            'version' => API_VERSION
        ];

        if (!empty($message)) {
            $response['message'] = $message;
        }

        if ($data !== null) {
            $response['data'] = $data;
        }

        return $this->formatResponse($response);
    }

    /**
     * Send error response
     */
    public function error($message, $code = 400, $details = null) {
        http_response_code($code);

        $response = [
            'success' => false,
            'error' => [
                'message' => $message,
                'code' => $code
            ],
            'timestamp' => date('c'),
            'version' => API_VERSION
        ];

        if ($details !== null) {
            $response['error']['details'] = $details;
        }

        // Log errors for debugging
        if (API_LOG_ERRORS) {
            $this->logError($message, $code, $details);
        }

        return $this->formatResponse($response);
    }

    /**
     * Send validation error response
     */
    public function validationError($errors, $message = 'Validation failed') {
        return $this->error($message, 422, ['validation_errors' => $errors]);
    }

    /**
     * Send not found response
     */
    public function notFound($resource = 'Resource') {
        return $this->error($resource . ' not found', 404);
    }

    /**
     * Send unauthorized response
     */
    public function unauthorized($message = 'Unauthorized access') {
        return $this->error($message, 401);
    }

    /**
     * Send forbidden response
     */
    public function forbidden($message = 'Access forbidden') {
        return $this->error($message, 403);
    }

    /**
     * Send rate limit exceeded response
     */
    public function rateLimitExceeded($message = 'Rate limit exceeded') {
        return $this->error($message, 429);
    }

    /**
     * Send server error response
     */
    public function serverError($message = 'Internal server error') {
        return $this->error($message, 500);
    }

    /**
     * Format response based on configuration
     */
    private function formatResponse($response) {
        $flags = 0;

        if (API_PRETTY_PRINT) {
            $flags |= JSON_PRETTY_PRINT;
        }

        return json_encode($response, $flags);
    }

    /**
     * Log error for debugging
     */
    private function logError($message, $code, $details = null) {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'error' => $message,
            'code' => $code,
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ];

        if ($details !== null) {
            $logData['details'] = $details;
        }

        error_log("API Error: " . json_encode($logData));
    }

    /**
     * Validate required fields in input data
     */
    public function validateRequired($data, $requiredFields) {
        $errors = [];

        foreach ($requiredFields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $errors[$field] = "Field '$field' is required";
            }
        }

        return $errors;
    }

    /**
     * Sanitize input data
     */
    public function sanitizeInput($data) {
        if (is_array($data)) {
            return array_map([$this, 'sanitizeInput'], $data);
        }

        return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
    }

    /**
     * Get JSON input from request body
     */
    public function getJsonInput() {
        // Cache the input since php://input can only be read once
        if (self::$cachedInput === null) {
            self::$cachedInput = file_get_contents('php://input');
        }

        $input = self::$cachedInput;

        // Log the raw input for debugging
        error_log("Raw input received: " . $input);
        error_log("Content-Type: " . ($_SERVER['CONTENT_TYPE'] ?? 'not set'));
        error_log("Request Method: " . ($_SERVER['REQUEST_METHOD'] ?? 'not set'));

        // If php://input is empty, try to get data from $_POST or raw POST data
        if (empty($input)) {
            error_log("Empty input received from php://input, trying alternatives");

            // Check if we have POST data
            if (!empty($_POST)) {
                error_log("Found POST data: " . json_encode($_POST));
                // If POST data is already an array, return it
                if (is_array($_POST)) {
                    return $this->sanitizeInput($_POST);
                }
                // If POST data is a JSON string, decode it
                $input = is_string($_POST) ? $_POST : json_encode($_POST);
            } else {
                error_log("No POST data available either");
                return null;
            }
        }

        $data = json_decode($input, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("JSON decode error: " . json_last_error_msg() . " for input: " . $input);
            return null;
        }

        error_log("Successfully parsed JSON input: " . json_encode($data));
        return $this->sanitizeInput($data);
    }

    /**
     * Paginate results
     */
    public function paginate($data, $page = 1, $perPage = 20) {
        $total = count($data);
        $totalPages = ceil($total / $perPage);
        $offset = ($page - 1) * $perPage;

        $paginatedData = array_slice($data, $offset, $perPage);

        return [
            'data' => $paginatedData,
            'pagination' => [
                'current_page' => $page,
                'per_page' => $perPage,
                'total_items' => $total,
                'total_pages' => $totalPages,
                'has_next' => $page < $totalPages,
                'has_prev' => $page > 1
            ]
        ];
    }
}
?>
