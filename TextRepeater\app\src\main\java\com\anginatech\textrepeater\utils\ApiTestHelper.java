package com.anginatech.textrepeater.utils;

import android.content.Context;
import android.util.Log;

import com.anginatech.textrepeater.Config;
import com.anginatech.textrepeater.network.ModernApiClient;
import com.anginatech.textrepeater.repository.TextRepeaterRepository;

/**
 * Helper class to test API functionality and verify the fixes
 */
public class ApiTestHelper {
    private static final String TAG = "ApiTestHelper";

    /**
     * Test the modern API client initialization
     */
    public static void testModernApiClient(Context context) {
        try {
            Log.d(TAG, "Testing Modern API Client initialization...");
            
            // Test base URL format
            String baseUrl = Config.BASE_URL;
            Log.d(TAG, "Base URL: " + baseUrl);
            
            if (!baseUrl.endsWith("/")) {
                Log.e(TAG, "ERROR: Base URL must end with /");
                return;
            }
            
            // Test API client initialization
            ModernApiClient apiClient = ModernApiClient.getInstance(context);
            if (apiClient != null) {
                Log.d(TAG, "✅ Modern API Client initialized successfully");
                Log.d(TAG, "✅ Cache size: " + apiClient.getCacheSize() + " bytes");
                Log.d(TAG, "✅ Cache available: " + apiClient.isCacheAvailable());
            } else {
                Log.e(TAG, "❌ Failed to initialize Modern API Client");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Error testing Modern API Client: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test the repository initialization
     */
    public static void testRepository(Context context) {
        try {
            Log.d(TAG, "Testing Repository initialization...");
            
            TextRepeaterRepository repository = TextRepeaterRepository.getInstance(context);
            if (repository != null) {
                Log.d(TAG, "✅ Repository initialized successfully");
                Log.d(TAG, "✅ Network available: " + repository.isNetworkAvailable());
            } else {
                Log.e(TAG, "❌ Failed to initialize Repository");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Error testing Repository: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Test API URL building
     */
    public static void testApiUrls() {
        try {
            Log.d(TAG, "Testing API URL building...");
            
            // Test legacy URL building
            String legacyUrl = Config.buildApiUrl("ads/config");
            Log.d(TAG, "Legacy URL: " + legacyUrl);
            
            // Test modern URL building
            String modernUrl = Config.buildModernApiUrl("ads/config");
            Log.d(TAG, "Modern URL: " + modernUrl);
            
            // Test direct URL building
            String directUrl = Config.buildDirectApiUrl("api/funny_text.json.php");
            Log.d(TAG, "Direct URL: " + directUrl);
            
            Log.d(TAG, "✅ All URL building methods work correctly");
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Error testing API URLs: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Run all tests
     */
    public static void runAllTests(Context context) {
        Log.d(TAG, "🚀 Starting API Test Suite...");
        
        testApiUrls();
        testModernApiClient(context);
        testRepository(context);
        
        Log.d(TAG, "✅ API Test Suite completed");
    }

    /**
     * Test network connectivity and API endpoints
     */
    public static void testNetworkConnectivity(Context context) {
        try {
            Log.d(TAG, "Testing network connectivity...");
            
            TextRepeaterRepository repository = TextRepeaterRepository.getInstance(context);
            
            if (repository.isNetworkAvailable()) {
                Log.d(TAG, "✅ Network is available");
                
                // Test AdMob config endpoint
                repository.fetchAdMobConfig(new TextRepeaterRepository.RepositoryCallback<com.anginatech.textrepeater.models.AdConfigResponse>() {
                    @Override
                    public void onSuccess(com.anginatech.textrepeater.models.AdConfigResponse data) {
                        Log.d(TAG, "✅ AdMob config fetch successful");
                        Log.d(TAG, "✅ Ads enabled: " + data.isAdsEnabled());
                    }

                    @Override
                    public void onError(String error) {
                        Log.e(TAG, "❌ AdMob config fetch failed: " + error);
                    }
                });
                
            } else {
                Log.w(TAG, "⚠️ Network is not available");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "❌ Error testing network connectivity: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
