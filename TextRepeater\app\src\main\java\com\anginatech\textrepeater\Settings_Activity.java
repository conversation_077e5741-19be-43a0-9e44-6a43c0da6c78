package com.anginatech.textrepeater;

import android.app.Dialog;
import android.content.ActivityNotFoundException;
import android.content.Intent;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.SwitchCompat;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.button.MaterialButton;

public class Settings_Activity extends AppCompatActivity {


    MaterialToolbar materialToolbar;
    SwitchCompat nightSwitch;
    private SharedPreferences sharedPreferences;
    private SharedPreferences.Editor editor;
    ConstraintLayout constraintLayout_Contact,constraintLayout_Share,constraintLayout_Rate_Us,constraintLayout_About_Us,constraintLayout_Privacy;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);

        sharedPreferences = getSharedPreferences("nightModePrefs", MODE_PRIVATE);
        editor = sharedPreferences.edit();

        if (sharedPreferences.getBoolean("nightMode", false)) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }
        setContentView(R.layout.activity_settings);

        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        materialToolbar = findViewById(R.id.materialToolbar);
        nightSwitch =  findViewById(R.id.nightSwitch);
        constraintLayout_Contact = findViewById(R.id.constraintLayout_Contact);
        constraintLayout_Share = findViewById(R.id.constraintLayout_Share);
        constraintLayout_Rate_Us = findViewById(R.id.constraintLayout_Rate_Us);
        constraintLayout_About_Us = findViewById(R.id.constraintLayout_About_Us);
        constraintLayout_Privacy = findViewById(R.id.constraintLayout_Privacy);



        constraintLayout_Contact.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                sendEmail();

            }
        });

        constraintLayout_Share.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                shareApp();

            }
        });


        constraintLayout_Rate_Us.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openRateUs();
            }
        });

        constraintLayout_About_Us.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showCustomDialog();

            }
        });


        constraintLayout_Privacy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openPrivacyPolicy();

            }
        });


        materialToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                startActivity(new Intent(Settings_Activity.this, MainActivity.class));
                finish();

            }
        });

        

        materialToolbar.setTitleTextAppearance(Settings_Activity.this,R.style.RobotoBoldTextAppearance);

        nightSwitch.setChecked(sharedPreferences.getBoolean("nightMode", false));

        nightSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
            if (isChecked) {
                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
                editor.putBoolean("nightMode", true);
                Toast.makeText(this, "Night Mode Enable", Toast.LENGTH_SHORT).show();
            } else {

                AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
                editor.putBoolean("nightMode", false);
                Toast.makeText(this, "Night Mode Disable", Toast.LENGTH_SHORT).show();
            }
            editor.apply();
        });
    }

    @Override
    public void onBackPressed() {

        startActivity(new Intent(Settings_Activity.this,MainActivity.class));
        finish();


        super.onBackPressed();
    }

    private void openRateUs() {
        try {
            Uri uri = Uri.parse("market://details?id=" + getPackageName());
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        } catch (ActivityNotFoundException e) {
            Uri uri = Uri.parse("https://play.google.com/store/apps/details?id=" + getPackageName());
            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        }
    }

    private void shareApp() {
        String shareText = "Check out this amazing app: https://play.google.com/store/apps/details?id=" + getPackageName();
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, shareText);
        startActivity(Intent.createChooser(shareIntent, "Share via"));
    }

    private void openPrivacyPolicy() {
        Uri uri = Uri.parse("https://sites.google.com/view/textprivacy");
        Intent intent = new Intent(Intent.ACTION_VIEW, uri);
        startActivity(intent);
    }


    private void openPrivacyPolicy1() {
        Uri uri = Uri.parse("https://anginatech.com");
        Intent intent = new Intent(Intent.ACTION_VIEW, uri);
        startActivity(intent);
    }

    private void sendEmail() {
        Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
        emailIntent.setData(Uri.parse("mailto:"));
        emailIntent.putExtra(Intent.EXTRA_EMAIL, new String[]{"<EMAIL>"});
        emailIntent.putExtra(Intent.EXTRA_SUBJECT, "Contact from App");
        emailIntent.putExtra(Intent.EXTRA_TEXT, "Hello, I need assistance with...");

        try {
            startActivity(Intent.createChooser(emailIntent, "Send Email"));
        } catch (ActivityNotFoundException e) {
            Toast.makeText(this, "No email client installed.", Toast.LENGTH_SHORT).show();
        }
    }


    private void showCustomDialog() {
        // Create Dialog
        Dialog dialog = new Dialog(this);
        dialog.setContentView(R.layout.dialog_about_us);

        // Set custom background with rounded corners
        if (dialog.getWindow() != null) {
            dialog.getWindow().setBackgroundDrawableResource(R.drawable.dialog_background);
            dialog.getWindow().setLayout(
                    (int) (getResources().getDisplayMetrics().widthPixels * 0.90), // 90% of screen width
                    WindowManager.LayoutParams.WRAP_CONTENT // Wrap content height
            );
        }

        // Get views from the custom layout
        TextView textView_Email = dialog.findViewById(R.id.textView_Email);
        TextView textView_Website = dialog.findViewById(R.id.textView_Website);
        MaterialButton dialog_Dismis_Button = dialog.findViewById(R.id.dialog_Dismis_Button);

        textView_Email.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                sendEmail();

            }
        });

        textView_Website.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                openPrivacyPolicy1();

            }
        });


        dialog_Dismis_Button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dialog.dismiss();
            }
        });




        dialog.show();
    }




}