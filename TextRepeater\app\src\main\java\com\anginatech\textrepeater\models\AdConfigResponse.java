package com.anginatech.textrepeater.models;

import com.google.gson.annotations.SerializedName;

/**
 * AdMob Configuration Response Model
 */
public class AdConfigResponse {
    
    @SerializedName("app_id")
    private String appId;
    
    @SerializedName("banner_id")
    private String bannerId;
    
    @SerializedName("interstitial_id")
    private String interstitialId;
    
    @SerializedName("app_open_id")
    private String appOpenId;
    
    @SerializedName("is_active")
    private int isActive;
    
    @SerializedName("test_mode")
    private int testMode;
    
    @SerializedName("ad_frequency")
    private int adFrequency;
    
    @SerializedName("max_ads_per_session")
    private int maxAdsPerSession;

    // Constructors
    public AdConfigResponse() {}

    // Getters and Setters
    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getBannerId() {
        return bannerId;
    }

    public void setBannerId(String bannerId) {
        this.bannerId = bannerId;
    }

    public String getInterstitialId() {
        return interstitialId;
    }

    public void setInterstitialId(String interstitialId) {
        this.interstitialId = interstitialId;
    }

    public String getAppOpenId() {
        return appOpenId;
    }

    public void setAppOpenId(String appOpenId) {
        this.appOpenId = appOpenId;
    }

    public int getIsActive() {
        return isActive;
    }

    public void setIsActive(int isActive) {
        this.isActive = isActive;
    }

    public int getTestMode() {
        return testMode;
    }

    public void setTestMode(int testMode) {
        this.testMode = testMode;
    }

    public int getAdFrequency() {
        return adFrequency;
    }

    public void setAdFrequency(int adFrequency) {
        this.adFrequency = adFrequency;
    }

    public int getMaxAdsPerSession() {
        return maxAdsPerSession;
    }

    public void setMaxAdsPerSession(int maxAdsPerSession) {
        this.maxAdsPerSession = maxAdsPerSession;
    }

    // Helper methods
    public boolean isAdsEnabled() {
        return isActive == 1;
    }

    public boolean isTestModeEnabled() {
        return testMode == 1;
    }
}
