package com.anginatech.textrepeater;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.anginatech.textrepeater.models.SyncResponse;
import com.anginatech.textrepeater.network.NetworkUtils;

/**
 * Sync Manager - Coordinates data synchronization operations
 * Provides high-level sync management with progress tracking and error handling
 */
public class SyncManager {
    private static final String TAG = "SyncManager";
    
    private static SyncManager instance;
    private final Context context;
    private final DataSyncService syncService;
    private final Handler mainHandler;
    
    // Sync state
    private boolean isInitialized = false;
    private SyncProgressListener progressListener;
    
    /**
     * Progress listener interface for UI updates
     */
    public interface SyncProgressListener {
        void onSyncStarted();
        void onSyncProgress(String message, int progress);
        void onSyncSuccess(String message);
        void onSyncError(String error);
        void onSyncComplete();
    }
    
    private SyncManager(Context context) {
        this.context = context.getApplicationContext();
        this.syncService = new DataSyncService(this.context);
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.isInitialized = true;
    }
    
    /**
     * Get singleton instance
     */
    public static synchronized SyncManager getInstance(Context context) {
        if (instance == null) {
            instance = new SyncManager(context);
        }
        return instance;
    }
    
    /**
     * Set progress listener for UI updates
     */
    public void setSyncProgressListener(SyncProgressListener listener) {
        this.progressListener = listener;
    }
    
    /**
     * Remove progress listener
     */
    public void removeSyncProgressListener() {
        this.progressListener = null;
    }
    
    /**
     * Check if sync is needed and perform automatic sync
     */
    public void performAutoSyncIfNeeded() {
        if (!isInitialized) {
            Log.w(TAG, "SyncManager not initialized");
            return;
        }
        
        Log.d(TAG, "Checking if auto sync is needed...");
        
        // Check network availability
        if (!NetworkUtils.isNetworkAvailable(context)) {
            Log.d(TAG, "Network not available, skipping auto sync");
            notifyComplete();
            return;
        }
        
        // Check if sync is needed
        if (!syncService.isSyncNeeded()) {
            Log.d(TAG, "Auto sync not needed");
            notifyComplete();
            return;
        }
        
        // Perform sync
        performSync(false);
    }
    
    /**
     * Force sync regardless of cache status
     */
    public void performForceSync() {
        if (!isInitialized) {
            Log.w(TAG, "SyncManager not initialized");
            return;
        }
        
        Log.d(TAG, "Performing force sync...");
        
        // Check network availability
        if (!NetworkUtils.isNetworkAvailable(context)) {
            Log.w(TAG, "Network not available for force sync");
            notifyError("Network not available. Please check your internet connection.");
            return;
        }
        
        // Perform sync
        performSync(true);
    }
    
    /**
     * Perform sync operation
     */
    private void performSync(boolean forceSync) {
        Log.d(TAG, "Starting sync operation (force: " + forceSync + ")");
        
        DataSyncService.SyncCallback callback = new DataSyncService.SyncCallback() {
            @Override
            public void onSyncStarted() {
                Log.d(TAG, "Sync started");
                notifyStarted();
            }
            
            @Override
            public void onSyncProgress(String message, int progress) {
                Log.d(TAG, "Sync progress: " + message + " (" + progress + "%)");
                notifyProgress(message, progress);
            }
            
            @Override
            public void onSyncSuccess(SyncResponse data) {
                Log.d(TAG, "Sync completed successfully");
                String message = "Sync completed successfully. " +
                    "Updated " + data.getTotalCategories() + " categories and " +
                    data.getTotalMessages() + " messages.";
                notifySuccess(message);
            }
            
            @Override
            public void onSyncError(String error) {
                Log.e(TAG, "Sync failed: " + error);
                notifyError("Sync failed: " + error);
            }
            
            @Override
            public void onSyncComplete() {
                Log.d(TAG, "Sync operation completed");
                notifyComplete();
            }
        };
        
        // Execute sync based on type
        if (forceSync) {
            syncService.performForceSync(callback);
        } else {
            syncService.performAutoSync(callback);
        }
    }
    
    /**
     * Check if sync is currently in progress
     */
    public boolean isSyncing() {
        return syncService.isSyncing();
    }
    
    /**
     * Check if sync is enabled
     */
    public boolean isSyncEnabled() {
        return syncService.isSyncEnabled();
    }
    
    /**
     * Enable or disable sync
     */
    public void setSyncEnabled(boolean enabled) {
        syncService.setSyncEnabled(enabled);
        Log.d(TAG, "Sync " + (enabled ? "enabled" : "disabled"));
    }
    
    /**
     * Get last sync time
     */
    public long getLastSyncTime() {
        return syncService.getLastSyncTime();
    }
    
    /**
     * Get formatted last sync time
     */
    public String getLastSyncTimeFormatted() {
        long lastSync = getLastSyncTime();
        if (lastSync == 0) {
            return "Never";
        }
        
        long timeDiff = System.currentTimeMillis() - lastSync;
        long hours = timeDiff / (1000 * 60 * 60);
        long minutes = (timeDiff % (1000 * 60 * 60)) / (1000 * 60);
        
        if (hours > 0) {
            return hours + " hours ago";
        } else if (minutes > 0) {
            return minutes + " minutes ago";
        } else {
            return "Just now";
        }
    }
    
    // Notification methods that run on main thread
    private void notifyStarted() {
        mainHandler.post(() -> {
            if (progressListener != null) {
                progressListener.onSyncStarted();
            }
        });
    }
    
    private void notifyProgress(String message, int progress) {
        mainHandler.post(() -> {
            if (progressListener != null) {
                progressListener.onSyncProgress(message, progress);
            }
        });
    }
    
    private void notifySuccess(String message) {
        mainHandler.post(() -> {
            if (progressListener != null) {
                progressListener.onSyncSuccess(message);
            }
        });
    }
    
    private void notifyError(String error) {
        mainHandler.post(() -> {
            if (progressListener != null) {
                progressListener.onSyncError(error);
            }
        });
    }
    
    private void notifyComplete() {
        mainHandler.post(() -> {
            if (progressListener != null) {
                progressListener.onSyncComplete();
            }
        });
    }
    
    /**
     * Get sync status information
     */
    public SyncStatus getSyncStatus() {
        return new SyncStatus(
            isSyncEnabled(),
            isSyncing(),
            getLastSyncTime(),
            getLastSyncTimeFormatted(),
            NetworkUtils.isNetworkAvailable(context)
        );
    }
    
    /**
     * Sync status data class
     */
    public static class SyncStatus {
        private final boolean enabled;
        private final boolean syncing;
        private final long lastSyncTime;
        private final String lastSyncFormatted;
        private final boolean networkAvailable;
        
        public SyncStatus(boolean enabled, boolean syncing, long lastSyncTime, 
                         String lastSyncFormatted, boolean networkAvailable) {
            this.enabled = enabled;
            this.syncing = syncing;
            this.lastSyncTime = lastSyncTime;
            this.lastSyncFormatted = lastSyncFormatted;
            this.networkAvailable = networkAvailable;
        }
        
        public boolean isEnabled() { return enabled; }
        public boolean isSyncing() { return syncing; }
        public long getLastSyncTime() { return lastSyncTime; }
        public String getLastSyncFormatted() { return lastSyncFormatted; }
        public boolean isNetworkAvailable() { return networkAvailable; }
        
        @Override
        public String toString() {
            return "SyncStatus{" +
                    "enabled=" + enabled +
                    ", syncing=" + syncing +
                    ", lastSync='" + lastSyncFormatted + '\'' +
                    ", networkAvailable=" + networkAvailable +
                    '}';
        }
    }
}
