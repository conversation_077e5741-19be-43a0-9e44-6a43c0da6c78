package com.anginatech.textrepeater;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;

import java.util.List;

public class DecorationAdapter extends RecyclerView.Adapter<DecorationAdapter.ViewHolder> {
    private final List<String> decorationList;
    private Context context;

    public DecorationAdapter(Context context,List<String> decorationList) {
        this.decorationList = decorationList;
        this.context = context;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.item_stylish_text, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        String decoration = decorationList.get(position);
        holder.textViewDecoration.setText(decoration);
        int number = position+1;
        holder.textViewDecoration.setSelected(true);
        holder.textPositionStyle.setText(""+number);
        holder.itemLayoutStyle.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(context);
                View bottomSheetView = LayoutInflater.from(context).inflate(R.layout.sms_item_dailog, null);

                TextView smsItemText = bottomSheetView.findViewById(R.id.smsItemText);
                CardView cardViewCopy = bottomSheetView.findViewById(R.id.cardViewCopy);
                CardView cardViewShare = bottomSheetView.findViewById(R.id.cardViewShare);
                smsItemText.setText(""+decoration);

                cardViewCopy.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {

                        ClipboardManager clipboard = (ClipboardManager) v.getContext()
                                .getSystemService(Context.CLIPBOARD_SERVICE);
                        ClipData clip = ClipData.newPlainText("Decorated Text", decoration);
                        clipboard.setPrimaryClip(clip);
                        Toast.makeText(v.getContext(), "Copied to clipboard", Toast.LENGTH_SHORT).show();
                        bottomSheetDialog.dismiss();

                    }


                });

                cardViewShare.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Intent shareIntent = new Intent(Intent.ACTION_SEND);
                        shareIntent.setType("text/plain");
                        shareIntent.putExtra(Intent.EXTRA_TEXT, decoration);
                        v.getContext().startActivity(Intent.createChooser(shareIntent, "Share via"));
                        bottomSheetDialog.dismiss();

                    }
                });

                bottomSheetDialog.setContentView(bottomSheetView);
                bottomSheetDialog.show();
            }
        });


    }

    @Override
    public int getItemCount() {
        return decorationList.size();
    }

    static class ViewHolder extends RecyclerView.ViewHolder {
        TextView textViewDecoration,textPositionStyle;
        ConstraintLayout itemLayoutStyle;


        ViewHolder(View itemView) {
            super(itemView);
            textViewDecoration = itemView.findViewById(R.id.textView);
            textPositionStyle = itemView.findViewById(R.id.textPositionStyle);
            itemLayoutStyle = itemView.findViewById(R.id.itemLayoutStyle);


        }
    }


}