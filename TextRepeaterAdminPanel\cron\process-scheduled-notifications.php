<?php
/**
 * Scheduled Notification Processor
 * This script processes scheduled notifications that are due to be sent
 * 
 * Usage:
 * - Run via cron job every minute: * * * * * /usr/bin/php /path/to/process-scheduled-notifications.php
 * - Or run manually: php process-scheduled-notifications.php
 * - Or run via web: http://yoursite.com/TextRepeaterAdminPanel/cron/process-scheduled-notifications.php
 */

// Set execution time limit for long-running process
set_time_limit(300); // 5 minutes max

// Include required files
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/NotificationManager.php';
require_once __DIR__ . '/../classes/FirebaseV1Service.php';

// Log file for debugging
$log_file = __DIR__ . '/scheduled-notifications.log';

/**
 * Log message with timestamp
 */
function logMessage($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    $log_entry = "[$timestamp] $message" . PHP_EOL;
    file_put_contents($log_file, $log_entry, FILE_APPEND | LOCK_EX);
    
    // Also output to console if running via CLI
    if (php_sapi_name() === 'cli') {
        echo $log_entry;
    }
}

/**
 * Send response for web requests
 */
function sendResponse($data) {
    if (php_sapi_name() !== 'cli') {
        header('Content-Type: application/json');
        echo json_encode($data);
    }
}

try {
    logMessage("=== Scheduled Notification Processor Started ===");
    
    // Initialize notification manager
    $notificationManager = new NotificationManager();
    
    // Get scheduled notifications that are due
    $scheduledNotifications = $notificationManager->getScheduledNotifications();
    
    if (empty($scheduledNotifications)) {
        logMessage("No scheduled notifications found to process");
        sendResponse([
            'success' => true,
            'message' => 'No scheduled notifications to process',
            'processed' => 0
        ]);
        exit;
    }
    
    logMessage("Found " . count($scheduledNotifications) . " scheduled notifications to process");
    
    $processed = 0;
    $successful = 0;
    $failed = 0;
    $results = [];
    
    foreach ($scheduledNotifications as $notification) {
        $notificationId = $notification['id'];
        $title = $notification['title'];
        $scheduledAt = $notification['scheduled_at'];
        
        logMessage("Processing notification ID: $notificationId - '$title' (scheduled for: $scheduledAt)");
        
        try {
            // Send the notification
            $result = $notificationManager->sendNotification($notificationId);
            
            if ($result['success']) {
                $successful++;
                logMessage("✓ Successfully sent notification ID: $notificationId to " . $result['sent_count'] . " users");
                
                $results[] = [
                    'id' => $notificationId,
                    'title' => $title,
                    'status' => 'sent',
                    'sent_count' => $result['sent_count'],
                    'failed_count' => $result['failed_count'] ?? 0
                ];
            } else {
                $failed++;
                logMessage("✗ Failed to send notification ID: $notificationId - " . $result['message']);
                
                $results[] = [
                    'id' => $notificationId,
                    'title' => $title,
                    'status' => 'failed',
                    'error' => $result['message']
                ];
            }
            
        } catch (Exception $e) {
            $failed++;
            $errorMessage = "Exception sending notification ID: $notificationId - " . $e->getMessage();
            logMessage("✗ $errorMessage");
            
            $results[] = [
                'id' => $notificationId,
                'title' => $title,
                'status' => 'error',
                'error' => $errorMessage
            ];
        }
        
        $processed++;
        
        // Small delay between notifications to avoid overwhelming the system
        usleep(100000); // 0.1 second delay
    }
    
    logMessage("=== Processing Complete ===");
    logMessage("Total processed: $processed");
    logMessage("Successful: $successful");
    logMessage("Failed: $failed");
    
    // Send response
    sendResponse([
        'success' => true,
        'message' => "Processed $processed scheduled notifications",
        'processed' => $processed,
        'successful' => $successful,
        'failed' => $failed,
        'results' => $results
    ]);
    
} catch (Exception $e) {
    $errorMessage = "Fatal error in scheduled notification processor: " . $e->getMessage();
    logMessage("❌ $errorMessage");
    logMessage("Stack trace: " . $e->getTraceAsString());
    
    sendResponse([
        'success' => false,
        'message' => $errorMessage,
        'processed' => 0
    ]);
}

logMessage("=== Scheduled Notification Processor Ended ===\n");
?>
