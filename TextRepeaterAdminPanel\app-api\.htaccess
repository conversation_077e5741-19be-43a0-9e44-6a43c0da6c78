# .htaccess temporarily disabled due to server configuration issues
# Use index.php?path=endpoint instead of clean URLs

# RewriteEngine On

# Handle CORS preflight requests
# RewriteCond %{REQUEST_METHOD} OPTIONS
# RewriteRule ^(.*)$ index.php [QSA,L]

# Route all requests to index.php with path parameter
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^(.*)$ index.php?path=$1 [QSA,L]

# Security headers
# Header always set X-Content-Type-Options nosniff
# Header always set X-Frame-Options DENY
# Header always set X-XSS-Protection "1; mode=block"


# Cache control for static files
# <FilesMatch "\.(css|js|png|jpg|jpeg|gif|ico|svg)$">
#     ExpiresActive On
#     ExpiresDefault "access plus 1 month"
# </FilesMatch>

# Prevent access to sensitive files
# <FilesMatch "\.(log|sql|md)$">
#     Order allow,deny
#     Deny from all
# </FilesMatch>
