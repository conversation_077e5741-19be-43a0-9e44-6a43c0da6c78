package com.anginatech.textrepeater;

import android.app.Activity;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.media.RingtoneManager;
import android.net.Uri;
import android.os.Build;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationManagerCompat;

import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;
import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

import org.json.JSONObject;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

/**
 * Firebase Cloud Messaging Service for Text Repeater
 */
public class MyFirebaseMessagingService extends FirebaseMessagingService {

    private static final String TAG = "FCMService";
    private static final String CHANNEL_ID = "text_repeater_notifications";
    private static final String CHANNEL_NAME = "Text Repeater Notifications";
    private static final String PREFS_NAME = "FCMPrefs";
    private static final String KEY_FCM_TOKEN = "fcm_token";

    @Override
    public void onCreate() {
        super.onCreate();
        createNotificationChannel();
    }

    @Override
    public void onNewToken(@NonNull String token) {
        Log.d(TAG, "Refreshed token: " + token);

        // Save token locally
        saveTokenLocally(token);

        // Send token to server
        sendTokenToServer(token);
    }

    @Override
    public void onMessageReceived(@NonNull RemoteMessage remoteMessage) {
        Log.d(TAG, "From: " + remoteMessage.getFrom());

        // Check if message contains a data payload
        if (remoteMessage.getData().size() > 0) {
            Log.d(TAG, "Message data payload: " + remoteMessage.getData());

            // Handle data payload
            handleDataPayload(remoteMessage.getData());
        }

        // Check if message contains a notification payload
        if (remoteMessage.getNotification() != null) {
            Log.d(TAG, "Message Notification Body: " + remoteMessage.getNotification().getBody());

            // Show notification
            showNotification(
                remoteMessage.getNotification().getTitle(),
                remoteMessage.getNotification().getBody(),
                remoteMessage.getNotification().getImageUrl(),
                remoteMessage.getData()
            );
        }

        // Track notification delivery
        trackNotificationDelivery(remoteMessage);
    }

    /**
     * Handle data payload from FCM
     */
    private void handleDataPayload(Map<String, String> data) {
        String notificationId = data.get("notification_id");
        String actionUrl = data.get("action_url");
        String imageUrl = data.get("image_url");

        Log.d(TAG, "Handling data payload - ID: " + notificationId + ", Action: " + actionUrl);

        // You can add custom logic here based on the data payload
    }

    /**
     * Show notification to user
     */
    private void showNotification(String title, String body, Uri imageUri, Map<String, String> data) {
        // Check if notification permission is granted
        if (!NotificationPermissionHelper.isNotificationPermissionGranted(this)) {
            Log.w(TAG, "Notification permission not granted, cannot show notification");
            return;
        }
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);

        // Add notification data to intent
        if (data != null) {
            for (Map.Entry<String, String> entry : data.entrySet()) {
                intent.putExtra(entry.getKey(), entry.getValue());
            }
            // Add a flag to track that this was opened from notification
            intent.putExtra("opened_from_notification", true);
        }

        PendingIntent pendingIntent = PendingIntent.getActivity(
            this,
            0,
            intent,
            PendingIntent.FLAG_ONE_SHOT | PendingIntent.FLAG_IMMUTABLE
        );

        Uri defaultSoundUri = RingtoneManager.getDefaultUri(RingtoneManager.TYPE_NOTIFICATION);

        NotificationCompat.Builder notificationBuilder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_launcher_foreground)
                .setContentTitle(title)
                .setContentText(body)
                .setAutoCancel(true)
                .setSound(defaultSoundUri)
                .setContentIntent(pendingIntent)
                .setPriority(NotificationCompat.PRIORITY_HIGH)
                .setDefaults(NotificationCompat.DEFAULT_ALL);

        // Load and set large image if available
        if (imageUri != null) {
            Bitmap bitmap = getBitmapFromUrl(imageUri.toString());
            if (bitmap != null) {
                notificationBuilder.setLargeIcon(bitmap)
                                 .setStyle(new NotificationCompat.BigPictureStyle()
                                         .bigPicture(bitmap)
                                         .bigLargeIcon((Bitmap) null));
            }
        }

        NotificationManager notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        // Generate unique notification ID
        int notificationId = (int) System.currentTimeMillis();

        notificationManager.notify(notificationId, notificationBuilder.build());

        Log.d(TAG, "Notification shown with ID: " + notificationId);
    }

    /**
     * Create notification channel for Android O and above
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            );
            channel.setDescription("Notifications from Text Repeater app");
            channel.enableLights(true);
            channel.enableVibration(true);

            NotificationManager notificationManager = getSystemService(NotificationManager.class);
            notificationManager.createNotificationChannel(channel);

            Log.d(TAG, "Notification channel created");
        }
    }

    /**
     * Save FCM token locally
     */
    private void saveTokenLocally(String token) {
        SharedPreferences prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(KEY_FCM_TOKEN, token);
        editor.apply();

        Log.d(TAG, "Token saved locally");
    }

    /**
     * Send FCM token to server
     */
    private void sendTokenToServer(String token) {
        String deviceId = getUniqueDeviceId();
        String apiUrl = Config.buildApiUrl(Config.ENDPOINT_USER_REGISTER);

        try {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("device_id", deviceId);
            jsonBody.put("fcm_token", token);

            JsonObjectRequest request = new JsonObjectRequest(
                Request.Method.POST,
                apiUrl,
                jsonBody,
                response -> Log.d(TAG, "Token sent to server successfully"),
                error -> Log.e(TAG, "Error sending token to server: " + error.toString())
            );

            RequestQueue queue = Volley.newRequestQueue(this);
            queue.add(request);

        } catch (Exception e) {
            Log.e(TAG, "Error creating token request: " + e.getMessage());
        }
    }

    /**
     * Track notification delivery to server
     */
    private void trackNotificationDelivery(RemoteMessage remoteMessage) {
        Map<String, String> data = remoteMessage.getData();
        String notificationId = data.get("notification_id");

        if (notificationId != null && !notificationId.isEmpty()) {
            String deviceId = getUniqueDeviceId();
            String apiUrl = Config.buildApiUrl(Config.ENDPOINT_NOTIFICATION_DELIVERED);

            try {
                JSONObject jsonBody = new JSONObject();
                jsonBody.put("notification_id", notificationId);
                jsonBody.put("device_id", deviceId);

                JsonObjectRequest request = new JsonObjectRequest(
                    Request.Method.POST,
                    apiUrl,
                    jsonBody,
                    response -> Log.d(TAG, "Notification delivery tracked successfully"),
                    error -> Log.e(TAG, "Error tracking notification delivery: " + error.toString())
                );

                RequestQueue queue = Volley.newRequestQueue(this);
                queue.add(request);

            } catch (Exception e) {
                Log.e(TAG, "Error creating delivery tracking request: " + e.getMessage());
            }
        }
    }

    /**
     * Get device ID
     */
    private String getUniqueDeviceId() {
        SharedPreferences prefs = getSharedPreferences("AppPrefs", Context.MODE_PRIVATE);
        String deviceId = prefs.getString("device_id", "");

        if (deviceId.isEmpty()) {
            deviceId = android.provider.Settings.Secure.getString(
                getContentResolver(),
                android.provider.Settings.Secure.ANDROID_ID
            );

            // Save device ID
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString("device_id", deviceId);
            editor.apply();
        }

        return deviceId;
    }

    /**
     * Download bitmap from URL
     */
    private Bitmap getBitmapFromUrl(String imageUrl) {
        try {
            URL url = new URL(imageUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setDoInput(true);
            connection.connect();
            InputStream input = connection.getInputStream();
            return BitmapFactory.decodeStream(input);
        } catch (IOException e) {
            Log.e(TAG, "Error loading image: " + e.getMessage());
            return null;
        }
    }

    /**
     * Get saved FCM token
     */
    public static String getSavedToken(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        return prefs.getString(KEY_FCM_TOKEN, "");
    }

    /**
     * Refresh FCM token (called when permission is granted)
     */
    public static void refreshFCMToken(Context context) {
        FirebaseMessaging.getInstance().getToken()
            .addOnCompleteListener(task -> {
                if (!task.isSuccessful()) {
                    Log.w(TAG, "Fetching FCM registration token failed", task.getException());
                    return;
                }

                // Get new FCM registration token
                String token = task.getResult();
                Log.d(TAG, "FCM Token refreshed: " + token);

                // Save token locally
                SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
                SharedPreferences.Editor editor = prefs.edit();
                editor.putString(KEY_FCM_TOKEN, token);
                editor.apply();

                // Send token to server
                sendTokenToServerStatic(context, token);
            });
    }

    /**
     * Static method to send token to server
     */
    private static void sendTokenToServerStatic(Context context, String token) {
        SharedPreferences prefs = context.getSharedPreferences("AppPrefs", Context.MODE_PRIVATE);
        String deviceId = prefs.getString("device_id", "");

        if (deviceId.isEmpty()) {
            deviceId = android.provider.Settings.Secure.getString(
                context.getContentResolver(),
                android.provider.Settings.Secure.ANDROID_ID
            );

            // Save device ID
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString("device_id", deviceId);
            editor.apply();
        }

        String apiUrl = Config.buildApiUrl(Config.ENDPOINT_USER_REGISTER);

        try {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("device_id", deviceId);
            jsonBody.put("fcm_token", token);

            JsonObjectRequest request = new JsonObjectRequest(
                Request.Method.POST,
                apiUrl,
                jsonBody,
                response -> Log.d(TAG, "Token sent to server successfully (static)"),
                error -> Log.e(TAG, "Error sending token to server (static): " + error.toString())
            );

            RequestQueue queue = Volley.newRequestQueue(context);
            queue.add(request);

        } catch (Exception e) {
            Log.e(TAG, "Error creating token request (static): " + e.getMessage());
        }
    }

    /**
     * Track notification click (call this from MainActivity when opened from notification)
     */
    public static void trackNotificationClick(Context context, String notificationId) {
        if (notificationId == null || notificationId.isEmpty()) {
            return;
        }

        SharedPreferences prefs = context.getSharedPreferences("AppPrefs", Context.MODE_PRIVATE);
        String deviceId = prefs.getString("device_id", "");

        if (deviceId.isEmpty()) {
            deviceId = android.provider.Settings.Secure.getString(
                context.getContentResolver(),
                android.provider.Settings.Secure.ANDROID_ID
            );
        }

        String apiUrl = Config.buildApiUrl(Config.ENDPOINT_NOTIFICATION_CLICKED);

        try {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("notification_id", notificationId);
            jsonBody.put("device_id", deviceId);

            JsonObjectRequest request = new JsonObjectRequest(
                Request.Method.POST,
                apiUrl,
                jsonBody,
                response -> Log.d(TAG, "Notification click tracked successfully"),
                error -> Log.e(TAG, "Error tracking notification click: " + error.toString())
            );

            RequestQueue queue = Volley.newRequestQueue(context);
            queue.add(request);

        } catch (Exception e) {
            Log.e(TAG, "Error creating click tracking request: " + e.getMessage());
        }
    }

    /**
     * Check and request notification permission if needed
     */
    public static void checkAndRequestNotificationPermission(Context context) {
        if (!NotificationPermissionHelper.isNotificationPermissionGranted(context)) {
            Log.d(TAG, "Notification permission not granted");

            // If context is an Activity, we can request permission
            if (context instanceof Activity) {
                NotificationPermissionHelper.requestNotificationPermission((Activity) context);
            } else {
                Log.d(TAG, "Context is not Activity, cannot request permission directly");
            }
        } else {
            Log.d(TAG, "Notification permission already granted");
            // Refresh token to ensure server has latest
            refreshFCMToken(context);
        }
    }
}
