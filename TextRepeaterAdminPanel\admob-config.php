<?php
$page_title = 'AdMob Configuration';
require_once 'includes/header.php';
require_once 'classes/AdMobManager.php';

$admobManager = new AdMobManager();
$current_user = $auth->getCurrentUser();

$success_message = '';
$error_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'create':
        case 'update':
            $data = [
                'config_name' => trim($_POST['config_name'] ?? ''),
                'app_id' => trim($_POST['app_id'] ?? ''),
                'banner_ad_unit_id' => trim($_POST['banner_ad_unit_id'] ?? ''),
                'interstitial_ad_unit_id' => trim($_POST['interstitial_ad_unit_id'] ?? ''),
                'app_open_ad_unit_id' => trim($_POST['app_open_ad_unit_id'] ?? ''),
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'test_mode' => isset($_POST['test_mode']) ? 1 : 0,
                'ad_frequency_minutes' => intval($_POST['ad_frequency_minutes'] ?? 5),
                'max_ads_per_session' => intval($_POST['max_ads_per_session'] ?? 10),
                'created_by' => $current_user['id']
            ];
            
            // Validation
            if (empty($data['config_name'])) {
                $error_message = 'Configuration name is required.';
            } elseif (!$admobManager->validateAppId($data['app_id'])) {
                $error_message = 'Invalid AdMob App ID format.';
            } elseif (!empty($data['banner_ad_unit_id']) && !$admobManager->validateAdUnitId($data['banner_ad_unit_id'])) {
                $error_message = 'Invalid Banner Ad Unit ID format.';
            } elseif (!empty($data['interstitial_ad_unit_id']) && !$admobManager->validateAdUnitId($data['interstitial_ad_unit_id'])) {
                $error_message = 'Invalid Interstitial Ad Unit ID format.';
            } elseif (!empty($data['app_open_ad_unit_id']) && !$admobManager->validateAdUnitId($data['app_open_ad_unit_id'])) {
                $error_message = 'Invalid App Open Ad Unit ID format.';
            } else {
                if ($action == 'create') {
                    $result = $admobManager->createConfig($data);
                } else {
                    $config_id = intval($_POST['config_id']);
                    $result = $admobManager->updateConfig($config_id, $data);
                }
                
                if ($result['success']) {
                    $success_message = $result['message'];
                } else {
                    $error_message = $result['message'];
                }
            }
            break;
            
        case 'activate':
            $config_id = intval($_POST['config_id']);
            $result = $admobManager->activateConfig($config_id);
            
            if ($result['success']) {
                $success_message = $result['message'];
            } else {
                $error_message = $result['message'];
            }
            break;
            
        case 'delete':
            $config_id = intval($_POST['config_id']);
            $result = $admobManager->deleteConfig($config_id);
            
            if ($result['success']) {
                $success_message = $result['message'];
            } else {
                $error_message = $result['message'];
            }
            break;
    }
}

// Get all configurations
$configs = $admobManager->getAllConfigs();
$active_config = $admobManager->getActiveConfig();
?>

<!-- AdMob Configuration Content -->
<?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Current Active Configuration -->
<?php if ($active_config): ?>
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-check-circle text-success me-2"></i>
            Active Configuration: <?php echo htmlspecialchars($active_config['config_name']); ?>
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <p><strong>App ID:</strong> <?php echo htmlspecialchars($active_config['app_id']); ?></p>
                <p><strong>Banner Ad Unit:</strong> <?php echo htmlspecialchars($active_config['banner_ad_unit_id'] ?: 'Not set'); ?></p>
                <p><strong>Interstitial Ad Unit:</strong> <?php echo htmlspecialchars($active_config['interstitial_ad_unit_id'] ?: 'Not set'); ?></p>
            </div>
            <div class="col-md-6">
                <p><strong>App Open Ad Unit:</strong> <?php echo htmlspecialchars($active_config['app_open_ad_unit_id'] ?: 'Not set'); ?></p>
                <p><strong>Test Mode:</strong> 
                    <span class="badge bg-<?php echo $active_config['test_mode'] ? 'warning' : 'success'; ?>">
                        <?php echo $active_config['test_mode'] ? 'Enabled' : 'Disabled'; ?>
                    </span>
                </p>
                <p><strong>Ad Frequency:</strong> <?php echo $active_config['ad_frequency_minutes']; ?> minutes</p>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Add New Configuration -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-plus me-2"></i>
            Add New Configuration
        </h5>
    </div>
    <div class="card-body">
        <form method="POST" action="">
            <input type="hidden" name="action" value="create">
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="config_name" class="form-label">Configuration Name *</label>
                        <input type="text" class="form-control" id="config_name" name="config_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="app_id" class="form-label">AdMob App ID *</label>
                        <input type="text" class="form-control" id="app_id" name="app_id" 
                               placeholder="ca-app-pub-XXXXXXXXXXXXXXXX~XXXXXXXXXX" required>
                        <div class="form-text">Format: ca-app-pub-XXXXXXXXXXXXXXXX~XXXXXXXXXX</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="banner_ad_unit_id" class="form-label">Banner Ad Unit ID</label>
                        <input type="text" class="form-control" id="banner_ad_unit_id" name="banner_ad_unit_id" 
                               placeholder="ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX">
                    </div>
                    
                    <div class="mb-3">
                        <label for="interstitial_ad_unit_id" class="form-label">Interstitial Ad Unit ID</label>
                        <input type="text" class="form-control" id="interstitial_ad_unit_id" name="interstitial_ad_unit_id" 
                               placeholder="ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="app_open_ad_unit_id" class="form-label">App Open Ad Unit ID</label>
                        <input type="text" class="form-control" id="app_open_ad_unit_id" name="app_open_ad_unit_id" 
                               placeholder="ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX">
                    </div>
                    
                    <div class="mb-3">
                        <label for="ad_frequency_minutes" class="form-label">Ad Frequency (minutes)</label>
                        <input type="number" class="form-control" id="ad_frequency_minutes" name="ad_frequency_minutes" 
                               value="5" min="1" max="60">
                    </div>
                    
                    <div class="mb-3">
                        <label for="max_ads_per_session" class="form-label">Max Ads Per Session</label>
                        <input type="number" class="form-control" id="max_ads_per_session" name="max_ads_per_session" 
                               value="10" min="1" max="100">
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="test_mode" name="test_mode">
                            <label class="form-check-label" for="test_mode">
                                Enable Test Mode
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active">
                            <label class="form-check-label" for="is_active">
                                Set as Active Configuration
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-save me-2"></i>Save Configuration
            </button>
        </form>
    </div>
</div>

<!-- Existing Configurations -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list me-2"></i>
            All Configurations
        </h5>
    </div>
    <div class="card-body">
        <?php if (empty($configs)): ?>
            <p class="text-muted">No configurations found.</p>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>App ID</th>
                            <th>Status</th>
                            <th>Test Mode</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($configs as $config): ?>
                            <tr>
                                <td>
                                    <strong><?php echo htmlspecialchars($config['config_name']); ?></strong>
                                    <?php if ($config['is_active']): ?>
                                        <span class="badge bg-success ms-2">Active</span>
                                    <?php endif; ?>
                                </td>
                                <td><code><?php echo htmlspecialchars($config['app_id']); ?></code></td>
                                <td>
                                    <span class="badge bg-<?php echo $config['is_active'] ? 'success' : 'secondary'; ?>">
                                        <?php echo $config['is_active'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-<?php echo $config['test_mode'] ? 'warning' : 'success'; ?>">
                                        <?php echo $config['test_mode'] ? 'Test' : 'Live'; ?>
                                    </span>
                                </td>
                                <td><?php echo date('M j, Y', strtotime($config['created_at'])); ?></td>
                                <td>
                                    <?php if (!$config['is_active']): ?>
                                        <form method="POST" class="d-inline">
                                            <input type="hidden" name="action" value="activate">
                                            <input type="hidden" name="config_id" value="<?php echo $config['id']; ?>">
                                            <button type="submit" class="btn btn-sm btn-success" 
                                                    onclick="return confirm('Activate this configuration?')">
                                                <i class="fas fa-check"></i>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    
                                    <button type="button" class="btn btn-sm btn-primary" 
                                            onclick="editConfig(<?php echo htmlspecialchars(json_encode($config)); ?>)">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    
                                    <form method="POST" class="d-inline">
                                        <input type="hidden" name="action" value="delete">
                                        <input type="hidden" name="config_id" value="<?php echo $config['id']; ?>">
                                        <button type="submit" class="btn btn-sm btn-danger" 
                                                onclick="return confirm('Delete this configuration?')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php
$page_scripts = "
<script>
function editConfig(config) {
    // Populate form with config data
    document.getElementById('config_name').value = config.config_name;
    document.getElementById('app_id').value = config.app_id;
    document.getElementById('banner_ad_unit_id').value = config.banner_ad_unit_id || '';
    document.getElementById('interstitial_ad_unit_id').value = config.interstitial_ad_unit_id || '';
    document.getElementById('app_open_ad_unit_id').value = config.app_open_ad_unit_id || '';
    document.getElementById('ad_frequency_minutes').value = config.ad_frequency_minutes;
    document.getElementById('max_ads_per_session').value = config.max_ads_per_session;
    document.getElementById('test_mode').checked = config.test_mode == 1;
    document.getElementById('is_active').checked = config.is_active == 1;
    
    // Change form action to update
    const form = document.querySelector('form');
    const actionInput = form.querySelector('input[name=\"action\"]');
    actionInput.value = 'update';
    
    // Add config ID
    let configIdInput = form.querySelector('input[name=\"config_id\"]');
    if (!configIdInput) {
        configIdInput = document.createElement('input');
        configIdInput.type = 'hidden';
        configIdInput.name = 'config_id';
        form.appendChild(configIdInput);
    }
    configIdInput.value = config.id;
    
    // Change button text
    const submitBtn = form.querySelector('button[type=\"submit\"]');
    submitBtn.innerHTML = '<i class=\"fas fa-save me-2\"></i>Update Configuration';
    
    // Scroll to form
    form.scrollIntoView({ behavior: 'smooth' });
}
</script>
";

require_once 'includes/footer.php';
?>
