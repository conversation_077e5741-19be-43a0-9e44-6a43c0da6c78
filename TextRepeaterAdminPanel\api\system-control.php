<?php
/**
 * System Control API
 * Handles starting/stopping the automatic notification processor
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Include required files
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/NotificationManager.php';

/**
 * Send JSON response
 */
function sendResponse($success, $message, $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => date('Y-m-d H:i:s')
    ]);
    exit;
}

/**
 * Check if PowerShell process is running
 */
function isProcessorRunning() {
    // Check for PowerShell process running our script
    $command = 'tasklist /FI "IMAGENAME eq powershell.exe" /FO CSV | findstr "auto-process-notifications"';
    $output = shell_exec($command);
    return !empty(trim($output));
}

/**
 * Start the automatic processor
 */
function startProcessor() {
    try {
        $scriptPath = realpath(__DIR__ . '/../auto-process-notifications.ps1');
        if (!$scriptPath || !file_exists($scriptPath)) {
            return [
                'success' => false,
                'message' => 'PowerShell script not found'
            ];
        }

        // Start PowerShell script in background
        $command = 'start /B powershell -WindowStyle Hidden -ExecutionPolicy Bypass -File "' . $scriptPath . '"';
        $result = shell_exec($command);
        
        // Give it a moment to start
        sleep(2);
        
        // Check if it's running
        if (isProcessorRunning()) {
            return [
                'success' => true,
                'message' => 'Auto-processor started successfully'
            ];
        } else {
            return [
                'success' => false,
                'message' => 'Failed to start auto-processor'
            ];
        }
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Error starting processor: ' . $e->getMessage()
        ];
    }
}

/**
 * Stop the automatic processor
 */
function stopProcessor() {
    try {
        // Kill PowerShell processes running our script
        $command = 'taskkill /F /FI "IMAGENAME eq powershell.exe" /FI "WINDOWTITLE eq *auto-process-notifications*"';
        shell_exec($command);
        
        // Alternative: Kill all PowerShell processes (more aggressive)
        // $command = 'taskkill /F /IM powershell.exe';
        // shell_exec($command);
        
        // Give it a moment to stop
        sleep(1);
        
        return [
            'success' => true,
            'message' => 'Auto-processor stopped successfully'
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Error stopping processor: ' . $e->getMessage()
        ];
    }
}

/**
 * Check system status
 */
function checkStatus() {
    try {
        $notificationManager = new NotificationManager();
        
        // Check if processor is running
        $isRunning = isProcessorRunning();
        
        // Get due notifications count
        $scheduledNotifications = $notificationManager->getScheduledNotifications();
        $dueCount = count($scheduledNotifications);
        
        // Get total scheduled count
        $database = new Database();
        $conn = $database->getConnection();
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM notifications WHERE status = 'scheduled'");
        $stmt->execute();
        $totalScheduled = $stmt->fetch()['count'];
        
        return [
            'success' => true,
            'message' => 'Status checked successfully',
            'data' => [
                'is_running' => $isRunning,
                'due_notifications' => $dueCount,
                'total_scheduled' => $totalScheduled,
                'last_check' => date('Y-m-d H:i:s')
            ]
        ];
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Error checking status: ' . $e->getMessage()
        ];
    }
}

// Main logic
try {
    // Get request data
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data || !isset($data['action'])) {
        sendResponse(false, 'Invalid request data');
    }
    
    $action = $data['action'];
    
    switch ($action) {
        case 'start_processor':
            $result = startProcessor();
            sendResponse($result['success'], $result['message']);
            break;
            
        case 'stop_processor':
            $result = stopProcessor();
            sendResponse($result['success'], $result['message']);
            break;
            
        case 'check_status':
            $result = checkStatus();
            sendResponse($result['success'], $result['message'], $result['data'] ?? null);
            break;
            
        default:
            sendResponse(false, 'Unknown action: ' . $action);
    }
    
} catch (Exception $e) {
    sendResponse(false, 'Server error: ' . $e->getMessage());
}
?>
