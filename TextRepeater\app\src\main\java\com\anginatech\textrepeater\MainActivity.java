package com.anginatech.textrepeater;



import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Configuration;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsetsController;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.material.floatingactionbutton.FloatingActionButton;


public class MainActivity extends AppCompatActivity {

    TextView textSeeAll;
    private static final int UPDATE_REQUEST_CODE = 22;
    private InAppUpdate inAppUpdate;
    private ViewGroup adContainer;
    private SyncManager syncManager;

    // Click debouncing variables
    private static final long CLICK_DEBOUNCE_TIME = 1000; // 1 second
    private long lastClickTime = 0;
    private boolean isNavigating = false;
    private Handler mainHandler;

    // Activity navigation enum for better type safety
    private enum NavigationTarget {
        SETTINGS(Settings_Activity.class),
        TEXT_REPEAT(Text_Repeat.class),
        MESSAGE(Message_Activity.class),
        TEXT_TO_EMOJI(Text_to_Imoji_Activity.class),
        STYLISH_FONT(Stylish_Font_Activity.class),
        EMOJI_ART(Emoji_Art.class),
        DECORATION_TEXT(Decoration_Text_Activity.class),
        RANDOM_TEXT(Random_Text_Activity.class),
        BLANK_TEXT(Blank_Text_Activity.class);

        private final Class<?> activityClass;

        NavigationTarget(Class<?> activityClass) {
            this.activityClass = activityClass;
        }

        public Class<?> getActivityClass() {
            return activityClass;
        }
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_main);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        // Apply night mode setting immediately for smooth UI
        applyNightModeSettings();

        // Initialize critical UI components first for immediate user interaction
        initializeUIComponents();

        // Initialize modern ads system for seamless experience
        initializeModernAdsSystem();

        ///Initialized the element=====================================================================

        textSeeAll = findViewById(R.id.textSeeAll);
        FloatingActionButton fab = findViewById(R.id.fab);
        fab.setSize(FloatingActionButton.SIZE_NORMAL);

        ConstraintLayout layout_Text_Repeat = findViewById(R.id.layout_Text_Repeat);
        ConstraintLayout layout_message = findViewById(R.id.layout_message);
        ConstraintLayout layout_Text_To_Imoji = findViewById(R.id.layout_Text_To_Imoji);
        ConstraintLayout layout_Stylish_Font = findViewById(R.id.layout_Stylish_Font);
        ConstraintLayout layout_Stylish_Number = findViewById(R.id.layout_Stylish_Number);
        ConstraintLayout layout_Decoration_Text = findViewById(R.id.layout_Decoration_Text);
        ConstraintLayout layout_Random_Text = findViewById(R.id.layout_Random_Text);
        ConstraintLayout layout_Blank_Text = findViewById(R.id.layout_Blank_Text);



        ///Initialized the element=====================================================================


        inAppUpdate = new InAppUpdate(MainActivity.this);
        inAppUpdate.checkForAppUpdate();

        // Initialize handler for smooth UI operations
        mainHandler = new Handler(Looper.getMainLooper());



        textSeeAll.setOnClickListener(createOptimizedClickListener(v ->
            navigateToActivity(Message_Activity.class)
        ));

        fab.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Settings_Activity.class)
        ));

        layout_Text_Repeat.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Text_Repeat.class)
        ));

        layout_message.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Message_Activity.class)
        ));

        layout_Text_To_Imoji.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Text_to_Imoji_Activity.class)
        ));

        layout_Stylish_Font.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Stylish_Font_Activity.class)
        ));

        layout_Stylish_Number.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Emoji_Art.class)
        ));

        layout_Decoration_Text.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Decoration_Text_Activity.class)
        ));

        layout_Random_Text.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Random_Text_Activity.class)
        ));

        layout_Blank_Text.setOnClickListener(createOptimizedClickListener(v ->
            navigateWithAd(Blank_Text_Activity.class)
        ));

    }

    /**
     * Apply night mode settings immediately for smooth UI
     */
    private void applyNightModeSettings() {
        SharedPreferences sharedPreferences = getSharedPreferences("nightModePrefs", MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }
    }

    /**
     * Initialize critical UI components first for immediate user interaction
     */
    private void initializeUIComponents() {
        // Initialize views immediately for user interaction
        adContainer = findViewById(R.id.ad_container);

        // Initialize other critical components
        inAppUpdate = new InAppUpdate(MainActivity.this);
        inAppUpdate.checkForAppUpdate();

        // Initialize handler for smooth UI operations
        mainHandler = new Handler(Looper.getMainLooper());

        // Initialize sync manager for background data synchronization
        syncManager = SyncManager.getInstance(this);

        // Handle notification click tracking
        handleNotificationClick();
    }

    /**
     * Initialize modern ads system for seamless user experience
     */
    private void initializeModernAdsSystem() {
        // Initialize consent form for GDPR compliance
        initMobileAds.requestConsentForm(MainActivity.this);

        // Initialize modern ads system with background preloading
        AdsHelper.initializeAds(MainActivity.this);

        // Load banner ad instantly if available
        if (adContainer != null) {
            AdsHelper.loadBannerAd(this, adContainer);
        }

        Log.d("MainActivity", "Modern ads system initialized for seamless experience");
    }



    /**
     * Create optimized click listener with immediate visual feedback and debouncing
     */
    private View.OnClickListener createOptimizedClickListener(View.OnClickListener action) {
        return new View.OnClickListener() {
            private long lastClickTime = 0;
            private static final long DEBOUNCE_TIME = 500; // 500ms debounce

            @Override
            public void onClick(View v) {
                long currentTime = System.currentTimeMillis();

                // Debounce rapid clicks
                if (currentTime - lastClickTime < DEBOUNCE_TIME) {
                    return;
                }
                lastClickTime = currentTime;

                // Immediate visual feedback
                v.setAlpha(0.7f);
                v.animate().alpha(1.0f).setDuration(150);

                // Execute action asynchronously to prevent UI blocking
                mainHandler.post(() -> {
                    try {
                        action.onClick(v);
                    } catch (Exception e) {
                        Log.e("MainActivity", "Error in click action: " + e.getMessage());
                    }
                });
            }
        };
    }

    /**
     * Navigate with ad in an optimized way
     */
    private void navigateWithAd(Class<?> targetActivity) {
        if (AdsHelper.isInterstitialAdLoaded()) {
            AdsHelper.showInterstitialAd(MainActivity.this, new FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    // Load the next interstitial ad asynchronously
                    new Thread(() -> AdsHelper.loadInterstitialAd(MainActivity.this)).start();

                    // Navigate to target activity
                    navigateToActivity(targetActivity);
                }

                @Override
                public void onAdFailedToShowFullScreenContent(AdError adError) {
                    // Ad failed to show, just navigate to target activity
                    navigateToActivity(targetActivity);
                }
            });
        } else {
            // If ad not loaded, just navigate to target activity
            navigateToActivity(targetActivity);
            // Load the next interstitial ad for future use
            new Thread(() -> AdsHelper.loadInterstitialAd(MainActivity.this)).start();
        }
    }

    /**
     * Navigate to activity with smooth transition
     */
    private void navigateToActivity(Class<?> targetActivity) {
        try {
            Intent intent = new Intent(MainActivity.this, targetActivity);
            startActivity(intent);

            // Add smooth transition animation
            overridePendingTransition(android.R.anim.fade_in, android.R.anim.fade_out);

            finish();
        } catch (Exception e) {
            Log.e("MainActivity", "Error navigating to activity: " + e.getMessage());
        }
    }

    /**
     * Handle notification click tracking
     */
    private void handleNotificationClick() {
        Intent intent = getIntent();
        if (intent != null && intent.getBooleanExtra("opened_from_notification", false)) {
            String notificationId = intent.getStringExtra("notification_id");
            if (notificationId != null && !notificationId.isEmpty()) {
                MyFirebaseMessagingService.trackNotificationClick(this, notificationId);
            }
        }
    }



    @Override
    protected void onResume() {
        super.onResume();
        inAppUpdate.onResume();

        // Refresh ads for seamless experience when returning to MainActivity
        AdsHelper.refreshAdsOnReturn(this);

        // Trigger background sync if needed
        if (syncManager != null) {
            new Thread(() -> syncManager.performAutoSyncIfNeeded()).start();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        inAppUpdate.onDestroy();
    }







}