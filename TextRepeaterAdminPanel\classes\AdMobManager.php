<?php
// Check if database config is already loaded, if not load it
if (!class_exists('Database')) {
    require_once __DIR__ . '/../config/database.php';
}

class AdMobManager {
    private $conn;
    private $table_name = "admob_config";

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    /**
     * Get all AdMob configurations
     */
    public function getAllConfigs() {
        try {
            $query = "SELECT ac.*, au.full_name as created_by_name
                     FROM " . $this->table_name . " ac
                     LEFT JOIN admin_users au ON ac.created_by = au.id
                     ORDER BY ac.created_at DESC";

            $stmt = $this->conn->prepare($query);
            $stmt->execute();

            return $stmt->fetchAll();
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * Get active configuration
     */
    public function getActiveConfig() {
        try {
            $query = "SELECT * FROM " . $this->table_name . " WHERE is_active = 1 LIMIT 1";
            $stmt = $this->conn->prepare($query);
            $stmt->execute();

            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Get configuration by ID
     */
    public function getConfigById($id) {
        try {
            $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            return $stmt->fetch();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Create new configuration
     */
    public function createConfig($data) {
        try {
            // Deactivate all other configs if this one is set as active
            if ($data['is_active']) {
                $this->deactivateAllConfigs();
            }

            $query = "INSERT INTO " . $this->table_name . "
                     (config_name, app_id, banner_ad_unit_id, interstitial_ad_unit_id,
                      app_open_ad_unit_id, is_active, test_mode, ad_frequency_minutes,
                      max_ads_per_session, created_by)
                     VALUES
                     (:config_name, :app_id, :banner_ad_unit_id, :interstitial_ad_unit_id,
                      :app_open_ad_unit_id, :is_active, :test_mode, :ad_frequency_minutes,
                      :max_ads_per_session, :created_by)";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':config_name', $data['config_name']);
            $stmt->bindParam(':app_id', $data['app_id']);
            $stmt->bindParam(':banner_ad_unit_id', $data['banner_ad_unit_id']);
            $stmt->bindParam(':interstitial_ad_unit_id', $data['interstitial_ad_unit_id']);
            $stmt->bindParam(':app_open_ad_unit_id', $data['app_open_ad_unit_id']);
            $stmt->bindParam(':is_active', $data['is_active'], PDO::PARAM_BOOL);
            $stmt->bindParam(':test_mode', $data['test_mode'], PDO::PARAM_BOOL);
            $stmt->bindParam(':ad_frequency_minutes', $data['ad_frequency_minutes']);
            $stmt->bindParam(':max_ads_per_session', $data['max_ads_per_session']);
            $stmt->bindParam(':created_by', $data['created_by']);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Configuration created successfully',
                    'id' => $this->conn->lastInsertId()
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to create configuration'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error creating configuration: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Update configuration
     */
    public function updateConfig($id, $data) {
        try {
            // Deactivate all other configs if this one is set as active
            if ($data['is_active']) {
                $this->deactivateAllConfigs();
            }

            $query = "UPDATE " . $this->table_name . " SET
                     config_name = :config_name,
                     app_id = :app_id,
                     banner_ad_unit_id = :banner_ad_unit_id,
                     interstitial_ad_unit_id = :interstitial_ad_unit_id,
                     app_open_ad_unit_id = :app_open_ad_unit_id,
                     is_active = :is_active,
                     test_mode = :test_mode,
                     ad_frequency_minutes = :ad_frequency_minutes,
                     max_ads_per_session = :max_ads_per_session,
                     updated_at = NOW()
                     WHERE id = :id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':config_name', $data['config_name']);
            $stmt->bindParam(':app_id', $data['app_id']);
            $stmt->bindParam(':banner_ad_unit_id', $data['banner_ad_unit_id']);
            $stmt->bindParam(':interstitial_ad_unit_id', $data['interstitial_ad_unit_id']);
            $stmt->bindParam(':app_open_ad_unit_id', $data['app_open_ad_unit_id']);
            $stmt->bindParam(':is_active', $data['is_active'], PDO::PARAM_BOOL);
            $stmt->bindParam(':test_mode', $data['test_mode'], PDO::PARAM_BOOL);
            $stmt->bindParam(':ad_frequency_minutes', $data['ad_frequency_minutes']);
            $stmt->bindParam(':max_ads_per_session', $data['max_ads_per_session']);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Configuration updated successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to update configuration'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error updating configuration: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Delete configuration
     */
    public function deleteConfig($id) {
        try {
            $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Configuration deleted successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to delete configuration'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error deleting configuration: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Activate configuration
     */
    public function activateConfig($id) {
        try {
            // First deactivate all configs
            $this->deactivateAllConfigs();

            // Then activate the specified config
            $query = "UPDATE " . $this->table_name . " SET is_active = 1, updated_at = NOW() WHERE id = :id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':id', $id);

            if ($stmt->execute()) {
                return [
                    'success' => true,
                    'message' => 'Configuration activated successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to activate configuration'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Error activating configuration: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Deactivate all configurations
     */
    private function deactivateAllConfigs() {
        $query = "UPDATE " . $this->table_name . " SET is_active = 0";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
    }

    /**
     * Get configuration for API response
     */
    public function getApiConfig() {
        $config = $this->getActiveConfig();

        if (!$config) {
            return [
                'success' => false,
                'message' => 'No active configuration found'
            ];
        }

        return [
            'success' => true,
            'data' => [
                'app_id' => $config['app_id'],
                'banner_id' => $config['banner_ad_unit_id'],
                'interstitial_id' => $config['interstitial_ad_unit_id'],
                'app_open_id' => $config['app_open_ad_unit_id'],
                'is_active' => $config['is_active'] ? 1 : 0,
                'test_mode' => $config['test_mode'] ? 1 : 0,
                'ad_frequency_minutes' => $config['ad_frequency_minutes'],
                'max_ads_per_session' => $config['max_ads_per_session']
            ]
        ];
    }

    /**
     * Validate ad unit ID format
     */
    public function validateAdUnitId($ad_unit_id, $type = 'general') {
        if (empty($ad_unit_id)) {
            return true; // Allow empty ad unit IDs
        }

        // AdMob ad unit ID format: ca-app-pub-XXXXXXXXXXXXXXXX/XXXXXXXXXX
        $pattern = '/^ca-app-pub-\d{16}\/\d{10}$/';

        if (!preg_match($pattern, $ad_unit_id)) {
            return false;
        }

        return true;
    }

    /**
     * Validate app ID format
     */
    public function validateAppId($app_id) {
        if (empty($app_id)) {
            return false;
        }

        // AdMob app ID format: ca-app-pub-XXXXXXXXXXXXXXXX~XXXXXXXXXX
        $pattern = '/^ca-app-pub-\d{16}~\d{10}$/';

        return preg_match($pattern, $app_id);
    }
}
?>
