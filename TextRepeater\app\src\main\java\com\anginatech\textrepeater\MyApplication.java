package com.anginatech.textrepeater;

import android.app.Application;
import android.util.Log;
import android.widget.Toast;

import com.android.volley.Request;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;
import com.anginatech.textrepeater.models.Category;
import com.anginatech.textrepeater.database.TextRepeaterDatabase;
import com.anginatech.textrepeater.database.repository.RoomDataRepository;
import com.anginatech.textrepeater.repository.TextRepeaterRepository;
import com.google.android.gms.ads.MobileAds;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class MyApplication extends Application {

    // Modern Room Database (replaces old SQLite databases)
    private TextRepeaterDatabase roomDatabase;
    private RoomDataRepository roomRepository;

    private AppOpenAdManager appOpenAdManager;
    private UserRegistrationHelper userRegistrationHelper;
    private TextRepeaterRepository repository;

    @Override
    public void onCreate() {
        super.onCreate();

        // Initialize Mobile Ads SDK (lightweight)
        MobileAds.initialize(this, initializationStatus -> {
            Log.d("MyApplication", "Mobile Ads SDK initialized");
        });

        // Initialize modern Room database (replaces old SQLite databases)
        roomDatabase = TextRepeaterDatabase.getDatabase(this);
        roomRepository = RoomDataRepository.getInstance(this);
        Log.d("MyApplication", "✅ Room database initialized successfully");

        // Initialize App Open Ad Manager (lightweight)
        appOpenAdManager = new AppOpenAdManager(this);

        // Initialize User Registration Helper (lightweight)
        userRegistrationHelper = new UserRegistrationHelper(this);

        // Initialize modern repository (lightweight and fast)
        try {
            repository = TextRepeaterRepository.getInstance(this);
            Log.d("MyApplication", "✅ Modern repository initialized successfully");

            // Run API tests in debug mode
            if (Config.isDebugMode()) {
                com.anginatech.textrepeater.utils.ApiTestHelper.runAllTests(this);
            }
        } catch (Exception e) {
            Log.e("MyApplication", "❌ Error initializing modern repository: " + e.getMessage());
            e.printStackTrace();
        }

        // Initialize sync manager for automatic data synchronization (Room-based)
        initializeSyncManager();

        // Perform heavy operations in background to speed up app startup
        performBackgroundInitialization();
    }

    /**
     * Perform all heavy operations in background to avoid blocking app startup
     * Uses Room database for modern data management
     */
    private void performBackgroundInitialization() {
        new Thread(() -> {
            try {
                // Register user with admin panel (background)
                userRegistrationHelper.registerUser();

                Log.d("MyApplication", "✅ Background initialization completed successfully");
            } catch (Exception e) {
                Log.e("MyApplication", "❌ Error in background initialization: " + e.getMessage());
                e.printStackTrace();
            }
        }).start();

        // Initialize modern ads system on main thread
        new android.os.Handler(android.os.Looper.getMainLooper()).post(() -> {
            try {
                // Initialize modern ads system with background preloading
                AdsHelper.initializeAds(this);

                // Initialize App Open Ad system
                loadAppOpenAdConfig();

                Log.d("MyApplication", "✅ Modern ads system initialized successfully");
            } catch (Exception e) {
                Log.e("MyApplication", "❌ Error in modern ads initialization: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }

    /**
     * Initialize sync manager for automatic data synchronization
     * Uses modern Room database for data storage
     */
    private void initializeSyncManager() {
        new Thread(() -> {
            try {
                SyncManager syncManager = SyncManager.getInstance(this);

                // Perform initial sync if needed
                syncManager.performAutoSyncIfNeeded();

                Log.d("MyApplication", "✅ Room-based sync manager initialized successfully");
            } catch (Exception e) {
                Log.e("MyApplication", "❌ Error initializing sync manager: " + e.getMessage());
                e.printStackTrace();
            }
        }).start();
    }

    public AppOpenAdManager getAppOpenAdManager() {
        return appOpenAdManager;
    }

    public UserRegistrationHelper getUserRegistrationHelper() {
        return userRegistrationHelper;
    }

    public RoomDataRepository getRoomRepository() {
        return roomRepository;
    }

    public TextRepeaterDatabase getRoomDatabase() {
        return roomDatabase;
    }

    public TextRepeaterRepository getRepository() {
        return repository;
    }

    // Note: Data fetching and storage is now handled by the Room-based SyncManager
    // The SyncManager automatically fetches fresh data from the server and stores it in Room database

    // Legacy method removed - data fetching is now handled by Room-based SyncManager

    /**
     * Load App Open Ad configuration from server
     * Simplified version that works with Room database
     */
    private void loadAppOpenAdConfig() {
        try {
            // Initialize App Open Ad with default configuration
            // The actual ad configuration will be loaded by the sync system
            appOpenAdManager.loadAd(this);
            Log.d("MyApplication", "✅ App Open Ad system initialized");
        } catch (Exception e) {
            Log.e("MyApplication", "❌ Error initializing App Open Ad: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
