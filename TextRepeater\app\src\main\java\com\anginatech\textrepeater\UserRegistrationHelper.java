package com.anginatech.textrepeater;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;
import com.google.firebase.messaging.FirebaseMessaging;

import org.json.JSONObject;

/**
 * Helper class to register users with the admin panel
 */
public class UserRegistrationHelper {

    private static final String TAG = "UserRegistration";
    private static final String PREFS_NAME = "UserPrefs";
    private static final String KEY_DEVICE_ID = "device_id";
    private static final String KEY_REGISTERED = "is_registered";

    private Context context;
    private SharedPreferences prefs;

    public UserRegistrationHelper(Context context) {
        this.context = context;
        this.prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
    }

    /**
     * Register user with the admin panel
     */
    public void registerUser() {
        if (isUserRegistered()) {
            Log.d(TAG, "User already registered, updating info");
            updateUserInfo();
            return;
        }

        String deviceId = getDeviceId();

        // Get FCM token
        FirebaseMessaging.getInstance().getToken()
            .addOnCompleteListener(task -> {
                if (!task.isSuccessful()) {
                    Log.w(TAG, "Fetching FCM registration token failed", task.getException());
                    registerUserWithoutToken(deviceId);
                    return;
                }

                // Get new FCM registration token
                String token = task.getResult();
                Log.d(TAG, "FCM Token: " + token);

                registerUserWithToken(deviceId, token);
            });
    }

    /**
     * Register user with FCM token
     */
    private void registerUserWithToken(String deviceId, String fcmToken) {
        String apiUrl = Config.buildApiUrl(Config.ENDPOINT_USER_REGISTER);

        try {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("device_id", deviceId);
            jsonBody.put("fcm_token", fcmToken);
            jsonBody.put("app_version", getAppVersion());
            jsonBody.put("android_version", Build.VERSION.RELEASE);
            jsonBody.put("device_model", Build.MODEL);

            JsonObjectRequest request = new JsonObjectRequest(
                Request.Method.POST,
                apiUrl,
                jsonBody,
                response -> {
                    Log.d(TAG, "User registered successfully: " + response.toString());
                    markUserAsRegistered();
                },
                error -> {
                    Log.e(TAG, "Error registering user: " + error.toString());
                    // Retry without token if registration fails
                    registerUserWithoutToken(deviceId);
                }
            );

            RequestQueue queue = Volley.newRequestQueue(context);
            queue.add(request);

        } catch (Exception e) {
            Log.e(TAG, "Error creating registration request: " + e.getMessage());
        }
    }

    /**
     * Register user without FCM token (fallback)
     */
    private void registerUserWithoutToken(String deviceId) {
        String apiUrl = Config.buildApiUrl(Config.ENDPOINT_USER_REGISTER);

        try {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("device_id", deviceId);
            jsonBody.put("fcm_token", "");
            jsonBody.put("app_version", getAppVersion());
            jsonBody.put("android_version", Build.VERSION.RELEASE);
            jsonBody.put("device_model", Build.MODEL);

            JsonObjectRequest request = new JsonObjectRequest(
                Request.Method.POST,
                apiUrl,
                jsonBody,
                response -> {
                    Log.d(TAG, "User registered successfully (without FCM): " + response.toString());
                    markUserAsRegistered();
                },
                error -> Log.e(TAG, "Error registering user without FCM: " + error.toString())
            );

            RequestQueue queue = Volley.newRequestQueue(context);
            queue.add(request);

        } catch (Exception e) {
            Log.e(TAG, "Error creating registration request without FCM: " + e.getMessage());
        }
    }

    /**
     * Update user information (for existing users)
     */
    public void updateUserInfo() {
        String deviceId = getDeviceId();

        // Get current FCM token
        FirebaseMessaging.getInstance().getToken()
            .addOnCompleteListener(task -> {
                if (!task.isSuccessful()) {
                    Log.w(TAG, "Fetching FCM registration token failed", task.getException());
                    return;
                }

                String token = task.getResult();
                updateUserWithToken(deviceId, token);
            });
    }

    /**
     * Update user with new FCM token
     */
    private void updateUserWithToken(String deviceId, String fcmToken) {
        String updateUrl = Config.buildApiUrl(Config.ENDPOINT_USER_UPDATE);

        try {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("device_id", deviceId);
            jsonBody.put("fcm_token", fcmToken);
            jsonBody.put("app_version", getAppVersion());
            jsonBody.put("android_version", Build.VERSION.RELEASE);
            jsonBody.put("device_model", Build.MODEL);

            JsonObjectRequest request = new JsonObjectRequest(
                Request.Method.POST,
                updateUrl,
                jsonBody,
                response -> Log.d(TAG, "User info updated successfully: " + response.toString()),
                error -> Log.e(TAG, "Error updating user info: " + error.toString())
            );

            RequestQueue queue = Volley.newRequestQueue(context);
            queue.add(request);

        } catch (Exception e) {
            Log.e(TAG, "Error creating update request: " + e.getMessage());
        }
    }

    /**
     * Get unique device ID
     */
    private String getDeviceId() {
        String deviceId = prefs.getString(KEY_DEVICE_ID, "");

        if (deviceId.isEmpty()) {
            deviceId = Settings.Secure.getString(
                context.getContentResolver(),
                Settings.Secure.ANDROID_ID
            );

            // Save device ID for future use
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(KEY_DEVICE_ID, deviceId);
            editor.apply();
        }

        return deviceId;
    }

    /**
     * Get app version
     */
    private String getAppVersion() {
        try {
            return context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), 0)
                    .versionName;
        } catch (Exception e) {
            return "Unknown";
        }
    }

    /**
     * Check if user is already registered
     */
    private boolean isUserRegistered() {
        return prefs.getBoolean(KEY_REGISTERED, false);
    }

    /**
     * Mark user as registered
     */
    private void markUserAsRegistered() {
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_REGISTERED, true);
        editor.apply();
    }

    /**
     * Get saved device ID
     */
    public String getSavedDeviceId() {
        return prefs.getString(KEY_DEVICE_ID, "");
    }

    /**
     * Track ad event to server
     */
    public void trackAdEvent(String adType, String eventType, String adUnitId) {
        String trackUrl = Config.buildApiUrl(Config.ENDPOINT_ADS_TRACK);
        String deviceId = getDeviceId();

        try {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("device_id", deviceId);
            jsonBody.put("ad_type", adType);
            jsonBody.put("event_type", eventType);
            jsonBody.put("ad_unit_id", adUnitId);
            jsonBody.put("app_version", getAppVersion());
            jsonBody.put("session_id", generateSessionId());

            JsonObjectRequest request = new JsonObjectRequest(
                Request.Method.POST,
                trackUrl,
                jsonBody,
                response -> Log.d(TAG, "Ad event tracked: " + response.toString()),
                error -> Log.e(TAG, "Error tracking ad event: " + error.toString())
            );

            RequestQueue queue = Volley.newRequestQueue(context);
            queue.add(request);

        } catch (Exception e) {
            Log.e(TAG, "Error creating ad tracking request: " + e.getMessage());
        }
    }

    /**
     * Generate session ID
     */
    private String generateSessionId() {
        return "session_" + System.currentTimeMillis();
    }

    /**
     * Track notification click
     */
    public void trackNotificationClick(String notificationId) {
        String clickUrl = Config.buildApiUrl(Config.ENDPOINT_NOTIFICATION_CLICKED);
        String deviceId = getDeviceId();

        try {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("notification_id", notificationId);
            jsonBody.put("device_id", deviceId);

            JsonObjectRequest request = new JsonObjectRequest(
                Request.Method.POST,
                clickUrl,
                jsonBody,
                response -> Log.d(TAG, "Notification click tracked: " + response.toString()),
                error -> Log.e(TAG, "Error tracking notification click: " + error.toString())
            );

            RequestQueue queue = Volley.newRequestQueue(context);
            queue.add(request);

        } catch (Exception e) {
            Log.e(TAG, "Error creating notification click tracking request: " + e.getMessage());
        }
    }
}
