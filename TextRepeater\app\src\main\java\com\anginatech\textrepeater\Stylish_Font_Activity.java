package com.anginatech.textrepeater;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.MaterialToolbar;

import java.util.ArrayList;
import java.util.List;

public class Stylish_Font_Activity extends AppCompatActivity {


    MaterialToolbar stylish_MaterialToolbar;

    private RecyclerView stylish_RecyclerView;
    private StylishTextAdapter adapter;
    private List<String> stylishTexts;

    EditText editStylish_Text;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_stylish_font);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        SharedPreferences sharedPreferences = getSharedPreferences("nightModePrefs", MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }

        stylish_MaterialToolbar = findViewById(R.id.stylish_MaterialToolbar);
        editStylish_Text = findViewById(R.id.editStylish_Text);



        stylish_MaterialToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(Stylish_Font_Activity.this, MainActivity.class));
                finish();
            }
        });
        stylish_MaterialToolbar.setTitleTextAppearance(Stylish_Font_Activity.this,R.style.RobotoBoldTextAppearance);

        stylish_RecyclerView = findViewById(R.id.stylish_RecyclerView);

        stylish_RecyclerView.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        stylish_RecyclerView.setHasFixedSize(true);
        stylishTexts = new ArrayList<>();
        adapter = new StylishTextAdapter(Stylish_Font_Activity.this,stylishTexts);
        stylish_RecyclerView.setAdapter(adapter);

        stylish_MaterialToolbar.setOnMenuItemClickListener(new Toolbar.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {

                if (item.getItemId()==R.id.clear_Data) {
                    editStylish_Text.setText("");
                }


                return true;
            }
        });



        editStylish_Text.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                generateStylishTexts(s.toString());
            }

            @Override
            public void afterTextChanged(Editable s) {

            }
        });



    }





    private void generateStylishTexts(String text) {
        stylishTexts.clear();

        if (text.isEmpty()) {
            stylishTexts.add("Please type something...");
        } else {
            stylishTexts.add(""+convertToFancyFont(text, 1));
            stylishTexts.add(""+convertToFancyFont(text, 2));
            stylishTexts.add(""+convertToFancyFont(text, 3));
            stylishTexts.add(""+convertToFancyFont(text, 4));
            stylishTexts.add(""+convertToFancyFont(text, 5));
            stylishTexts.add(""+convertToFancyFont(text, 6));
            stylishTexts.add(""+convertToFancyFont(text, 7));
            stylishTexts.add(""+convertToFancyFont(text, 8));
            stylishTexts.add(""+convertToFancyFont(text, 9));
            stylishTexts.add(""+convertToFancyFont(text, 10));
            stylishTexts.add(""+convertToFancyFont(text, 11));
            stylishTexts.add(""+convertToFancyFont(text, 12));
            stylishTexts.add(""+convertToFancyFont(text, 13));
            stylishTexts.add(""+convertToFancyFont(text, 14));
            stylishTexts.add(""+convertToFancyFont(text, 15));
            stylishTexts.add(""+convertToFancyFont(text, 16));
            stylishTexts.add(""+convertToFancyFont(text, 17));
            stylishTexts.add(""+convertToFancyFont(text, 18));
            stylishTexts.add(""+convertToFancyFont(text, 19));
            stylishTexts.add(""+convertToFancyFont(text, 20));
            stylishTexts.add(""+convertToFancyFont(text, 21));
            stylishTexts.add(""+convertToFancyFont(text, 22));
            stylishTexts.add(""+convertToFancyFont(text, 23));
            stylishTexts.add(""+convertToFancyFont(text, 24));
            stylishTexts.add(""+convertToFancyFont(text, 25));
            stylishTexts.add(""+convertToFancyFont(text, 26));
            stylishTexts.add(""+convertToFancyFont(text, 27));
            stylishTexts.add(""+convertToFancyFont(text, 28));
            stylishTexts.add(""+convertToFancyFont(text, 29));
            stylishTexts.add(""+convertToFancyFont(text, 30));
            stylishTexts.add(""+convertToFancyFont(text, 31));
            stylishTexts.add(""+convertToFancyFont(text, 32));
            stylishTexts.add(""+convertToFancyFont(text, 33));
            stylishTexts.add(""+convertToFancyFont(text, 34));
            stylishTexts.add(""+convertToFancyFont(text, 35));
            stylishTexts.add(""+convertToFancyFont(text, 36));
            stylishTexts.add(""+convertToFancyFont(text, 37));
            stylishTexts.add(""+convertToFancyFont(text, 38));
            stylishTexts.add(""+convertToFancyFont(text, 39));
            stylishTexts.add(""+convertToFancyFont(text, 40));
            stylishTexts.add(""+convertToFancyFont(text, 41));
            stylishTexts.add(""+convertToFancyFont(text, 42));
            stylishTexts.add(""+convertToFancyFont(text, 43));
            stylishTexts.add(""+convertToFancyFont(text, 44));
            stylishTexts.add(""+convertToFancyFont(text, 45));
            stylishTexts.add(""+convertToFancyFont(text, 46));
            stylishTexts.add(""+convertToFancyFont(text, 47));
            stylishTexts.add(""+convertToFancyFont(text, 48));
            stylishTexts.add(""+convertToFancyFont(text, 49));
            stylishTexts.add(""+convertToFancyFont(text, 50));
            stylishTexts.add(""+convertToFancyFont(text, 51));
            stylishTexts.add(""+convertToFancyFont(text, 52));
            stylishTexts.add(""+convertToFancyFont(text, 53));
            stylishTexts.add(""+convertToFancyFont(text, 54));
            stylishTexts.add(""+convertToFancyFont(text, 55));
            stylishTexts.add(""+convertToFancyFont(text, 56));
            stylishTexts.add(""+convertToFancyFont(text, 57));
            stylishTexts.add(""+convertToFancyFont(text, 58));
            stylishTexts.add(""+convertToFancyFont(text, 59));
            stylishTexts.add(""+convertToFancyFont(text, 60));


        }

        adapter.notifyDataSetChanged();
    }

    private String convertToFancyFont(String input, int style) {
        StringBuilder result = new StringBuilder();

        String[] swirly = {"𝒶", "𝒷", "𝒸", "𝒹", "ℯ", "𝒻", "ℊ", "𝒽", "𝒾", "𝒿",
                "𝓀", "𝓁", "𝓂", "𝓃", "ℴ", "𝓅", "𝓆", "𝓇", "𝓈", "𝓉",
                "𝓊", "𝓋", "𝓌", "𝓍", "𝓎", "𝓏"};

        String[] cursive = {"𝓪", "𝓫", "𝓬", "𝓭", "𝓮", "𝓯", "𝓰", "𝓱", "𝓲", "𝓳",
                "𝓴", "𝓵", "𝓶", "𝓷", "𝓸", "𝓹", "𝓺", "𝓻", "𝓼", "𝓽",
                "𝓾", "𝓿", "𝔀", "𝔁", "𝔂", "𝔃"};

        String[] doubleStruckStyle = {"𝔸", "𝔹", "ℂ", "𝔻", "𝔼", "𝔽", "𝔾", "ℍ", "𝕀", "𝕁",
                "𝕂", "𝕃", "𝕄", "ℕ", "𝕆", "ℙ", "ℚ", "ℝ", "𝕊", "𝕋",
                "𝕌", "𝕍", "𝕎", "𝕏", "𝕐", "ℤ"};


        String[] boldItalic = {"𝒂", "𝒃", "𝒄", "𝒅", "𝒆", "𝒇", "𝒈", "𝒉", "𝒊", "𝒋",
                "𝒌", "𝒍", "𝒎", "𝒏", "𝒐", "𝒑", "𝒒", "𝒓", "𝒔", "𝒕",
                "𝒖", "𝒗", "𝒘", "𝒙", "𝒚", "𝒛"};

        String[] gothic = {"𝔄", "𝔅", "ℭ", "𝔇", "𝔈", "𝔉", "𝔊", "ℌ", "ℑ", "𝔍",
                "𝔎", "𝔏", "𝔐", "𝔑", "𝔒", "𝔓", "𝔔", "ℜ", "𝔖", "𝔗",
                "𝔘", "𝔙", "𝔚", "𝔛", "𝔜", "ℨ"};

        String[] boldFraktur = {"𝕬", "𝕭", "𝕮", "𝕯", "𝕰", "𝕱", "𝕲", "𝕳", "𝕴", "𝕵",
                "𝕶", "𝕷", "𝕸", "𝕹", "𝕺", "𝕻", "𝕼", "𝕽", "𝕾", "𝕿",
                "𝖀", "𝖁", "𝖂", "𝖃", "𝖄", "𝖅"};

        String[] gothi = {"𝔞", "𝔟", "𝔠", "𝔡", "𝔢", "𝔣", "𝔤", "𝔥", "𝔦", "𝔧",
                "𝔨", "𝔩", "𝔪", "𝔫", "𝔬", "𝔭", "𝔮", "𝔯", "𝔰", "𝔱",
                "𝔲", "𝔳", "𝔴", "𝔵", "𝔶", "𝔷"};

        String[] italic = {"𝘢", "𝘣", "𝘤", "𝘥", "𝘦", "𝘧", "𝘨", "𝘩", "𝘪", "𝘫",
                "𝘬", "𝘭", "𝘮", "𝘯", "𝘰", "𝘱", "𝘲", "𝘳", "𝘴", "𝘵",
                "𝘶", "𝘷", "𝘸", "𝘹", "𝘺", "𝘻"};

        String[] sansSerifBold = {"𝐚", "𝐛", "𝐜", "𝐝", "𝐞", "𝐟", "𝐠", "𝐡", "𝐢", "𝐣",
                "𝐤", "𝐥", "𝐦", "𝐧", "𝐨", "𝐩", "𝐪", "𝐫", "𝐬", "𝐭",
                "𝐮", "𝐯", "𝐰", "𝐱", "𝐲", "𝐳"};


        String[] fraktur = {"𝖆", "𝖇", "𝖈", "𝖉", "𝖊", "𝖋", "𝖌", "𝖍", "𝖎", "𝖏",
                "𝖐", "𝖑", "𝖒", "𝖓", "𝖔", "𝖕", "𝖖", "𝖗", "𝖘", "𝖙",
                "𝖚", "𝖛", "𝖜", "𝖝", "𝖞", "𝖟"};

        String[] techno = {"α", "β", "ς", "∂", "є", "ғ", "g", "н", "ι", "נ",
                "к", "ℓ", "м", "и", "σ", "ρ", "q", "я", "ѕ", "т",
                "υ", "ν", "ω", "χ", "у", "z"};

        String[] diagonal = {"a", "ƃ", "ƈ", "d", "Ɛ", "ƒ", "Ɠ", "ƕ", "į", "ʝ",
                "Ƙ", "ʆ", "м", "и", "օ", "Ƥ", "ƍ", "Ր", "Ƨ", "Ƭ",
                "մ", "ν", "ω", "Ӿ", "Ƴ", "ɀ"};

        String[] arrowStyle = {"➤A➤", "➤B➤", "➤C➤", "➤D➤", "➤E➤", "➤F➤", "➤G➤", "➤H➤", "➤I➤", "➤J➤",
                "➤K➤", "➤L➤", "➤M➤", "➤N➤", "➤O➤", "➤P➤", "➤Q➤", "➤R➤", "➤S➤", "➤T➤",
                "➤U➤", "➤V➤", "➤W➤", "➤X➤", "➤Y➤", "➤Z➤"};

        String[] smallCaps = {"ᴀ", "ʙ", "ᴄ", "ᴅ", "ᴇ", "ғ", "ɢ", "ʜ", "ɪ", "ᴊ",
                "ᴋ", "ʟ", "ᴍ", "ɴ", "ᴏ", "ᴘ", "ǫ", "ʀ", "s", "ᴛ",
                "ᴜ", "ᴠ", "ᴡ", "x", "ʏ", "ᴢ"};

        String[] square = {"🄐", "🄑", "🄒", "🄓", "🄔", "🄕", "🄖", "🄗", "🄘", "🄙",
                "🄚", "🄛", "🄜", "🄝", "🄞", "🄟", "🄠", "🄡", "🄢", "🄣",
                "🄤", "🄥", "🄦", "🄧", "🄨", "🄩"};

        String[] mirrored = {"𝙖", "𝙗", "𝙘", "𝙙", "𝙚", "𝙛", "𝙜", "𝙝", "𝙞", "𝙟",
                "𝙠", "𝙡", "𝙢", "𝙣", "𝙤", "𝙥", "𝙦", "𝙧", "𝙨", "𝙩",
                "𝙪", "𝙫", "𝙬", "𝙭", "𝙮", "𝙯"};
        String[] slashed = {"🄰", "🄱", "🄲", "🄳", "🄴", "🄵", "🄶", "🄷", "🄸", "🄹",
                "🄺", "🄻", "🄼", "🄽", "🄾", "🄿", "🅀", "🅁", "🅂", "🅃",
                "🅄", "🅅", "🅆", "🅇", "🅈", "🅉"};

        String[] bubbleFille = {"ⓐ", "ⓑ", "ⓒ", "ⓓ", "ⓔ", "ⓕ", "ⓖ", "ⓗ", "ⓘ", "ⓙ",
                "ⓚ", "ⓛ", "ⓜ", "ⓝ", "ⓞ", "ⓟ", "ⓠ", "ⓡ", "ⓢ", "ⓣ",
                "ⓤ", "ⓥ", "ⓦ", "ⓧ", "ⓨ", "ⓩ"};

        String[] bubble = {"🅐", "🅑", "🅒", "🅓", "🅔", "🅕", "🅖", "🅗", "🅘", "🅙",
                "🅚", "🅛", "🅜", "🅝", "🅞", "🅟", "🅠", "🅡", "🅢", "🅣",
                "🅤", "🅥", "🅦", "🅧", "🅨", "🅩"};

        String[] bubbleFilled = {"🅰", "🅱", "🅲", "🅳", "🅴", "🅵", "🅶", "🅷", "🅸", "🅹",
                "🅺", "🅻", "🅼", "🅽", "🅾", "🅿", "🆀", "🆁", "🆂", "🆃",
                "🆄", "🆅", "🆆", "🆇", "🆈", "🆉"};

        String[] fancyCurlyStyle = {"𝓐", "𝓑", "𝓒", "𝓓", "𝓔", "𝓕", "𝓖", "𝓗", "𝓘", "𝓙",
                "𝓚", "𝓛", "𝓜", "𝓝", "𝓞", "𝓟", "𝓠", "𝓡", "𝓢", "𝓣",
                "𝓤", "𝓥", "𝓦", "𝓧", "𝓨", "𝓩"};

        String[] starOutlineStyle = {"☆A", "☆B", "☆C", "☆D", "☆E", "☆F", "☆G", "☆H", "☆I", "☆J",
                "☆K", "☆L", "☆M", "☆N", "☆O", "☆P", "☆Q", "☆R", "☆S", "☆T",
                "☆U", "☆V", "☆W", "☆X", "☆Y", "☆Z"};

        String[] reverse = {"ᴀ", "ʙ", "ᴄ", "ᴅ", "ᴇ", "ғ", "ɢ", "ʜ", "ɪ", "ᴊ",
                "ᴋ", "ʟ", "ᴍ", "ɴ", "ᴏ", "ᴘ", "ǫ", "ʀ", "s", "ᴛ",
                "ᴜ", "ᴠ", "ᴡ", "x", "ʏ", "ᴢ"};

        String[] strikethrough = {"̶a", "̶b", "̶c", "̶d", "̶e", "̶f", "̶g", "̶h", "̶i", "̶j",
                "̶k", "̶l", "̶m", "̶n", "̶o", "̶p", "̶q", "̶r", "̶s", "̶t",
                "̶u", "̶v", "̶w", "̶x", "̶y", "̶z"};

        String[] starOutlineStyl = {"☆𝓐", "☆𝓑", "☆𝓒", "☆𝓓", "☆𝓔", "☆𝓕", "☆𝓖", "☆𝓗", "☆𝓘", "☆𝓙",
                "☆𝓚", "☆𝓛", "☆𝓜", "☆𝓝", "☆𝓞", "☆𝓟", "☆𝓠", "☆𝓡", "☆𝓢", "☆𝓣",
                "☆𝓤", "☆𝓥", "☆𝓦", "☆𝓧", "☆𝓨", "☆𝓩"};

        String[] overline = {"a̅", "b̅", "c̅", "d̅", "e̅", "f̅", "g̅", "h̅", "i̅", "j̅",
                "k̅", "l̅", "m̅", "n̅", "o̅", "p̅", "q̅", "r̅", "s̅", "t̅",
                "u̅", "v̅", "w̅", "x̅", "y̅", "z̅"};

        String[] underline = {"𝗮", "𝗯", "𝗰", "𝗱", "𝗲", "𝗳", "𝗴", "𝗵", "𝗶", "𝗷",
                "𝗸", "𝗹", "𝗺", "𝗻", "𝗼", "𝗽", "𝗾", "𝗿", "𝘀", "𝘁",
                "𝘂", "𝘷", "𝘄", "𝘅", "𝘆", "𝘇"};


        String[] mirrore = {"ɒ", "q", "ɔ", "p", "ǝ", "ɟ", "ƃ", "ɥ", "ᴉ", "ɾ",
                "ʞ", "ʅ", "ɯ", "n", "o", "d", "b", "ɹ", "s", "ʇ",
                "u", "ʌ", "ʍ", "x", "ʎ", "z"};

        String[] roundedBoldStyle = {"𝗔", "𝗕", "𝗖", "𝗗", "𝗘", "𝗙", "𝗚", "𝗛", "𝗜", "𝗝",
                "𝗞", "𝗟", "𝗠", "𝗡", "𝗢", "𝗣", "𝗤", "𝗥", "𝗦", "𝗧",
                "𝗨", "𝗩", "𝗪", "𝗫", "𝗬", "𝗭"};

        String[] mirrorVertical = {"ɒ", "ɓ", "ɔ", "ɗ", "ɘ", "ƒ", "ɡ", "ɥ", "ɪ", "ʝ",
                "ƙ", "ʟ", "ɯ", "ɴ", "o", "ρ", "ϲ", "я", "ɾ", "ʇ"};

        String[] scratch = {"𝘼", "𝘽", "𝘾", "𝘿", "𝙴", "𝙵", "𝙂", "𝙷", "𝙸", "𝙹",
                "𝙺", "𝙻", "𝙼", "𝙽", "𝙾", "𝙿", "𝚀", "𝚁", "𝚂", "𝚃",
                "𝚄", "𝚅", "𝚆", "𝚇", "𝚈", "𝚉"};

        String[] japaneseStyle = {"卂", "乃", "匚", "刀", "乇", "下", "厶", "卄", "工", "丁",
                "长", "乚", "从", "𠘨", "口", "尸", "㔾", "尺", "丂", "丅",
                "凵", "リ", "山", "メ", "丫", "乙"};


        String[] emojiLetters = {"🅰️", "🅱️", "🌜", "🌛", "🎗️", "🎏", "🌀", "♓", "🎐", "🎷",
                "🎋", "👢", "〽️", "🎵", "⚽", "🅿️", "🇶", "🌱", "💲", "✝️",
                "⛎", "✅", "🔱", "❎", "🌱", "💤"};

        String[] subscript = {
                "ₐ", "♭", "꜀", "ᑯ", "ₑ", "բ", "₉", "ₕ", "ᵢ", "ⱼ",
                "ₖ", "ₗ", "ₘ", "ₙ", "ₒ", "ₚ", "૧", "ᵣ", "ₛ", "ₜ",
                "ᵤ", "ᵥ", "௰", "ₓ", "ᵧ", "𝓏"
        };

        String[] slashedMath = {
                "Ⱥ", "Ƀ", "Ȼ", "Đ", "Ɇ", "₣", "Ǥ", "Ħ", "Ɨ", "Ɉ",
                "Ꝁ", "Ł", "Μ", "Ν", "Ø", "Ᵽ", "Ꝗ", "Ɍ", "ẜ", "Ŧ",
                "Ỻ", "V", "Ꝡ", "Ӿ", "Ɏ", "Ƶ"
        };

        String[] acuteAccent = {
                "á", "b́", "ć", "d́", "é", "f́", "ǵ", "h́", "í", "j́",
                "ḱ", "ĺ", "ḿ", "ń", "ó", "ṕ", "q́", "ŕ", "ś", "t́",
                "ú", "v́", "ẃ", "x́", "ý", "ź"
        };

        String[] waveUnderline = {"a̴", "b̴", "c̴", "d̴", "e̴", "f̴", "g̴", "h̴", "i̴", "j̴",
                "k̴", "l̴", "m̴", "n̴", "o̴", "p̴", "q̴", "r̴", "s̴", "t̴",
                "u̴", "v̴", "w̴", "x̴", "y̴", "z̴"};

        String[] wavyStyle = {"a̰", "b̰", "c̰", "d̰", "ḛ", "f̰", "g̰", "h̰", "ḭ", "j̰",
                "k̰", "l̰", "m̰", "n̰", "o̰", "p̰", "q̰", "r̰", "s̰", "t̰",
                "ṵ", "v̰", "w̰", "x̰", "y̰", "z̰"};

        String[] doubleUnderline = {"a̳", "b̳", "c̳", "d̳", "e̳", "f̳", "g̳", "h̳", "i̳", "j̳",
                "k̳", "l̳", "m̳", "n̳", "o̳", "p̳", "q̳", "r̳", "s̳", "t̳",
                "u̳", "v̳", "w̳", "x̳", "y̳", "z̳"};

        String[] tildeOverlay = {"ã", "b̃", "c̃", "d̃", "ẽ", "f̃", "g̃", "h̃", "ĩ", "j̃",
                "k̃", "l̃", "m̃", "ñ", "õ", "p̃", "q̃", "r̃", "s̃", "t̃",
                "ũ", "ṽ", "w̃", "x̃", "ỹ", "z̃"};

        String[] dotAbove = {"ȧ", "ḃ", "ċ", "ḋ", "ė", "ḟ", "ġ", "ḣ", "i̇", "j̇",
                "k̇", "l̇", "ṁ", "ṅ", "ȯ", "ṗ", "q̇", "ṙ", "ṡ", "ṫ",
                "u̇", "v̇", "ẇ", "ẋ", "ẏ", "ż"};

        String[] zigzagOverlay = {"a̩", "b̩", "c̩", "d̩", "e̩", "f̩", "g̩", "h̩", "i̩", "j̩",
                "k̩", "l̩", "m̩", "n̩", "o̩", "p̩", "q̩", "r̩", "s̩", "t̩",
                "u̩", "v̩", "w̩", "x̩", "y̩", "z̩"};


        String[] gothic1 = {"𝔞", "𝔟", "𝔠", "𝔡", "𝔢", "𝔣", "𝔤", "𝔥", "𝔦", "𝔧",
                "𝔨", "𝔩", "𝔪", "𝔫", "𝔬", "𝔭", "𝔮", "𝔯", "𝔰", "𝔱",
                "𝔲", "𝔳", "𝔴", "𝔵", "𝔶", "𝔷"};

        String[] italicFraktur = {"𝖆", "𝖇", "𝖈", "𝖉", "𝖊", "𝖋", "𝖌", "𝖍", "𝖎", "𝖏",
                "𝖐", "𝖑", "𝖒", "𝖓", "𝖔", "𝖕", "𝖖", "𝖗", "𝖘", "𝖙",
                "𝖚", "𝖛", "𝖜", " 𝖝", "𝖞", "𝖟"};

        String[] glitchText = {"ค", "в", "¢", "∂", "є", "Ŧ", "ﻮ", "ђ", "เ",
                "к", "ɭ", "๓", "ภ", "๏", "ק", "ợ", "г", "ร", "Շ",
                "ย", "ש", "ฬ", "א", "ע", "չ"};

        String[] fantasy = {"α", "в", "¢", "∂", "є", "ғ", "ɢ", "н", "ɪ", "ᴊ",
                "κ", "ʟ", "м", "и", "σ", "ρ", "ǫ", "я", "ѕ", "т",
                "υ", "ν", "ω", "χ", "у", "z"};

        String[] wavy = {"Ⱥ", "Ƀ", "Ȼ", "Ɖ", "Ɇ", "Ƒ", "Ɠ", "Ȟ", "Ɨ", "Ɉ",
                "Ȼ", "Ƚ", "Ɱ", "Ƞ", "Ơ", "Ƥ", "Q", "Ȓ", "Ƨ", "Ŧ",
                "Ʉ", "V", "Ŵ", "Ẍ", "Ɏ", "Ƶ"};

        String[] curvy = {"ɑ", "ɓ", "ƈ", "ɗ", "ɛ", "ƒ", "ɠ", "ɦ", "ɨ", "ʝ",
                "ƙ", "ʟ", "ɱ", "ɲ", "ɵ", "ρ", "ɋ", "ʀ", "ʂ", "ƭ",
                "ʋ", "ʋ", "ʍ", "𝓍", "𝓎", "ƶ"};


        String[] script = {"𝒜", "ℬ", "𝒞", "𝒟", "ℰ", "ℱ", "𝒢", "ℋ", "ℐ", "𝒥",
                "𝒦", "ℒ", "ℳ", "𝒩", "𝒪", "𝒫", "𝒬", "ℛ", "𝒮", "𝒯",
                "𝒰", "𝒱", "𝒲", "𝒳", "𝒴", "𝒵"};
        String[] inverted = {"ɐ", "q", "ɔ", "p", "ǝ", "ɟ", "ƃ", "ɥ", "ᴉ", "ɾ",
                "ʞ", "ʃ", "ɯ", "u", "o", "d", "b", "ɹ", "s", "ʇ",
                "n", "ʌ", "ʍ", "x", "ʎ", "z"};
        String[] singleUnderline = {"a̲", "b̲", "c̲", "d̲", "e̲", "f̲", "g̲", "h̲", "i̲", "j̲",
                "k̲", "l̲", "m̲", "n̲", "o̲", "p̲", "q̲", "r̲", "s̲", "t̲",
                "u̲", "v̲", "w̲", "x̲", "y̲", "z̲"};
        String[] doubleUnderline1 = {"a̳", "b̳", "c̳", "d̳", "e̳", "f̳", "g̳", "h̳", "i̳", "j̳",
                "k̳", "l̳", "m̳", "n̳", "o̳", "p̳", "q̳", "r̳", "s̳", "t̳",
                "u̳", "v̳", "w̳", "x̳", "y̳", "z̳"};
        String[] dotted = {"ȧ", "ḃ", "ċ", "ḋ", "ė", "ḟ", "ġ", "ḣ", "i̇", "j̇",
                "k̇", "l̇", "ṁ", "ṅ", "ȯ", "ṗ", "q̇", "ṙ", "ṡ", "ṫ",
                "u̇", "v̇", "ẇ", "ẋ", "ẏ", "ż"};
        String[] subscript1 = {"ₐ", "ᵦ", "𝒸", "ᵨ", "ₑ", "𝒻", "𝓰", "ₕ", "ᵢ", "ⱼ",
                "ₖ", "ₗ", "ₘ", "ₙ", "ₒ", "ₚ", "ᵩ", "ᵣ", "ₛ", "ₜ",
                "ᵤ", "ᵥ", "𝓌", "ₓ", "ᵧ", "𝓏"};
        String[] subscriptStyle = {"ₐ", "b", "c", "d", "ₑ", "f", "g", "ₕ", "ᵢ", "ⱼ",
                "ₖ", "ₗ", "ₘ", "ₙ", "ₒ", "ₚ", "q", "ᵣ", "ₛ", "ₜ",
                "ᵤ", "ᵥ", "w", "ₓ", "y", "z"};

        String[] emojiMixed = {"𝐀", " 🅱", "𝘾", "𝐃", "𝐄", "𝐅", "𝐆", "𝐇", "𝐈", "𝐉",
                "𝐊", "𝐋", "𝐌", "𝐍", "𝐎", "𝐏", "𝐐", "𝐑", "𝐒", "𝐓",
                "𝐔", "𝐕", "𝐖", "𝐗", "𝐘", "𝐙"};

        String[] diagonalStrike = {"a̸", "b̸", "c̸", "d̸", "e̸", "f̸", "g̸", "h̸", "i̸", "j̸",
                "k̸", "l̸", "m̸", "n̸", "o̸", "p̸", "q̸", "r̸", "s̸", "t̸",
                "u̸", "v̸", "w̸", "x̸", "y̸", "z̸"};

        String[] strikethrough1 = {"a̶", "b̶", "c̶", "d̶", "e̶", "f̶", "g̶", "h̶", "i̶", "j̶",
                "k̶", "l̶", "m̶", "n̶", "o̶", "p̶", "q̶", "r̶", "s̶", "t̶",
                "u̶", "v̶", "w̶", "x̶", "y̶", "z̶"};

        String[] softGlow = {"ȧ̇", "ḃ̇", "ċ̇", "ḋ̇", "ė̇", "ḟ̇", "ġ̇", "ḣ̇", "i̇̇", "j̇̇",
                "k̇̇", "l̇̇", "ṁ̇", "ṅ̇", "ȯ̇", "ṗ̇", "q̇̇", "ṙ̇", "ṡ̇", "ṫ̇",
                "u̇̇", "v̇̇", "ẇ̇", "ẋ̇", "ẏ̇", "ż̇"};

        String[] elegantSwirl = {"a͓", "b͓", "c͓", "d͓", "e͓", "f͓", "g͓", "h͓", "i͓", "j͓",
                "k͓", "l͓", "m͓", "n͓", "o͓", "p͓", "q͓", "r͓", "s͓", "t͓",
                "u͓", "v͓", "w͓", "x͓", "y͓", "z͓"};




        String[] selectedFont = null;
        switch (style) {
            case 1:
                selectedFont = swirly;
                break;
            case 2:
                selectedFont = cursive;
                break;
            case 3:
                selectedFont = doubleStruckStyle;
                break;
            case 4:
                selectedFont = boldItalic;
                break;
            case 5:
                selectedFont = gothic;
                break;
            case 6:
                selectedFont = boldFraktur;
                break;
            case 7:
                selectedFont = gothi;
                break;
            case 8:
                selectedFont = italic;
                break;
            case 9:
                selectedFont = sansSerifBold;
                break;

            case 10:
                selectedFont = fraktur;
                break;
            case 11:
                selectedFont = techno;
                break;
            case 12:
                selectedFont = diagonal;
                break;
            case 13:
                selectedFont = arrowStyle;
                break;
            case 14:
                selectedFont = smallCaps;
                break;
            case 15:
                selectedFont = square;
                break;
            case 16:
                selectedFont = mirrored;
                break;
            case 17:
                selectedFont = slashed;
                break;
            case 18:
                selectedFont = bubbleFille;
                break;
            case 19:
                selectedFont = bubble;
                break;
            case 20:
                selectedFont = bubbleFilled;
                break;
            case 21:
                selectedFont = fancyCurlyStyle;
                break;
            case 22:
                selectedFont = reverse;
                break;
            case 23:
                selectedFont = strikethrough;
                break;
            case 24:
                selectedFont = starOutlineStyl;
                break;
            case 25:
                selectedFont = overline;
                break;
            case 26:
                selectedFont = underline;
                break;
            case 27:
                selectedFont = mirrore;
                break;
            case 28:
                selectedFont = roundedBoldStyle;
                break;
            case 29:
                selectedFont = mirrorVertical;
                break;
            case 30:
                selectedFont = scratch;
                break;
            case 31:
                selectedFont = japaneseStyle;
                break;
            case 32:
                selectedFont = emojiLetters;
                break;
            case 33:
                selectedFont = subscript;
                break;
            case 34:
                selectedFont = slashedMath;
                break;
            case 35:
                selectedFont = acuteAccent;
                break;
            case 36:
                selectedFont = waveUnderline;
                break;
            case 37:
                selectedFont = wavyStyle;
                break;
            case 38:
                selectedFont = doubleUnderline;
                break;
            case 39:
                selectedFont = tildeOverlay;
                break;
            case 40:
                selectedFont = dotAbove;
                break;
            case 41:
                selectedFont = zigzagOverlay;
                break;
            case 42:
                selectedFont = gothic1;
                break;
            case 43:
                selectedFont = italicFraktur;
                break;
            case 44:
                selectedFont = glitchText;
                break;
            case 45:
                selectedFont = fantasy;
                break;
            case 46:
                selectedFont = wavy;
                break;
            case 47:
                selectedFont = curvy;
                break;
            case 48:
                selectedFont = script;
                break;
            case 49:
                selectedFont = inverted;
                break;
            case 50:
                selectedFont = singleUnderline;
                break;
            case 51:
                selectedFont = doubleUnderline1;
                break;
            case 52:
                selectedFont = dotted;
                break;
            case 53:
                selectedFont = subscript1;
                break;
            case 54:
                selectedFont = subscriptStyle;
                break;
            case 55:
                selectedFont = emojiMixed;
                break;
            case 56:
                selectedFont = diagonalStrike;
                break;
            case 57:
                selectedFont = strikethrough1;
                break;
            case 58:
                selectedFont = softGlow;
                break;
            case 59:
                selectedFont = elegantSwirl;
                break;

            case 60:
                selectedFont = starOutlineStyle;
                break;

            default:
                return input;
        }


        for (char c : input.toCharArray()) {
            if (Character.isLetter(c)) {
                int index = Character.toLowerCase(c) - 'a';
                if (index >= 0 && index < selectedFont.length) {
                    result.append(selectedFont[index]);
                } else {
                    result.append(c);
                }
            } else {
                result.append(c);
            }


        }
        return result.toString();

    }

    




    @Override
    public void onBackPressed() {

        startActivity(new Intent(Stylish_Font_Activity.this, MainActivity.class));
        finish();
        super.onBackPressed();
    }



}