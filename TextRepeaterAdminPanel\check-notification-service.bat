@echo off
echo ========================================
echo   Check Notification Service Status
echo ========================================
echo.

cd /d "%~dp0"

echo Checking service status...
C:\xampp\php\php.exe "cron\background-notification-service.php" status

echo.
echo Recent log entries:
echo -------------------
if exist "cron\background-service.log" (
    powershell "Get-Content 'cron\background-service.log' | Select-Object -Last 10"
) else (
    echo No log file found.
)

echo.
pause
