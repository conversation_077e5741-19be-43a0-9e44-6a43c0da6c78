<?php
$page_title = 'Notification History';

// Get database connection first (before including header)
require_once 'config/database.php';
$database = new Database();
$conn = $database->getConnection();

// Handle delete action BEFORE including header to avoid "headers already sent" error
if (isset($_POST['delete_notification'])) {
    $notification_id = $_POST['notification_id'];

    try {
        // Delete notification delivery records first
        $stmt = $conn->prepare("DELETE FROM notification_delivery WHERE notification_id = :id");
        $stmt->bindParam(':id', $notification_id);
        $stmt->execute();

        // Delete notification
        $stmt = $conn->prepare("DELETE FROM notifications WHERE id = :id");
        $stmt->bindParam(':id', $notification_id);
        $stmt->execute();

        $success_message = "Notification deleted successfully.";

        // Refresh the page to update the list
        header("Location: notification-history.php?" . http_build_query($_GET));
        exit();
    } catch (Exception $e) {
        $error_message = "Error deleting notification: " . $e->getMessage();
    }
}

// Now include header after processing POST requests
require_once 'includes/header.php';

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$limit = Config::ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Search and filter parameters
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status'] ?? '';
$date_from = $_GET['date_from'] ?? '';
$date_to = $_GET['date_to'] ?? '';

// Build WHERE clause
$where_conditions = [];
$params = [];

if (!empty($search)) {
    $where_conditions[] = "(title LIKE :search OR message LIKE :search)";
    $params[':search'] = '%' . $search . '%';
}

if (!empty($status_filter)) {
    $where_conditions[] = "status = :status";
    $params[':status'] = $status_filter;
}

if (!empty($date_from)) {
    $where_conditions[] = "DATE(created_at) >= :date_from";
    $params[':date_from'] = $date_from;
}

if (!empty($date_to)) {
    $where_conditions[] = "DATE(created_at) <= :date_to";
    $params[':date_to'] = $date_to;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get total count for pagination
$count_query = "SELECT COUNT(*) as total FROM notifications $where_clause";
$count_stmt = $conn->prepare($count_query);
foreach ($params as $key => $value) {
    $count_stmt->bindValue($key, $value);
}
$count_stmt->execute();
$total_records = $count_stmt->fetch()['total'];
$total_pages = ceil($total_records / $limit);

// Get notifications
$query = "
    SELECT
        id,
        title,
        message,
        status,
        target_type,
        target_value,
        scheduled_at,
        sent_at,
        created_at,
        (SELECT COUNT(*) FROM notification_delivery WHERE notification_id = notifications.id) as delivery_count,
        (SELECT COUNT(*) FROM notification_delivery WHERE notification_id = notifications.id AND status = 'delivered') as delivered_count,
        (SELECT COUNT(*) FROM notification_delivery WHERE notification_id = notifications.id AND clicked_at IS NOT NULL) as clicked_count
    FROM notifications
    $where_clause
    ORDER BY created_at DESC
    LIMIT :limit OFFSET :offset
";

$stmt = $conn->prepare($query);
foreach ($params as $key => $value) {
    $stmt->bindValue($key, $value);
}
$stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
$stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
$stmt->execute();
$notifications = $stmt->fetchAll();
?>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <?php echo $error_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">Filter Notifications</h6>
            </div>
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" class="form-control" id="search" name="search"
                               placeholder="Title or message..." value="<?php echo htmlspecialchars($search); ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">All Status</option>
                            <option value="draft" <?php echo $status_filter === 'draft' ? 'selected' : ''; ?>>Draft</option>
                            <option value="scheduled" <?php echo $status_filter === 'scheduled' ? 'selected' : ''; ?>>Scheduled</option>
                            <option value="sent" <?php echo $status_filter === 'sent' ? 'selected' : ''; ?>>Sent</option>
                            <option value="failed" <?php echo $status_filter === 'failed' ? 'selected' : ''; ?>>Failed</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="date_from" class="form-label">From Date</label>
                        <input type="date" class="form-control" id="date_from" name="date_from"
                               value="<?php echo htmlspecialchars($date_from); ?>">
                    </div>
                    <div class="col-md-2">
                        <label for="date_to" class="form-label">To Date</label>
                        <input type="date" class="form-control" id="date_to" name="date_to"
                               value="<?php echo htmlspecialchars($date_to); ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>Filter
                            </button>
                            <a href="notification-history.php" class="btn btn-outline-secondary">
                                <i class="fas fa-refresh me-2"></i>Reset
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- System Control Panel -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-cogs me-2"></i>Automatic Notification System
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="d-flex align-items-center mb-3">
                            <div class="me-3">
                                <div id="systemStatus" class="badge bg-secondary">Checking...</div>
                            </div>
                            <div>
                                <h6 class="mb-1">Automatic Processing Status</h6>
                                <p class="mb-0 text-muted small">Monitors and sends scheduled notifications automatically</p>
                            </div>
                        </div>

                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-success" onclick="startAutoProcessor()">
                                <i class="fas fa-play me-2"></i>Start Auto-Processor
                            </button>
                            <button type="button" class="btn btn-warning" onclick="stopAutoProcessor()">
                                <i class="fas fa-stop me-2"></i>Stop Auto-Processor
                            </button>
                            <button type="button" class="btn btn-info" onclick="checkSystemStatus()">
                                <i class="fas fa-refresh me-2"></i>Check Status
                            </button>
                            <a href="scheduled-notifications-processor.php" class="btn btn-primary">
                                <i class="fas fa-cog me-2"></i>Processor Dashboard
                            </a>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="text-center">
                            <div class="mb-2">
                                <i class="fas fa-clock fa-2x text-primary"></i>
                            </div>
                            <h6>Quick Actions</h6>
                            <div class="btn-group-vertical w-100">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="processNow()">
                                    <i class="fas fa-paper-plane me-2"></i>Process Due Now
                                </button>
                                <a href="notifications.php" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-plus me-2"></i>Create New
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Log -->
                <div class="mt-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h6 class="mb-0">System Log</h6>
                        <button type="button" class="btn btn-sm btn-outline-secondary" onclick="refreshLog()">
                            <i class="fas fa-refresh"></i>
                        </button>
                    </div>
                    <div id="systemLog" class="bg-dark text-light p-3 rounded" style="height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;">
                        <div class="text-muted">System log will appear here...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Notifications List -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Notification History</h6>
                <a href="notifications.php" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus me-2"></i>Send New Notification
                </a>
            </div>
            <div class="card-body">
                <?php if (empty($notifications)): ?>
                    <div class="text-center py-4">
                        <i class="fas fa-bell fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No notifications found</h5>
                        <p class="text-muted">No notifications match your current filters.</p>
                        <a href="notifications.php" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Send Your First Notification
                        </a>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Title</th>
                                    <th>Status</th>
                                    <th>Target</th>
                                    <th>Delivery Stats</th>
                                    <th>Created</th>
                                    <th>Scheduled/Sent</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($notifications as $notification): ?>
                                    <tr>
                                        <td>
                                            <div class="fw-bold"><?php echo htmlspecialchars($notification['title']); ?></div>
                                            <div class="text-muted small">
                                                <?php echo htmlspecialchars(substr($notification['message'], 0, 100)); ?>
                                                <?php if (strlen($notification['message']) > 100): ?>...<?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php
                                            $status_class = [
                                                'draft' => 'secondary',
                                                'scheduled' => 'warning',
                                                'sent' => 'success',
                                                'failed' => 'danger'
                                            ];
                                            $class = $status_class[$notification['status']] ?? 'secondary';
                                            ?>
                                            <span class="badge bg-<?php echo $class; ?>">
                                                <?php echo ucfirst($notification['status']); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <div class="small">
                                                <strong><?php echo ucfirst($notification['target_type']); ?></strong>
                                                <?php if ($notification['target_value']): ?>
                                                    <br><code class="small"><?php echo htmlspecialchars($notification['target_value']); ?></code>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($notification['status'] === 'sent'): ?>
                                                <div class="small">
                                                    <div>Sent: <?php echo number_format($notification['delivery_count']); ?></div>
                                                    <div>Delivered: <?php echo number_format($notification['delivered_count']); ?></div>
                                                    <div>Clicked: <?php echo number_format($notification['clicked_count']); ?></div>
                                                </div>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="small">
                                                <?php echo date('M j, Y', strtotime($notification['created_at'])); ?>
                                                <br><?php echo date('g:i A', strtotime($notification['created_at'])); ?>
                                            </div>
                                        </td>
                                        <td>
                                            <?php if ($notification['status'] === 'scheduled' && $notification['scheduled_at']): ?>
                                                <div class="small">
                                                    <strong class="text-warning">Scheduled:</strong><br>
                                                    <?php echo date('M j, Y', strtotime($notification['scheduled_at'])); ?>
                                                    <br><?php echo date('g:i A', strtotime($notification['scheduled_at'])); ?>
                                                    <?php
                                                    $scheduled_time = strtotime($notification['scheduled_at']);
                                                    $current_time = time();
                                                    if ($scheduled_time <= $current_time): ?>
                                                        <br><span class="badge bg-danger">Due Now</span>
                                                    <?php else: ?>
                                                        <br><span class="badge bg-info">Future</span>
                                                    <?php endif; ?>
                                                </div>
                                            <?php elseif ($notification['sent_at']): ?>
                                                <div class="small">
                                                    <strong class="text-success">Sent:</strong><br>
                                                    <?php echo date('M j, Y', strtotime($notification['sent_at'])); ?>
                                                    <br><?php echo date('g:i A', strtotime($notification['sent_at'])); ?>
                                                    <?php if ($notification['scheduled_at']): ?>
                                                        <br><small class="text-muted">
                                                            (Scheduled: <?php echo date('M j, g:i A', strtotime($notification['scheduled_at'])); ?>)
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            <?php elseif ($notification['status'] === 'draft'): ?>
                                                <span class="text-muted small">Draft</span>
                                            <?php else: ?>
                                                <span class="text-muted">-</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-outline-info"
                                                        onclick="viewNotification(<?php echo $notification['id']; ?>)"
                                                        title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if ($notification['status'] === 'draft'): ?>
                                                    <a href="notifications.php?edit=<?php echo $notification['id']; ?>"
                                                       class="btn btn-sm btn-outline-primary"
                                                       title="Edit Notification">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                <?php elseif ($notification['status'] === 'scheduled'): ?>
                                                    <?php
                                                    $scheduled_time = strtotime($notification['scheduled_at']);
                                                    $current_time = time();
                                                    if ($scheduled_time <= $current_time): ?>
                                                        <a href="scheduled-notifications-processor.php?process=true"
                                                           class="btn btn-sm btn-outline-success"
                                                           title="Send Now (Due)">
                                                            <i class="fas fa-paper-plane"></i>
                                                        </a>
                                                    <?php else: ?>
                                                        <a href="notifications.php?edit=<?php echo $notification['id']; ?>"
                                                           class="btn btn-sm btn-outline-warning"
                                                           title="Edit Schedule">
                                                            <i class="fas fa-clock"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                <?php endif; ?>
                                                <button type="button" class="btn btn-sm btn-outline-danger"
                                                        onclick="deleteNotification(<?php echo $notification['id']; ?>)"
                                                        title="Delete Notification">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="Page navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>">
                                            Previous
                                        </a>
                                    </li>
                                <?php endif; ?>

                                <?php for ($i = max(1, $page - 2); $i <= min($total_pages, $page + 2); $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>">
                                            <?php echo $i; ?>
                                        </a>
                                    </li>
                                <?php endfor; ?>

                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>">
                                            Next
                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- View Notification Modal -->
<div class="modal fade" id="viewNotificationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Notification Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="notificationDetails">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Form -->
<form id="deleteForm" method="POST" style="display: none;">
    <input type="hidden" name="delete_notification" value="1">
    <input type="hidden" name="notification_id" id="deleteNotificationId">
</form>

<?php
$page_scripts = "
<script>
function viewNotification(id) {
    // Show loading
    document.getElementById('notificationDetails').innerHTML = '<div class=\"text-center\"><div class=\"loading-spinner\"></div> Loading...</div>';

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('viewNotificationModal'));
    modal.show();

    // Load notification details via AJAX
    makeAjaxRequest('api/get-notification.php?id=' + id, 'GET', null,
        function(response) {
            if (response.success) {
                const notification = response.data;
                let html = '<div class=\"row\">';
                html += '<div class=\"col-md-6\"><strong>Title:</strong><br>' + notification.title + '</div>';
                html += '<div class=\"col-md-6\"><strong>Status:</strong><br><span class=\"badge bg-' + getStatusClass(notification.status) + '\">' + notification.status.charAt(0).toUpperCase() + notification.status.slice(1) + '</span></div>';
                html += '</div><hr>';
                html += '<div class=\"row\">';
                html += '<div class=\"col-12\"><strong>Message:</strong><br>' + notification.message + '</div>';
                html += '</div><hr>';
                html += '<div class=\"row\">';
                html += '<div class=\"col-md-6\"><strong>Target Type:</strong><br>' + notification.target_type + '</div>';
                html += '<div class=\"col-md-6\"><strong>Target Value:</strong><br>' + (notification.target_value || 'All users') + '</div>';
                html += '</div><hr>';
                html += '<div class=\"row\">';
                html += '<div class=\"col-md-6\"><strong>Created:</strong><br>' + formatDate(notification.created_at) + '</div>';
                if (notification.status === 'scheduled' && notification.scheduled_at) {
                    html += '<div class=\"col-md-6\"><strong>Scheduled:</strong><br>' + formatDate(notification.scheduled_at) + '</div>';
                } else {
                    html += '<div class=\"col-md-6\"><strong>Sent:</strong><br>' + (notification.sent_at ? formatDate(notification.sent_at) : 'Not sent') + '</div>';
                }
                html += '</div>';
                if (notification.scheduled_at && notification.sent_at) {
                    html += '<hr><div class=\"row\">';
                    html += '<div class=\"col-md-6\"><strong>Originally Scheduled:</strong><br>' + formatDate(notification.scheduled_at) + '</div>';
                    html += '<div class=\"col-md-6\"><strong>Actually Sent:</strong><br>' + formatDate(notification.sent_at) + '</div>';
                    html += '</div>';
                }

                document.getElementById('notificationDetails').innerHTML = html;
            } else {
                document.getElementById('notificationDetails').innerHTML = '<div class=\"alert alert-danger\">Error loading notification details.</div>';
            }
        },
        function(status, error) {
            document.getElementById('notificationDetails').innerHTML = '<div class=\"alert alert-danger\">Error loading notification details: ' + error + '</div>';
        }
    );
}

function deleteNotification(id) {
    confirmAction(
        'Delete Notification',
        'Are you sure you want to delete this notification? This action cannot be undone.',
        function() {
            document.getElementById('deleteNotificationId').value = id;
            document.getElementById('deleteForm').submit();
        }
    );
}

function getStatusClass(status) {
    const classes = {
        'draft': 'secondary',
        'scheduled': 'warning',
        'sent': 'success',
        'failed': 'danger'
    };
    return classes[status] || 'secondary';
}

// System Control Functions
let autoProcessorInterval = null;
let logUpdateInterval = null;

function startAutoProcessor() {
    const statusElement = document.getElementById('systemStatus');
    const logElement = document.getElementById('systemLog');

    // Update status
    statusElement.className = 'badge bg-success';
    statusElement.textContent = 'Starting...';

    // Add log entry
    addLogEntry('🚀 Starting automatic notification processor...');

    // Start the processor
    fetch('api/system-control.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'start_processor'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusElement.className = 'badge bg-success';
            statusElement.textContent = 'Running';
            addLogEntry('✅ Auto-processor started successfully');

            // Start monitoring
            startStatusMonitoring();
        } else {
            statusElement.className = 'badge bg-danger';
            statusElement.textContent = 'Failed';
            addLogEntry('❌ Failed to start auto-processor: ' + data.message);
        }
    })
    .catch(error => {
        statusElement.className = 'badge bg-danger';
        statusElement.textContent = 'Error';
        addLogEntry('❌ Error starting auto-processor: ' + error.message);
    });
}

function stopAutoProcessor() {
    const statusElement = document.getElementById('systemStatus');

    // Update status
    statusElement.className = 'badge bg-warning';
    statusElement.textContent = 'Stopping...';

    // Add log entry
    addLogEntry('🛑 Stopping automatic notification processor...');

    // Stop the processor
    fetch('api/system-control.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'stop_processor'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            statusElement.className = 'badge bg-secondary';
            statusElement.textContent = 'Stopped';
            addLogEntry('✅ Auto-processor stopped successfully');

            // Stop monitoring
            stopStatusMonitoring();
        } else {
            statusElement.className = 'badge bg-danger';
            statusElement.textContent = 'Error';
            addLogEntry('❌ Failed to stop auto-processor: ' + data.message);
        }
    })
    .catch(error => {
        statusElement.className = 'badge bg-danger';
        statusElement.textContent = 'Error';
        addLogEntry('❌ Error stopping auto-processor: ' + error.message);
    });
}

function checkSystemStatus() {
    const statusElement = document.getElementById('systemStatus');

    statusElement.className = 'badge bg-info';
    statusElement.textContent = 'Checking...';

    fetch('api/system-control.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'check_status'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const isRunning = data.data.is_running;
            const dueCount = data.data.due_notifications || 0;

            if (isRunning) {
                statusElement.className = 'badge bg-success';
                statusElement.textContent = 'Running';
                addLogEntry('✅ Auto-processor is running');
            } else {
                statusElement.className = 'badge bg-secondary';
                statusElement.textContent = 'Stopped';
                addLogEntry('ℹ️ Auto-processor is not running');
            }

            if (dueCount > 0) {
                addLogEntry('⏰ ' + dueCount + ' notifications are due for processing');
            }
        } else {
            statusElement.className = 'badge bg-danger';
            statusElement.textContent = 'Error';
            addLogEntry('❌ Failed to check status: ' + data.message);
        }
    })
    .catch(error => {
        statusElement.className = 'badge bg-danger';
        statusElement.textContent = 'Error';
        addLogEntry('❌ Error checking status: ' + error.message);
    });
}

function processNow() {
    addLogEntry('🔄 Processing due notifications manually...');

    fetch('cron/process-scheduled-notifications.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            addLogEntry('✅ Processed ' + data.processed + ' notifications (' + data.successful + ' successful, ' + data.failed + ' failed)');

            // Refresh the page to update the notification list
            setTimeout(() => {
                window.location.reload();
            }, 2000);
        } else {
            addLogEntry('❌ Failed to process notifications: ' + data.message);
        }
    })
    .catch(error => {
        addLogEntry('❌ Error processing notifications: ' + error.message);
    });
}

function addLogEntry(message) {
    const logElement = document.getElementById('systemLog');
    const timestamp = new Date().toLocaleTimeString();
    const entry = '[' + timestamp + '] ' + message;

    // Add new entry
    const newEntry = document.createElement('div');
    newEntry.textContent = entry;
    logElement.appendChild(newEntry);

    // Scroll to bottom
    logElement.scrollTop = logElement.scrollHeight;

    // Keep only last 50 entries
    while (logElement.children.length > 50) {
        logElement.removeChild(logElement.firstChild);
    }
}

function refreshLog() {
    const logElement = document.getElementById('systemLog');
    logElement.innerHTML = '<div class=\"text-muted\">System log refreshed...</div>';
    addLogEntry('🔄 Log refreshed');
}

function startStatusMonitoring() {
    // Check status every 30 seconds
    autoProcessorInterval = setInterval(checkSystemStatus, 30000);
}

function stopStatusMonitoring() {
    if (autoProcessorInterval) {
        clearInterval(autoProcessorInterval);
        autoProcessorInterval = null;
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    checkSystemStatus();
    addLogEntry('📋 Notification History page loaded');
});
</script>
";

require_once 'includes/footer.php';
?>