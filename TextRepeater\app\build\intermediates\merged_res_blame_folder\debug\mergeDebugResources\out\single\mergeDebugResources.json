[{"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_random_image.png.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/random_image.png"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_pulsing_dot.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/pulsing_dot.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_emoji_dailog.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/emoji_dailog.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_share.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/share.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_slider_screen.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/slider_screen.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_view_back.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/view_back.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_sms_item_dailog.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/sms_item_dailog.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_constant_second_back.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/constant_second_back.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_baseline_repeat_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/baseline_repeat_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/xml_data_extraction_rules.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/xml/data_extraction_rules.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_alkatra_medium.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/alkatra_medium.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_info.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/info.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_afacad_medium.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/afacad_medium.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_logo.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/logo.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_notification_permission.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_notification_permission.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_baseline_wrap_text_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/baseline_wrap_text_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_text_to_emoji.png.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/text_to_emoji.png"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_dialog_sms_bottom_sheet.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/dialog_sms_bottom_sheet.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_emoji_art_layout.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/emoji_art_layout.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_andika_italic.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/andika_italic.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_ic_launcher_foreground.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-xxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_amaranth_bold.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/amaranth_bold.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_image_stylish.png.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/image_stylish.png"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_fragment_sms.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/fragment_sms.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_alata.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/alata.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable-night_baseline_refresh_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable-night/baseline_refresh_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_aladin.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/aladin.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_decoration_text.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_decoration_text.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_emoji_art.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_emoji_art.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_night_mode.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/night_mode.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_abril_fatface.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/abril_fatface.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/anim_fade_in_text.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/anim/fade_in_text.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_image_art.png.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/image_art.png"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_privacy.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/privacy.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_baseline_text_fields_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/baseline_text_fields_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_alkatra.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/alkatra.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_baseline_keyboard_arrow_right_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/baseline_keyboard_arrow_right_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_ic_launcher_background.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/ic_launcher_background.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_drawer_color_item.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/drawer_color_item.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_aleo_medium_italic.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/aleo_medium_italic.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_button_backround.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/button_backround.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_fragment_funny_.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/fragment_funny_.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_decoration_image.png.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/decoration_image.png"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_fragment_sad_.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/fragment_sad_.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_baseline_arrow_back_ios_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/baseline_arrow_back_ios_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_angina_logo.png.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/angina_logo.png"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable-night_view_back.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable-night/view_back.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_enoji_item_layout.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/enoji_item_layout.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_arima_madurai_medium.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/arima_madurai_medium.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_abhaya_libre.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/abhaya_libre.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/anim_slide_in.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/anim/slide_in.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_button_back.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/button_back.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_baseline_content_copy_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/baseline_content_copy_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_arima_madurai_bold.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/arima_madurai_bold.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_almendra.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/almendra.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_edittext_backround.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/edittext_backround.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_navigation.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_navigation.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_archivo_black.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/archivo_black.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/anim_pulse_animation.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/anim/pulse_animation.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_blank_image.png.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/blank_image.png"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_sms_item_back.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/sms_item_back.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_baseline_arrow_forward_ios_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/baseline_arrow_forward_ios_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_acme.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/acme.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_allerta.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/allerta.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_baseline_share_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/baseline_share_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_splash_screen.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_splash_screen.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_advent_pro_medium.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/advent_pro_medium.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_random_text.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_random_text.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_alatsi.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/alatsi.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_fragment_dynamic_category.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/fragment_dynamic_category.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable-night_baseline_settings_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable-night/baseline_settings_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_blank_text.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_blank_text.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_baseline_settings_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/baseline_settings_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_stylish_font.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_stylish_font.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_fragment_romantic_s_.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/fragment_romantic_s_.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-xxxhdpi/ic_launcher_foreground.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_alexandria.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/alexandria.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_advent_pro_semibold.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/advent_pro_semibold.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-hdpi/ic_launcher_foreground.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_dot_inactive.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/dot_inactive.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_star.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/star.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_alice.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/alice.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_message.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_message.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_settings_layout_back.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/settings_layout_back.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/anim_slide_out.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/anim/slide_out.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_dialog_about_us.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/dialog_about_us.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_dot_active.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/dot_active.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_constant_back.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/constant_back.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_message_item.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/message_item.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_repeater_image.png.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/repeater_image.png"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_agbalumo.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/agbalumo.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_artifika.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/artifika.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/menu_settings_item.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/menu/settings_item.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_copy_share_back.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/copy_share_back.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/xml_gma_ad_services_config.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/xml/gma_ad_services_config.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_amaranth.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/amaranth.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_text_to_imoji.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_text_to_imoji.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_text_repeat.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_text_repeat.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_adamina.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/adamina.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable-night_baseline_arrow_forward_ios_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable-night/baseline_arrow_forward_ios_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_message_image.png.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/message_image.png"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_question_mark.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/question_mark.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_loading_background.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/loading_background.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/xml_backup_rules.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/xml/backup_rules.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_dialog_background.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/dialog_background.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable_baseline_refresh_24.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable/baseline_refresh_24.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_settings.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_settings.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-xhdpi/ic_launcher_foreground.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_item_stylish_text.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/item_stylish_text.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/font_anek_bangla_medium.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/font/anek_bangla_medium.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_item_sms.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/item_sms.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_item_repeat.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/item_repeat.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "com.anginatech.textrepeater.app-main-58:/mipmap-mdpi/ic_launcher_foreground.webp"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/drawable-night_copy_share_back.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/drawable-night/copy_share_back.xml"}, {"merged": "com.anginatech.textrepeater.app-debug-56:/layout_activity_main.xml.flat", "source": "com.anginatech.textrepeater.app-main-58:/layout/activity_main.xml"}]