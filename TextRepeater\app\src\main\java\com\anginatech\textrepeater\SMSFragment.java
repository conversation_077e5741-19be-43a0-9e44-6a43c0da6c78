package com.anginatech.textrepeater;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import java.util.ArrayList;
import java.util.List;


public class SMSFragment extends Fragment {

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_sms, container, false);

        RecyclerView recyclerView = view.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));

        List<String> smsList = getArguments() != null ? getArguments().getStringArrayList("smsList") : new ArrayList<>();

        if (smsList.isEmpty()) {
            Toast.makeText(requireContext(), "No messages available", Toast.LENGTH_SHORT).show();
        } else {
            SMSAdapter2 adapter = new SMSAdapter2(smsList, (sms, isChecked) -> {

                if (getActivity() instanceof Text_Repeat) {
                    ((Text_Repeat) getActivity()).updateSelectedSMS(sms);

                }

                Fragment parentFragment = getParentFragment();
                if (parentFragment instanceof SMSBottomSheetDialog) {
                    ((SMSBottomSheetDialog) parentFragment).dismiss();
                }
            });
            recyclerView.setAdapter(adapter);
        }

        return view;
    }
}