package com.anginatech.textrepeater;

public class PatternGenerator {

    public static String generatePattern(char character, String emoji) {
        StringBuilder pattern = new StringBuilder();

        switch (character) {
            case 'A':
                pattern.append("      ").append(emoji).append("\n ")
                        .append("  ").append(emoji).append(" ").append(emoji).append("\n")
                        .append("  ").append(emoji).append("    ").append(emoji).append("\n")
                        .append(" ").append(emoji).append(" "+emoji).append(""+emoji).append("\n")
                        .append(emoji).append("         ").append(emoji).append("\n");
                break;
            case 'B':
                pattern.append(emoji).append(" ").append(emoji).append(" ").append("\n")
                        .append(emoji).append("    ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append(" ").append("\n")
                        .append(emoji).append("    ").append(emoji).append("\n")
                        .append(emoji).append(" ").append(emoji).append(" ").append("\n");
                break;
            case 'C':
                pattern.append("  ").append(emoji).append(emoji).append("\n")
                        .append(emoji).append(" \n")
                        .append(emoji).append(" \n")
                        .append(emoji).append(" \n")
                        .append("  ").append(emoji).append(emoji).append("\n");
                break;
            case 'D':
                pattern.append(emoji).append(emoji).append("  \n")
                        .append(emoji).append("    ").append(emoji).append("\n")
                        .append(emoji).append("    ").append(emoji).append("\n")
                        .append(emoji).append("    ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append("  \n");
                break;
            case 'E':
                pattern.append(emoji).append(emoji).append(emoji).append("\n")
                        .append(emoji).append("\n")
                        .append(emoji).append(emoji).append("\n")
                        .append(emoji).append("\n")
                        .append(emoji).append(emoji).append(emoji).append("\n");
                break;
            case 'F':
                pattern.append(emoji).append(emoji).append(emoji).append("\n")
                        .append(emoji).append("\n")
                        .append(emoji).append(emoji).append("\n")
                        .append(emoji).append("\n")
                        .append(emoji).append("\n");
                break;
            case 'G':
                pattern.append("  ").append(emoji).append(emoji).append("\n")
                        .append(emoji).append("\n")
                        .append(emoji).append("    ").append(emoji).append(emoji).append("\n")
                        .append(emoji).append("      ").append(emoji).append("\n")
                        .append("   ").append(emoji).append(emoji).append("\n");
                break;
            case 'H':
                pattern.append(emoji).append("     ").append(emoji).append("\n")
                        .append(emoji).append("     ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append(emoji).append("\n")
                        .append(emoji).append("     ").append(emoji).append("\n")
                        .append(emoji).append("     ").append(emoji).append("\n");
                break;
            case 'I':
                pattern.append(emoji).append(emoji).append(emoji).append("\n")
                        .append("     ").append(emoji).append("\n")
                        .append("     ").append(emoji).append("\n")
                        .append("     ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append(emoji).append("\n");
                break;
            case 'J':
                pattern.append("  ").append(emoji).append(emoji).append("\n")
                        .append("        ").append(emoji).append("\n")
                        .append("        ").append(emoji).append("\n")
                        .append(emoji).append("   ").append(emoji).append("\n")
                        .append(" ").append(emoji).append(emoji).append("\n");
                break;
            case 'K':
                pattern.append(emoji).append("     ").append(emoji).append("\n")
                        .append(emoji).append("   ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append("\n")
                        .append(emoji).append("   ").append(emoji).append("\n")
                        .append(emoji).append("     ").append(emoji).append("\n");
                break;
            case 'L':
                pattern.append(emoji).append("\n")
                        .append(emoji).append("\n")
                        .append(emoji).append("\n")
                        .append(emoji).append("\n")
                        .append(emoji).append(emoji).append(emoji).append("\n");
                break;
            case 'M':
                pattern.append(emoji).append("               ").append(emoji).append("\n") // Top line
                        .append(emoji).append(emoji).append("     ").append(emoji).append(emoji).append("\n") // Second line
                        .append(emoji).append("  ").append(emoji).append(" ").append(emoji).append("  ").append(emoji).append("\n") // Third line
                        .append(emoji).append("   ").append("  ").append(emoji).append("     ").append(emoji).append("\n") // Fourth line
                        .append(emoji).append("   ").append("             ").append(emoji).append("\n"); // Bottom line
                break;
            case 'N':
                pattern.append(emoji).append("       ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append("  ").append(emoji).append("\n")
                        .append(emoji).append(" ").append(emoji).append(" ").append(emoji).append("\n")
                        .append(emoji).append("  ").append(emoji).append(emoji).append("\n")
                        .append(emoji).append("       ").append(emoji).append("\n");
                break;
            case 'O':
                pattern.append("   ").append(emoji).append(emoji).append("  ").append("\n")
                        .append(emoji).append("      ").append(emoji).append("\n")
                        .append(emoji).append("      ").append(emoji).append("\n")
                        .append(emoji).append("      ").append(emoji).append("\n")
                        .append("   ").append(emoji).append(emoji).append("  ").append("\n");
                break;
            case 'P':
                pattern.append(emoji).append(emoji).append("  \n")
                        .append(emoji).append("   ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append("  \n")
                        .append(emoji).append("\n")
                        .append(emoji).append("\n");
                break;
            case 'Q':
                pattern.append("  ").append(emoji).append(emoji).append(" \n")
                        .append(emoji).append("     ").append(emoji).append("\n")
                        .append(emoji).append("     ").append(emoji).append("\n")
                        .append(emoji).append("    ").append(emoji).append("\n")
                        .append("  ").append(emoji).append(emoji).append("").append(emoji).append("\n");
                break;
            case 'R':
                pattern.append(emoji).append(emoji).append("  \n")
                        .append(emoji).append("   ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append("  \n")
                        .append(emoji).append("   ").append(emoji).append("\n")
                        .append(emoji).append("     ").append(emoji).append("\n");
                break;
            case 'S':
                pattern.append(" ").append(emoji).append(emoji).append(emoji).append("\n")
                        .append(emoji).append("\n")
                        .append(" ").append(emoji).append(emoji).append(emoji).append("\n")
                        .append("             ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append(emoji).append("\n");
                break;
            case 'T':
                pattern.append(emoji).append(emoji).append(emoji).append("\n")
                        .append("     ").append(emoji).append("\n")
                        .append("     ").append(emoji).append("\n")
                        .append("     ").append(emoji).append("\n")
                        .append("     ").append(emoji).append("\n");
                break;
            case 'U':
                pattern.append(emoji).append("     ").append(emoji).append("\n")
                        .append(emoji).append("     ").append(emoji).append("\n")
                        .append(emoji).append("     ").append(emoji).append("\n")
                        .append(emoji).append("     ").append(emoji).append("\n")
                        .append("  ").append(emoji).append(emoji).append(" \n");
                break;
            case 'V':
                pattern.append(emoji).append("       ").append(emoji).append("\n")
                        .append(" ").append(emoji).append("     ").append(emoji).append(" \n")
                        .append("  ").append(emoji).append("   ").append(emoji).append("  \n")
                        .append("   ").append(emoji).append(" ").append(emoji).append("   \n")
                        .append("      ").append(emoji).append("    \n");
                break;

            case 'W':
                pattern.append(emoji).append("        ").append("               ").append(emoji).append("\n")
                        .append(emoji).append("       ").append(emoji).append("         ").append(emoji).append("\n")
                        .append(emoji).append("    ").append(emoji).append(" ").append(emoji).append("    ").append(emoji).append("\n")
                        .append(" ").append(emoji).append(" ").append(emoji).append("     ").append(emoji).append(" ").append(emoji).append("\n")
                        .append("  ").append(emoji).append("               ").append(emoji).append("\n");
                break;
            case 'X':
                pattern.append(emoji).append("       ").append(emoji).append("\n")
                        .append("  ").append(emoji).append("  ").append(emoji).append("\n")
                        .append("     ").append(emoji).append("\n")
                        .append("  ").append(emoji).append("  ").append(emoji).append("\n")
                        .append(emoji).append("       ").append(emoji).append("\n");
                break;
            case 'Y':
                pattern.append(emoji).append("      ").append(emoji).append("\n")
                        .append("  ").append(emoji).append(" ").append(emoji).append(" \n")
                        .append("     ").append(emoji).append(" \n")
                        .append("     ").append(emoji).append(" \n")
                        .append("     ").append(emoji).append(" \n");
                break;
            case 'Z':
                pattern.append(emoji).append(emoji).append(emoji).append("\n")
                        .append("        ").append(emoji).append("\n")
                        .append("     ").append(emoji).append(" \n")
                        .append("  ").append(emoji).append("  \n")
                        .append(emoji).append(emoji).append(emoji).append("\n");
                break;

            case '0':
                pattern.append("  ").append(emoji).append(emoji).append("  ").append("\n")
                        .append(emoji).append("    ").append(emoji).append("\n")
                        .append(emoji).append("    ").append(emoji).append("\n")
                        .append(emoji).append("    ").append(emoji).append("\n")
                        .append("  ").append(emoji).append(emoji).append("  ").append("\n");
                break;

            case '1':
                pattern.append("     ").append(emoji).append("\n")
                        .append(emoji).append("").append(emoji).append(" \n")
                        .append("     ").append(emoji).append("\n")
                        .append("     ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append(emoji).append("\n");
                break;
            case '2':
                pattern.append(" ").append(emoji).append(emoji).append(" ").append("\n")
                        .append("        ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append(" ").append("\n")
                        .append(emoji).append("\n")
                        .append(emoji).append(emoji).append(emoji).append("\n");
                break;
            case '3':
                pattern.append(" ").append(emoji).append(emoji).append(" ").append("\n")
                        .append("        ").append(emoji).append("\n")
                        .append(" ").append(emoji).append(emoji).append(" ").append("\n")
                        .append("        ").append(emoji).append("\n")
                        .append(" ").append(emoji).append(emoji).append(" ").append("\n");
                break;
            case '4':
                pattern.append(emoji).append("   ").append(emoji).append("\n")
                        .append(emoji).append("   ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append(emoji).append("\n")
                        .append("       ").append(emoji).append("\n")
                        .append("       ").append(emoji).append("\n");
                break;
            case '5':
                pattern.append(emoji).append(emoji).append(emoji).append("\n")
                        .append(emoji).append("\n")
                        .append(emoji).append(emoji).append(" ").append("\n")
                        .append("       ").append(emoji).append("\n")
                        .append(emoji).append(emoji).append("\n");
                break;
            case '6':
                pattern.append(" ").append(emoji).append(emoji).append(" ").append("\n")
                        .append(emoji).append("\n")
                        .append(emoji).append(emoji).append(" ").append(" \n")
                        .append(emoji).append("   ").append(emoji).append("\n")
                        .append(" ").append(emoji).append(emoji).append(" ").append("\n");
                break;
            case '7':
                pattern.append(emoji).append(emoji).append(emoji).append("\n")
                        .append("         ").append(emoji).append("\n")
                        .append("      ").append(emoji).append("\n")
                        .append("   ").append(emoji).append("\n")
                        .append(emoji).append(" \n");
                break;
            case '8':
                pattern.append(" ").append(emoji).append(emoji).append(" ").append("\n")
                        .append(emoji).append("    ").append(emoji).append("\n")
                        .append(" ").append(emoji).append(emoji).append("  ").append(" \n")
                        .append(emoji).append("    ").append(emoji).append("\n")
                        .append(" ").append(emoji).append(emoji).append(" ").append("\n");
                break;
            case '9':
                pattern.append("  ").append(emoji).append(emoji).append("  ").append("\n")
                        .append(emoji).append("    ").append(emoji).append(" \n")
                        .append("  ").append(emoji).append(emoji).append("\n")
                        .append("     ").append(emoji).append("\n")
                        .append("   ").append(emoji).append("\n");
                break;

            default:
                pattern.append("Pattern not defined for: ").append(character).append("\n");
        }

        return pattern.toString();
    }


}