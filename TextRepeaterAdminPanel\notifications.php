<?php
$page_title = 'Send Notifications';
require_once 'includes/header.php';
require_once 'classes/NotificationManager.php';

$notificationManager = new NotificationManager();
$current_user = $auth->getCurrentUser();

$success_message = '';
$error_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $title = trim($_POST['title'] ?? '');
    $message = trim($_POST['message'] ?? '');
    $image_url = trim($_POST['image_url'] ?? '');
    $action_url = trim($_POST['action_url'] ?? '');
    $target_type = $_POST['target_type'] ?? 'all';
    $send_type = $_POST['send_type'] ?? 'immediate';
    $scheduled_datetime = $_POST['scheduled_datetime'] ?? '';
    
    // Validation
    if (empty($title)) {
        $error_message = 'Title is required.';
    } elseif (empty($message)) {
        $error_message = 'Message is required.';
    } elseif ($send_type === 'scheduled' && empty($scheduled_datetime)) {
        $error_message = 'Scheduled date and time is required.';
    } elseif ($send_type === 'scheduled' && strtotime($scheduled_datetime) <= time()) {
        $error_message = 'Scheduled time must be in the future.';
    } else {
        // Prepare notification data
        $notification_data = [
            'title' => $title,
            'message' => $message,
            'image_url' => $image_url,
            'action_url' => $action_url,
            'target_type' => $target_type,
            'target_users' => null, // For now, we'll implement specific user targeting later
            'status' => $send_type === 'immediate' ? 'sent' : 'scheduled',
            'scheduled_at' => $send_type === 'scheduled' ? $scheduled_datetime : null,
            'created_by' => $current_user['id']
        ];
        
        $result = $notificationManager->createNotification($notification_data);
        
        if ($result['success']) {
            if ($send_type === 'immediate') {
                $success_message = 'Notification sent successfully!';
            } else {
                $success_message = 'Notification scheduled successfully!';
            }
            
            // Clear form data
            $_POST = [];
        } else {
            $error_message = $result['message'];
        }
    }
}

// Get user statistics for targeting info
$database = new Database();
$conn = $database->getConnection();

$stmt = $conn->prepare("SELECT COUNT(*) as total FROM app_users WHERE fcm_token IS NOT NULL AND fcm_token != ''");
$stmt->execute();
$total_users_with_tokens = $stmt->fetch()['total'];

$stmt = $conn->prepare("SELECT COUNT(*) as active FROM app_users WHERE fcm_token IS NOT NULL AND fcm_token != '' AND last_seen >= DATE_SUB(NOW(), INTERVAL 7 DAY)");
$stmt->execute();
$active_users_with_tokens = $stmt->fetch()['active'];
?>

<!-- Notification Form -->
<?php if (!empty($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (!empty($error_message)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-paper-plane me-2"></i>
                    Create Notification
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="title" class="form-label">Notification Title *</label>
                        <input type="text" class="form-control" id="title" name="title" 
                               value="<?php echo htmlspecialchars($_POST['title'] ?? ''); ?>" 
                               maxlength="100" required>
                        <div class="form-text">Maximum 100 characters</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="message" class="form-label">Message *</label>
                        <textarea class="form-control" id="message" name="message" rows="4" 
                                  maxlength="500" required><?php echo htmlspecialchars($_POST['message'] ?? ''); ?></textarea>
                        <div class="form-text">Maximum 500 characters</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="image_url" class="form-label">Image URL (Optional)</label>
                        <input type="url" class="form-control" id="image_url" name="image_url" 
                               value="<?php echo htmlspecialchars($_POST['image_url'] ?? ''); ?>" 
                               placeholder="https://example.com/image.jpg">
                        <div class="form-text">URL to an image to display in the notification</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="action_url" class="form-label">Action URL (Optional)</label>
                        <input type="url" class="form-control" id="action_url" name="action_url" 
                               value="<?php echo htmlspecialchars($_POST['action_url'] ?? ''); ?>" 
                               placeholder="https://example.com/action">
                        <div class="form-text">URL to open when notification is tapped</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="target_type" class="form-label">Target Audience</label>
                                <select class="form-select" id="target_type" name="target_type">
                                    <option value="all" <?php echo ($_POST['target_type'] ?? '') === 'all' ? 'selected' : ''; ?>>
                                        All Users (<?php echo number_format($total_users_with_tokens); ?> users)
                                    </option>
                                    <option value="active_users" <?php echo ($_POST['target_type'] ?? '') === 'active_users' ? 'selected' : ''; ?>>
                                        Active Users (<?php echo number_format($active_users_with_tokens); ?> users)
                                    </option>
                                </select>
                                <div class="form-text">Choose who should receive this notification</div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="send_type" class="form-label">Send Type</label>
                                <select class="form-select" id="send_type" name="send_type" onchange="toggleScheduleField()">
                                    <option value="immediate" <?php echo ($_POST['send_type'] ?? '') === 'immediate' ? 'selected' : ''; ?>>
                                        Send Immediately
                                    </option>
                                    <option value="scheduled" <?php echo ($_POST['send_type'] ?? '') === 'scheduled' ? 'selected' : ''; ?>>
                                        Schedule for Later
                                    </option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3" id="schedule_field" style="display: none;">
                        <label for="scheduled_datetime" class="form-label">Schedule Date & Time</label>
                        <input type="datetime-local" class="form-control" id="scheduled_datetime" name="scheduled_datetime" 
                               value="<?php echo htmlspecialchars($_POST['scheduled_datetime'] ?? ''); ?>">
                        <div class="form-text">Select when to send this notification</div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-2"></i>
                            <span id="submit_text">Send Notification</span>
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="previewNotification()">
                            <i class="fas fa-eye me-2"></i>
                            Preview
                        </button>
                        <button type="reset" class="btn btn-outline-danger">
                            <i class="fas fa-times me-2"></i>
                            Clear
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <!-- Preview Card -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-mobile-alt me-2"></i>
                    Notification Preview
                </h6>
            </div>
            <div class="card-body">
                <div class="notification-preview" id="notification_preview">
                    <div class="preview-phone">
                        <div class="preview-notification">
                            <div class="preview-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="preview-content">
                                <div class="preview-title" id="preview_title">Your App Name</div>
                                <div class="preview-message" id="preview_message">Your notification message will appear here...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Audience Statistics
                </h6>
            </div>
            <div class="card-body">
                <div class="stat-item mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Total Users with FCM:</span>
                        <strong><?php echo number_format($total_users_with_tokens); ?></strong>
                    </div>
                </div>
                <div class="stat-item mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Active Users (7 days):</span>
                        <strong><?php echo number_format($active_users_with_tokens); ?></strong>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="d-flex justify-content-between">
                        <span>Engagement Rate:</span>
                        <strong><?php echo $total_users_with_tokens > 0 ? number_format(($active_users_with_tokens / $total_users_with_tokens) * 100, 1) : 0; ?>%</strong>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Tips -->
        <div class="card">
            <div class="card-header">
                <h6 class="card-title mb-0">
                    <i class="fas fa-lightbulb me-2"></i>
                    Tips for Better Notifications
                </h6>
            </div>
            <div class="card-body">
                <ul class="list-unstyled mb-0">
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Keep titles under 50 characters
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Use clear, actionable language
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Test with different user segments
                    </li>
                    <li class="mb-2">
                        <i class="fas fa-check text-success me-2"></i>
                        Schedule for optimal times
                    </li>
                    <li>
                        <i class="fas fa-check text-success me-2"></i>
                        Include relevant images when possible
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>

<style>
.notification-preview {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
}

.preview-phone {
    background: #333;
    border-radius: 20px;
    padding: 10px;
    max-width: 250px;
    margin: 0 auto;
}

.preview-notification {
    background: white;
    border-radius: 10px;
    padding: 12px;
    display: flex;
    align-items: flex-start;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.preview-icon {
    width: 40px;
    height: 40px;
    background: var(--primary-color);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 12px;
    flex-shrink: 0;
}

.preview-content {
    flex: 1;
    min-width: 0;
}

.preview-title {
    font-weight: 600;
    font-size: 14px;
    color: #333;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.preview-message {
    font-size: 12px;
    color: #666;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.stat-item {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
}

.stat-item:last-child {
    border-bottom: none;
}
</style>

<?php
$page_scripts = "
<script>
function toggleScheduleField() {
    const sendType = document.getElementById('send_type').value;
    const scheduleField = document.getElementById('schedule_field');
    const submitText = document.getElementById('submit_text');
    
    if (sendType === 'scheduled') {
        scheduleField.style.display = 'block';
        submitText.textContent = 'Schedule Notification';
    } else {
        scheduleField.style.display = 'none';
        submitText.textContent = 'Send Notification';
    }
}

function updatePreview() {
    const title = document.getElementById('title').value || 'Your App Name';
    const message = document.getElementById('message').value || 'Your notification message will appear here...';
    
    document.getElementById('preview_title').textContent = title;
    document.getElementById('preview_message').textContent = message;
}

function previewNotification() {
    const title = document.getElementById('title').value;
    const message = document.getElementById('message').value;
    
    if (!title || !message) {
        showAlert('Preview Error', 'Please enter both title and message to preview.', 'warning');
        return;
    }
    
    updatePreview();
    showAlert('Preview', 'Check the preview panel on the right to see how your notification will look.', 'info');
}

// Update preview in real-time
document.getElementById('title').addEventListener('input', updatePreview);
document.getElementById('message').addEventListener('input', updatePreview);

// Initialize
toggleScheduleField();
updatePreview();
</script>
";

require_once 'includes/footer.php';
?>
