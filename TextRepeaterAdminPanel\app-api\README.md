# Text Repeater App API

A well-organized REST API for the Text Repeater Android application, providing endpoints for AdMob integration, user management, app settings, and push notifications.

## 📁 Project Structure

```
app-api/
├── index.php                 # Main API router and entry point
├── config/
│   └── api-config.php       # API configuration settings
├── middleware/
│   └── auth.php             # Authentication and rate limiting
├── utils/
│   └── response.php         # Response formatting utilities
├── endpoints/
│   ├── ads.php              # AdMob related endpoints
│   ├── users.php            # User management endpoints
│   ├── settings.php         # App settings endpoints
│   └── notifications.php    # Push notification endpoints
└── README.md                # This documentation
```

## 🚀 API Endpoints

### Base URL
```
http://your-domain.com/app-api/
```

### Health Check
- **GET** `/health` - API health status
- **GET** `/version` - API version and available endpoints

### 📱 AdMob Endpoints (`/ads`)
- **GET** `/ads/config` - Get AdMob configuration
- **POST** `/ads/track` - Track ad events (impressions, clicks)
- **GET** `/ads/analytics?device_id=xxx` - Get ad analytics for device
- **GET** `/ads/units` - Get available ad units

### 👤 User Endpoints (`/users`)
- **POST** `/users/register` - Register/update user
- **POST** `/users/update` - Update user information
- **GET** `/users/profile?device_id=xxx` - Get user profile
- **GET** `/users/stats?device_id=xxx` - Get user statistics

### ⚙️ Settings Endpoints (`/settings`)
- **GET** `/settings/settings` - Get app settings
- **GET** `/settings/config` - Get app configuration
- **GET** `/settings/features` - Get feature flags
- **GET** `/settings/version?version=xxx&platform=android` - Check app version
- **GET** `/settings/maintenance` - Get maintenance status

### 🔔 Notification Endpoints (`/notifications`)
- **POST** `/notifications/delivered` - Mark notification as delivered
- **POST** `/notifications/clicked` - Mark notification as clicked
- **POST** `/notifications/register` - Register FCM token
- **POST** `/notifications/unregister` - Unregister FCM token
- **GET** `/notifications/history?device_id=xxx` - Get notification history

## 📝 Request/Response Format

### Request Headers
```
Content-Type: application/json
X-API-Key: your-api-key (if required)
```

### Success Response Format
```json
{
  "success": true,
  "data": { ... },
  "timestamp": "2024-01-01T12:00:00+00:00",
  "version": "1.0.0"
}
```

### Error Response Format
```json
{
  "success": false,
  "error": {
    "message": "Error description",
    "code": 400
  },
  "timestamp": "2024-01-01T12:00:00+00:00",
  "version": "1.0.0"
}
```

## 🔧 Configuration

### Environment Variables
Edit `config/api-config.php` to configure:

- **API_VERSION**: Current API version
- **API_KEY_REQUIRED**: Enable/disable API key authentication
- **API_RATE_LIMIT**: Requests per hour per IP
- **ADMOB_API_ENABLED**: Enable AdMob features
- **FCM_SERVER_KEY**: Firebase Cloud Messaging server key

### Database Connection
The API uses the existing TextRepeaterAdminPanel database configuration.

## 🔐 Authentication

### API Key (Optional)
If `API_KEY_REQUIRED` is enabled, include API key in headers:
```
X-API-Key: your-api-key
```

### Rate Limiting
- Default: 1000 requests per hour per IP
- Configurable in `api-config.php`

## 📊 Monitoring & Logging

### Request Logging
All API requests are logged with:
- Timestamp
- Method and path
- IP address
- User agent

### Error Logging
Errors are logged to PHP error log with detailed context.

## 🚀 Usage Examples

### Register User
```bash
curl -X POST http://your-domain.com/app-api/users/register \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "unique-device-id",
    "fcm_token": "fcm-token",
    "app_version": "1.0.0",
    "android_version": "11",
    "device_model": "Samsung Galaxy S21"
  }'
```

### Get AdMob Config
```bash
curl -X GET http://your-domain.com/app-api/ads/config
```

### Track Ad Event
```bash
curl -X POST http://your-domain.com/app-api/ads/track \
  -H "Content-Type: application/json" \
  -d '{
    "device_id": "unique-device-id",
    "ad_type": "banner",
    "event_type": "impression",
    "ad_unit_id": "ca-app-pub-xxx"
  }'
```

## 🛠️ Development

### Adding New Endpoints
1. Create endpoint handler in `endpoints/` directory
2. Add route in `index.php`
3. Update this documentation

### Testing
Use tools like Postman or curl to test endpoints.

## 📄 License
This API is part of the Text Repeater project.
