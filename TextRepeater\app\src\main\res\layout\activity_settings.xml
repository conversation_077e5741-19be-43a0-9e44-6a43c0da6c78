<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Settings_Activity"
    android:background="@color/mother_layout_color"
    >



    <androidx.core.widget.NestedScrollView
        android:id="@+id/nestedScrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">


            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/materialToolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                app:layout_constraintBottom_toTopOf="@+id/imageView"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                app:navigationIcon="@drawable/baseline_arrow_back_ios_24"
                app:navigationIconTint="@color/toolbar_text_color"
                app:title="Settings"
                app:titleTextColor="@color/toolbar_text_color">


            </com.google.android.material.appbar.MaterialToolbar>

            <ImageView
                android:id="@+id/imageView"
                android:layout_width="100dp"
                android:layout_height="100dp"
                android:layout_marginTop="45dp"
                android:scaleType="centerCrop"
                android:src="@drawable/logo"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.498"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.067" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout2"
                android:layout_width="match_parent"
                android:layout_height="110dp"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="45dp"
                android:background="@drawable/settings_layout_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/imageView"
                app:layout_constraintVertical_bias="0.0">


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout3"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:clickable="true"
                    android:foreground="?attr/selectableItemBackground"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0">


                    <ImageView
                        android:id="@+id/imageView3"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginStart="10dp"
                        android:src="@drawable/night_mode"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.005"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/textView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:fontFamily="sans-serif"
                        android:text="Night Mode"
                        android:textColor="@color/primary_text"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintStart_toEndOf="@+id/imageView3"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.516" />


                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/nightSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="9dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="1.0"
                        app:layout_constraintStart_toEndOf="@+id/textView"
                        app:layout_constraintTop_toTopOf="parent"
                        app:thumbTint="@color/love"
                        app:trackTint="@color/mother_layout_color" />


                </androidx.constraintlayout.widget.ConstraintLayout>


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout_Contact"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:clickable="true"
                    android:foreground="?attr/selectableItemBackground"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/constraintLayout3"
                    app:layout_constraintVertical_bias="0.0"
                    >


                    <ImageView
                        android:id="@+id/imageView25"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="5dp"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/question_mark"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.005"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/textView14"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_marginBottom="5dp"
                        android:fontFamily="sans-serif"
                        android:text="Contact"
                        android:textColor="@color/primary_text"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintStart_toEndOf="@+id/imageView25"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.516" />


                    <ImageView
                        android:id="@+id/nightSwitch1"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="19dp"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/baseline_arrow_forward_ios_24"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="1.0"
                        app:layout_constraintStart_toEndOf="@+id/textView14"
                        app:layout_constraintTop_toTopOf="parent"

                        />


                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout4"
                android:layout_width="match_parent"
                android:layout_height="220dp"
                android:layout_marginHorizontal="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="20dp"
                android:background="@drawable/settings_layout_back"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/constraintLayout2"
                app:layout_constraintVertical_bias="0.0">


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout_Share"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:clickable="true"
                    android:foreground="?attr/selectableItemBackground"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.0">

                    <ImageView
                        android:id="@+id/imageView28"
                        android:layout_width="30dp"
                        android:layout_height="29dp"
                        android:layout_marginStart="10dp"
                        android:src="@drawable/share"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.005"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"

                        />

                    <TextView
                        android:id="@+id/textView20"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_marginBottom="2dp"
                        android:fontFamily="sans-serif"
                        android:text="Share"
                        android:textColor="@color/primary_text"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintStart_toEndOf="@+id/imageView28"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.516"

                        />


                    <ImageView
                        android:id="@+id/rateUs"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="19dp"
                        android:src="@drawable/baseline_arrow_forward_ios_24"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="1.0"
                        app:layout_constraintStart_toEndOf="@+id/textView20"
                        app:layout_constraintTop_toTopOf="parent"

                        />


                </androidx.constraintlayout.widget.ConstraintLayout>


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout_Rate_Us"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:clickable="true"
                    android:foreground="?attr/selectableItemBackground"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/constraintLayout_Share"
                    app:layout_constraintVertical_bias="0.0">

                    <ImageView
                        android:id="@+id/imageView17"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/star"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.005"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"

                        />

                    <TextView
                        android:id="@+id/textView02"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_marginBottom="5dp"
                        android:fontFamily="sans-serif"
                        android:text="Rate us"
                        android:textColor="@color/primary_text"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintStart_toEndOf="@+id/imageView17"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.516"

                        />


                    <ImageView
                        android:id="@+id/shareUs"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="19dp"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/baseline_arrow_forward_ios_24"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="1.0"
                        app:layout_constraintStart_toEndOf="@+id/textView02"
                        app:layout_constraintTop_toTopOf="parent" />


                </androidx.constraintlayout.widget.ConstraintLayout>


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout_About_Us"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:clickable="true"
                    android:foreground="?attr/selectableItemBackground"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.0"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/constraintLayout_Rate_Us"
                    app:layout_constraintVertical_bias="0.036">

                    <ImageView
                        android:id="@+id/imageView98"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/info"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.005"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"

                        />

                    <TextView
                        android:id="@+id/textView09"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_marginBottom="5dp"
                        android:fontFamily="sans-serif"
                        android:text="About us"
                        android:textColor="@color/primary_text"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintStart_toEndOf="@+id/imageView98"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.516"

                        />


                    <ImageView
                        android:id="@+id/aboutUs"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="19dp"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/baseline_arrow_forward_ios_24"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="1.0"
                        app:layout_constraintStart_toEndOf="@+id/textView09"
                        app:layout_constraintTop_toTopOf="parent" />


                </androidx.constraintlayout.widget.ConstraintLayout>


                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout_Privacy"
                    android:layout_width="match_parent"
                    android:layout_height="55dp"
                    android:clickable="true"
                    android:foreground="?attr/selectableItemBackground"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/constraintLayout_About_Us">

                    <ImageView
                        android:id="@+id/imageView8"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginStart="10dp"
                        android:layout_marginBottom="9dp"
                        android:src="@drawable/privacy"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.005"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"

                        />

                    <TextView
                        android:id="@+id/textView06"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="12dp"
                        android:layout_marginBottom="9dp"
                        android:fontFamily="sans-serif"
                        android:text="Privacy policy"
                        android:textColor="@color/primary_text"
                        android:textSize="16sp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.0"
                        app:layout_constraintStart_toEndOf="@+id/imageView8"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.516"

                        />


                    <ImageView
                        android:id="@+id/faqUs"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:layout_marginEnd="19dp"
                        android:layout_marginBottom="5dp"
                        android:src="@drawable/baseline_arrow_forward_ios_24"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="1.0"
                        app:layout_constraintStart_toEndOf="@+id/textView06"
                        app:layout_constraintTop_toTopOf="parent" />


                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.constraintlayout.widget.ConstraintLayout>


            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="18dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/constraintLayout4"
                android:text="Version : 3.9.1"
                android:gravity="center"
                android:textSize="18sp"
                android:textColor="@color/secondary_text"
                android:fontFamily="@font/alice"

                />


        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.core.widget.NestedScrollView>






</androidx.constraintlayout.widget.ConstraintLayout>