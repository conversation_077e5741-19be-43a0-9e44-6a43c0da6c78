package com.anginatech.textrepeater;

import android.Manifest;
import android.app.Activity;
import android.app.NotificationManager;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.core.app.ActivityCompat;
import androidx.core.app.NotificationManagerCompat;
import androidx.core.content.ContextCompat;

/**
 * Helper class for managing notification permissions
 * Handles runtime permission requests for Android 13+ (API 33+)
 */
public class NotificationPermissionHelper {
    
    private static final String TAG = "NotificationPermission";
    private static final String PREFS_NAME = "NotificationPermissionPrefs";
    private static final String KEY_PERMISSION_REQUESTED = "permission_requested";
    private static final String KEY_PERMISSION_DENIED_COUNT = "permission_denied_count";
    private static final String KEY_LAST_REQUEST_TIME = "last_request_time";
    
    public static final int NOTIFICATION_PERMISSION_REQUEST_CODE = 1001;
    private static final long MIN_REQUEST_INTERVAL = 24 * 60 * 60 * 1000; // 24 hours
    private static final int MAX_DENIAL_COUNT = 2;
    
    /**
     * Check if notification permission is granted
     */
    public static boolean isNotificationPermissionGranted(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Android 13+ requires runtime permission
            return ContextCompat.checkSelfPermission(context, Manifest.permission.POST_NOTIFICATIONS) 
                   == PackageManager.PERMISSION_GRANTED;
        } else {
            // For older versions, check if notifications are enabled
            return NotificationManagerCompat.from(context).areNotificationsEnabled();
        }
    }
    
    /**
     * Check if we should request notification permission
     */
    public static boolean shouldRequestPermission(Context context) {
        if (isNotificationPermissionGranted(context)) {
            return false;
        }
        
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        
        // Check denial count
        int denialCount = prefs.getInt(KEY_PERMISSION_DENIED_COUNT, 0);
        if (denialCount >= MAX_DENIAL_COUNT) {
            Log.d(TAG, "Permission denied too many times, not requesting again");
            return false;
        }
        
        // Check time interval
        long lastRequestTime = prefs.getLong(KEY_LAST_REQUEST_TIME, 0);
        long currentTime = System.currentTimeMillis();
        
        if (currentTime - lastRequestTime < MIN_REQUEST_INTERVAL) {
            Log.d(TAG, "Too soon to request permission again");
            return false;
        }
        
        return true;
    }
    
    /**
     * Request notification permission for Android 13+
     */
    public static void requestNotificationPermission(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (shouldRequestPermission(activity)) {
                Log.d(TAG, "Requesting notification permission");
                
                // Update request time
                updateRequestTime(activity);
                
                ActivityCompat.requestPermissions(
                    activity,
                    new String[]{Manifest.permission.POST_NOTIFICATIONS},
                    NOTIFICATION_PERMISSION_REQUEST_CODE
                );
            }
        } else {
            // For older versions, check if notifications are disabled and guide user to settings
            if (!NotificationManagerCompat.from(activity).areNotificationsEnabled()) {
                showNotificationSettingsDialog(activity);
            }
        }
    }
    
    /**
     * Handle permission request result
     */
    public static void handlePermissionResult(Context context, int requestCode, 
                                            @NonNull String[] permissions, 
                                            @NonNull int[] grantResults) {
        if (requestCode == NOTIFICATION_PERMISSION_REQUEST_CODE) {
            if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                Log.d(TAG, "Notification permission granted");
                resetDenialCount(context);
                onPermissionGranted(context);
            } else {
                Log.d(TAG, "Notification permission denied");
                incrementDenialCount(context);
                onPermissionDenied(context);
            }
        }
    }
    
    /**
     * Called when permission is granted
     */
    private static void onPermissionGranted(Context context) {
        // Initialize FCM token refresh
        MyFirebaseMessagingService.refreshFCMToken(context);
        
        Log.d(TAG, "Notification permission granted - FCM token refresh initiated");
    }
    
    /**
     * Called when permission is denied
     */
    private static void onPermissionDenied(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        int denialCount = prefs.getInt(KEY_PERMISSION_DENIED_COUNT, 0);
        
        if (denialCount >= MAX_DENIAL_COUNT) {
            Log.d(TAG, "Permission denied multiple times - showing settings guidance");
            // Could show a dialog explaining why notifications are important
        }
    }
    
    /**
     * Show dialog to guide user to notification settings
     */
    private static void showNotificationSettingsDialog(Activity activity) {
        // This could be implemented with a custom dialog
        // For now, we'll just log and optionally open settings
        Log.d(TAG, "Notifications are disabled - user should enable in settings");
        
        // Optionally open notification settings
        openNotificationSettings(activity);
    }
    
    /**
     * Open notification settings for the app
     */
    public static void openNotificationSettings(Context context) {
        Intent intent = new Intent();
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            intent.setAction(Settings.ACTION_APP_NOTIFICATION_SETTINGS);
            intent.putExtra(Settings.EXTRA_APP_PACKAGE, context.getPackageName());
        } else {
            intent.setAction(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
            intent.setData(Uri.parse("package:" + context.getPackageName()));
        }
        
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
        
        Log.d(TAG, "Opened notification settings");
    }
    
    /**
     * Update the last request time
     */
    private static void updateRequestTime(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putLong(KEY_LAST_REQUEST_TIME, System.currentTimeMillis());
        editor.putBoolean(KEY_PERMISSION_REQUESTED, true);
        editor.apply();
    }
    
    /**
     * Increment denial count
     */
    private static void incrementDenialCount(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        int currentCount = prefs.getInt(KEY_PERMISSION_DENIED_COUNT, 0);
        
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(KEY_PERMISSION_DENIED_COUNT, currentCount + 1);
        editor.apply();
        
        Log.d(TAG, "Permission denial count: " + (currentCount + 1));
    }
    
    /**
     * Reset denial count when permission is granted
     */
    private static void resetDenialCount(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(KEY_PERMISSION_DENIED_COUNT, 0);
        editor.apply();
        
        Log.d(TAG, "Permission denial count reset");
    }
    
    /**
     * Check if notification channels are properly configured
     */
    public static boolean areNotificationChannelsConfigured(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationManager notificationManager = 
                (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
            
            return notificationManager.getNotificationChannel("text_repeater_notifications") != null;
        }
        return true; // Channels not needed for older versions
    }
    
    /**
     * Get permission status summary
     */
    public static String getPermissionStatusSummary(Context context) {
        boolean isGranted = isNotificationPermissionGranted(context);
        boolean channelsConfigured = areNotificationChannelsConfigured(context);
        
        if (isGranted && channelsConfigured) {
            return "Notifications fully enabled";
        } else if (isGranted) {
            return "Permission granted, channels need setup";
        } else {
            return "Notification permission required";
        }
    }
}
