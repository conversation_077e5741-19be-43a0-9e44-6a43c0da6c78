package com.anginatech.textrepeater;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.AppCompatCheckBox;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.google.android.material.appbar.MaterialToolbar;

public class Blank_Text_Activity extends AppCompatActivity {


    MaterialToolbar blank_MaterialToolbar;
    ConstraintLayout blank_layout_Copy_Share,blankLayout_Copy,blankLayout_Share;
    EditText blank_editEnterlimit;
    AppCompatCheckBox blank_New_Line;
    Button blank_buttonRepeat;
    TextView blank_Display;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_blank_text);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        SharedPreferences sharedPreferences = getSharedPreferences("nightModePrefs", MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }

        blank_MaterialToolbar = findViewById(R.id.blank_MaterialToolbar);
        blank_layout_Copy_Share = findViewById(R.id.blank_layout_Copy_Share);
        blankLayout_Copy = findViewById(R.id.blankLayout_Copy);
        blankLayout_Share = findViewById(R.id.blankLayout_Share);
        blank_editEnterlimit = findViewById(R.id.blank_editEnterlimit);
        blank_New_Line = findViewById(R.id.blank_New_Line);
        blank_buttonRepeat = findViewById(R.id.blank_buttonRepeat);
        blank_Display = findViewById(R.id.blank_Display);



        blank_MaterialToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(Blank_Text_Activity.this, MainActivity.class));
                finish();
            }
        });

        blank_MaterialToolbar.setTitleTextAppearance(Blank_Text_Activity.this,R.style.RobotoBoldTextAppearance);

        blank_MaterialToolbar.setOnMenuItemClickListener(new Toolbar.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {
                if (item.getItemId()==R.id.clear_Data){
                    blank_layout_Copy_Share.setVisibility(View.GONE);
                    blank_Display.setText("");
                    blank_editEnterlimit.setText("");
                    blank_New_Line.setChecked(false);
                    Toast.makeText(Blank_Text_Activity.this, "Cleared", Toast.LENGTH_SHORT).show();
                }

                return true;
            }
        });


        blank_buttonRepeat.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                generateBlankText();

            }
        });


        blankLayout_Copy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String text = blank_Display.getText().toString();
                blankToClipboard(text);

            }
        });

        blankLayout_Share.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String text = blank_Display.getText().toString();
                blankShareText(text);
            }
        });




    }


    @Override
    public void onBackPressed() {

        startActivity(new Intent(Blank_Text_Activity.this, MainActivity.class));
        finish();

        super.onBackPressed();
    }


    private void generateBlankText() {
        String limitText = blank_editEnterlimit.getText().toString();

        // Validate input
        if (TextUtils.isEmpty(limitText)) {
            Toast.makeText(this, "Please enter a limit", Toast.LENGTH_SHORT).show();
            return;
        }

        int limit;
        try {
            limit = Integer.parseInt(limitText);
        } catch (NumberFormatException e) {
            Toast.makeText(this, "Invalid limit value", Toast.LENGTH_SHORT).show();
            return;
        }

        if (limit <= 0 || limit > 5000) {
            Toast.makeText(this, "Limit must be between 1 and 5,000", Toast.LENGTH_SHORT).show();
            return;
        }

        // Use a thread to generate blank text
        new Thread(() -> {
            StringBuilder result = new StringBuilder();
            for (int i = 0; i < limit; i++) {
                result.append(",");
                if (blank_New_Line.isChecked()) {
                    result.append("\n");
                }
            }
            // Update the UI on the main thread
            new Handler(Looper.getMainLooper()).post(() -> blank_Display.setText(result.toString()));

        }).start();

        blank_layout_Copy_Share.setVisibility(View.VISIBLE);
    }



    private void blankToClipboard(String text) {
        if (text.isEmpty()) {
            Toast.makeText(this, "No text to copy!", Toast.LENGTH_SHORT).show();
            return;
        }

        if (text.length()>100000) {
            Toast.makeText(this, "Text too long to copy (Max 100k characters)", Toast.LENGTH_LONG).show();
            return;
        }
        android.content.ClipboardManager clipboard =
                (android.content.ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
        android.content.ClipData clip = android.content.ClipData.newPlainText("text_to_emoji", text);
        clipboard.setPrimaryClip(clip);

        Toast.makeText(this, "Text copied to clipboard!", Toast.LENGTH_SHORT).show();
    }


    private void blankShareText(String text) {
        if (text.isEmpty()) {
            Toast.makeText(this, "No text to share!", Toast.LENGTH_SHORT).show();
            return;
        }

        if (text.length()>50000) {
            Toast.makeText(this, "Text too long to share (Max 50k characters)", Toast.LENGTH_LONG).show();
            return;
        }
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, text);

        startActivity(Intent.createChooser(shareIntent, "Share via"));
    }










}