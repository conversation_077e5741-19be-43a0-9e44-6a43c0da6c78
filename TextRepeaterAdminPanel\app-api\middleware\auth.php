<?php
/**
 * API Authentication Middleware
 * Handles API key validation and rate limiting
 */

class APIAuth {
    private $conn;
    
    public function __construct() {
        $this->conn = getAPIDatabase();
    }
    
    /**
     * Validate API request
     */
    public function validateRequest() {
        // Check maintenance mode
        if (isMaintenanceMode()) {
            return false;
        }
        
        // Check rate limiting
        if (!$this->checkRateLimit()) {
            return false;
        }
        
        // If API key is required, validate it
        if (API_KEY_REQUIRED) {
            return $this->validateAPIKey();
        }
        
        return true;
    }
    
    /**
     * Validate API key from headers
     */
    private function validateAPIKey() {
        $apiKey = $this->getAPIKeyFromHeaders();
        
        if (empty($apiKey)) {
            return false;
        }
        
        // Check if API key exists and is active
        try {
            $query = "SELECT id, name, is_active, rate_limit 
                     FROM api_keys 
                     WHERE api_key = :api_key AND is_active = 1";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':api_key', $apiKey);
            $stmt->execute();
            
            $keyData = $stmt->fetch();
            
            if ($keyData) {
                // Update last used timestamp
                $this->updateAPIKeyUsage($keyData['id']);
                return true;
            }
            
        } catch (Exception $e) {
            error_log("API Key validation error: " . $e->getMessage());
        }
        
        return false;
    }
    
    /**
     * Get API key from request headers
     */
    private function getAPIKeyFromHeaders() {
        $headers = getallheaders();
        
        // Check various header formats
        if (isset($headers['X-API-Key'])) {
            return $headers['X-API-Key'];
        }
        
        if (isset($headers['Authorization'])) {
            // Handle Bearer token format
            if (strpos($headers['Authorization'], 'Bearer ') === 0) {
                return substr($headers['Authorization'], 7);
            }
        }
        
        // Check query parameter as fallback
        return $_GET['api_key'] ?? '';
    }
    
    /**
     * Check rate limiting
     */
    private function checkRateLimit() {
        $ip = $this->getClientIP();
        $currentHour = date('Y-m-d H');
        
        try {
            // Get current request count for this IP in current hour
            $query = "SELECT request_count 
                     FROM api_rate_limits 
                     WHERE ip_address = :ip AND hour_window = :hour";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':ip', $ip);
            $stmt->bindParam(':hour', $currentHour);
            $stmt->execute();
            
            $result = $stmt->fetch();
            $currentCount = $result ? $result['request_count'] : 0;
            
            // Check if limit exceeded
            if ($currentCount >= API_RATE_LIMIT) {
                return false;
            }
            
            // Update or insert rate limit record
            if ($result) {
                $query = "UPDATE api_rate_limits 
                         SET request_count = request_count + 1, 
                             last_request = NOW() 
                         WHERE ip_address = :ip AND hour_window = :hour";
            } else {
                $query = "INSERT INTO api_rate_limits 
                         (ip_address, hour_window, request_count, last_request) 
                         VALUES (:ip, :hour, 1, NOW())";
            }
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':ip', $ip);
            $stmt->bindParam(':hour', $currentHour);
            $stmt->execute();
            
            return true;
            
        } catch (Exception $e) {
            error_log("Rate limit check error: " . $e->getMessage());
            // Allow request if rate limiting fails
            return true;
        }
    }
    
    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                return $ip;
            }
        }
        
        return 'unknown';
    }
    
    /**
     * Update API key usage statistics
     */
    private function updateAPIKeyUsage($keyId) {
        try {
            $query = "UPDATE api_keys 
                     SET last_used = NOW(), 
                         total_requests = total_requests + 1 
                     WHERE id = :key_id";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':key_id', $keyId);
            $stmt->execute();
            
        } catch (Exception $e) {
            error_log("API Key usage update error: " . $e->getMessage());
        }
    }
    
    /**
     * Generate new API key
     */
    public function generateAPIKey($name, $description = '') {
        $apiKey = bin2hex(random_bytes(32));
        
        try {
            $query = "INSERT INTO api_keys 
                     (name, description, api_key, created_at) 
                     VALUES (:name, :description, :api_key, NOW())";
            
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':name', $name);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':api_key', $apiKey);
            
            if ($stmt->execute()) {
                return $apiKey;
            }
            
        } catch (Exception $e) {
            error_log("API Key generation error: " . $e->getMessage());
        }
        
        return false;
    }
}
?>
