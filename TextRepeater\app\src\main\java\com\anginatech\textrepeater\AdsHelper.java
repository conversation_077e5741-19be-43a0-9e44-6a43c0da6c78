package com.anginatech.textrepeater;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Handler;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Toast;

import androidx.annotation.NonNull;

import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;
import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdListener;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.AdSize;
import com.google.android.gms.ads.AdView;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.MobileAds;
import com.google.android.gms.ads.interstitial.InterstitialAd;
import com.google.android.gms.ads.interstitial.InterstitialAdLoadCallback;

import org.json.JSONObject;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;

public class AdsHelper {
    private static final String TAG = "AdsHelper";
    private static InterstitialAd mInterstitialAd;

    public static String admob_banner = "";
    public static String admob_interstitial = "";
    public static String admob_app_open = "";
    public static boolean isAds = false;

    // Ad frequency control
    private static final String PREFS_NAME = "AdPrefs";
    private static final String BANERPREF = "AdPrefsBaner";
    private static final String KEY_AD_COUNT = "ad_count";
    private static final String KEY_NEXT_AD_TIME = "next_ad_time";
    private static final int MIN_ADS_BEFORE_BREAK = 3;
    private static final int MAX_ADS_BEFORE_BREAK = 5;
    private static final long AD_BREAK_DURATION = 60000;

    // Click control constants
    private static final String KEY_CLICK_COUNT = "ad_click_count";
    private static final String KEY_LAST_HIDE_TIME = "last_hide_time";
    private static final int MAX_CLICKS = 3;
    private static Handler handler;

    public static void initializeAds(Context context) {
        // Initialize handler on main thread if not already initialized
        if (handler == null) {
            // Ensure we're on the main thread for Handler creation
            if (android.os.Looper.myLooper() == android.os.Looper.getMainLooper()) {
                handler = new Handler(android.os.Looper.getMainLooper());
            } else {
                // Post to main thread to create handler
                new Handler(android.os.Looper.getMainLooper()).post(() -> {
                    handler = new Handler(android.os.Looper.getMainLooper());
                    loadAdsConfigurationAsync(context);
                });
                return;
            }
        }
        loadAdsConfigurationAsync(context);
    }

    /**
     * Load ads configuration asynchronously to prevent UI blocking
     */
    private static void loadAdsConfigurationAsync(Context context) {
        // First try to load from cache for immediate availability
        loadAdConfigFromPrefs(context);

        // Then load from network in background to update cache
        new Thread(() -> {
            try {
                loadAdsConfiguration(context);
            } catch (Exception e) {
                Log.e(TAG, "Error loading ads configuration asynchronously: " + e.getMessage());
            }
        }).start();
    }

    /**
     * Get or create handler safely on main thread
     */
    private static Handler getMainHandler() {
        if (handler == null) {
            handler = new Handler(android.os.Looper.getMainLooper());
        }
        return handler;
    }

    private static void loadAdsConfiguration(Context context) {
        Log.d(TAG, "Loading ads configuration from new API...");

        ApiClient apiClient = ApiClient.getInstance(context);
        apiClient.fetchAdMobConfig(new ApiClient.AdMobConfigCallback() {
            @Override
            public void onSuccess(JSONObject config) {
                try {
                    // Parse new API response format
                    admob_banner = config.optString("banner_id", "");
                    admob_interstitial = config.optString("interstitial_id", "");
                    admob_app_open = config.optString("app_open_id", "");
                    isAds = config.optInt("is_active", 1) == 1;

                    Log.d(TAG, "Ad IDs loaded from new API - Banner: " + admob_banner +
                            ", Interstitial: " + admob_interstitial +
                            ", App Open: " + admob_app_open +
                            ", Ads Enabled: " + isAds);

                    // Save to SharedPreferences for offline use
                    saveAdConfigToPrefs(context, config);

                    if (isAds && context instanceof Activity) {
                        ViewGroup adContainer = ((Activity) context).findViewById(R.id.ad_container);
                        if (adContainer != null) {
                            loadBannerAd((Activity) context, adContainer);
                        }
                        loadInterstitialAd(context);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error parsing new API response: " + e.getMessage());
                    loadAdsConfigurationFallback(context);
                }
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "Error loading ads configuration from new API: " + error);
                loadAdsConfigurationFallback(context);
            }
        });
    }

    /**
     * Fallback method to load ads configuration from old API
     */
    private static void loadAdsConfigurationFallback(Context context) {
        Log.d(TAG, "Loading ads configuration from fallback API...");

        RequestQueue queue = Volley.newRequestQueue(context);
        String fallbackUrl = Config.buildApiUrl(Config.ENDPOINT_ADS_CONFIG);
        JsonObjectRequest request = new JsonObjectRequest(Request.Method.GET, fallbackUrl, null,
                response -> {
                    try {
                        if (response.has("banner_id") && !response.getString("banner_id").isEmpty()) {
                            admob_banner = response.getString("banner_id");
                        }
                        if (response.has("interstitial_id") && !response.getString("interstitial_id").isEmpty()) {
                            admob_interstitial = response.getString("interstitial_id");
                        }
                        if (response.has("app_open_id") && !response.getString("app_open_id").isEmpty()) {
                            admob_app_open = response.getString("app_open_id");
                        }
                        isAds = response.getInt("is_active") == 1;

                        Log.d(TAG, "Ad IDs loaded from fallback API - Banner: " + admob_banner +
                                ", Interstitial: " + admob_interstitial +
                                ", App Open: " + admob_app_open +
                                ", Ads Enabled: " + isAds);

                        if (isAds && context instanceof Activity) {
                            ViewGroup adContainer = ((Activity) context).findViewById(R.id.ad_container);
                            if (adContainer != null) {
                                loadBannerAd((Activity) context, adContainer);
                            }
                            loadInterstitialAd(context);
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error parsing fallback API response: " + e.getMessage());
                        loadAdConfigFromPrefs(context); // Try to load from saved preferences
                    }
                },
                error -> {
                    Log.e(TAG, "Error loading ads configuration from fallback API: " + error.getMessage());
                    loadAdConfigFromPrefs(context); // Try to load from saved preferences
                });
        queue.add(request);
    }

    /**
     * Save ad configuration to SharedPreferences for offline use
     */
    private static void saveAdConfigToPrefs(Context context, JSONObject config) {
        SharedPreferences prefs = context.getSharedPreferences(Config.PREFS_ADMOB_CONFIG, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();

        editor.putString(Config.PREF_BANNER_AD_ID, config.optString("banner_id", ""));
        editor.putString(Config.PREF_INTERSTITIAL_AD_ID, config.optString("interstitial_id", ""));
        editor.putString(Config.PREF_APP_OPEN_AD_ID, config.optString("app_open_id", ""));
        editor.putBoolean(Config.PREF_ADMOB_ENABLED, config.optInt("is_active", 1) == 1);
        editor.putLong("last_updated", System.currentTimeMillis());
        editor.apply();

        Log.d(TAG, "Ad configuration saved to preferences");
    }

    /**
     * Load ad configuration from SharedPreferences as last resort
     */
    private static void loadAdConfigFromPrefs(Context context) {
        Log.d(TAG, "Loading ad configuration from saved preferences...");

        SharedPreferences prefs = context.getSharedPreferences(Config.PREFS_ADMOB_CONFIG, Context.MODE_PRIVATE);

        admob_banner = prefs.getString(Config.PREF_BANNER_AD_ID, "");
        admob_interstitial = prefs.getString(Config.PREF_INTERSTITIAL_AD_ID, "");
        admob_app_open = prefs.getString(Config.PREF_APP_OPEN_AD_ID, "");
        isAds = prefs.getBoolean(Config.PREF_ADMOB_ENABLED, Config.DEFAULT_ADMOB_ENABLED);

        Log.d(TAG, "Ad IDs loaded from preferences - Banner: " + admob_banner +
                ", Interstitial: " + admob_interstitial +
                ", App Open: " + admob_app_open +
                ", Ads Enabled: " + isAds);

        if (isAds && context instanceof Activity) {
            ViewGroup adContainer = ((Activity) context).findViewById(R.id.ad_container);
            if (adContainer != null) {
                loadBannerAd((Activity) context, adContainer);
            }
            loadInterstitialAd(context);
        }
    }

    public static boolean isInterstitialAdLoaded() {
        return mInterstitialAd != null;
    }

    public static void showInterstitialAd(Activity activity, FullScreenContentCallback userCallback) {
        if (mInterstitialAd != null) {
            Log.d(TAG, "Showing interstitial ad");

            // Create wrapper callback to handle both increment and user callback
            FullScreenContentCallback wrapperCallback = new FullScreenContentCallback() {
                @Override
                public void onAdDismissedFullScreenContent() {
                    Log.d(TAG, "Interstitial ad dismissed");
                    mInterstitialAd = null;
                    incrementAdCount(activity);

                    // Call user callback if provided
                    if (userCallback != null) {
                        userCallback.onAdDismissedFullScreenContent();
                    }

                    // Load next ad
                    loadInterstitialAd(activity);
                }

                @Override
                public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                    Log.e(TAG, "Interstitial ad failed to show: " + adError.getMessage());
                    mInterstitialAd = null;

                    // Call user callback if provided
                    if (userCallback != null) {
                        userCallback.onAdFailedToShowFullScreenContent(adError);
                    }

                    // Load next ad
                    loadInterstitialAd(activity);
                }

                @Override
                public void onAdShowedFullScreenContent() {
                    Log.d(TAG, "Interstitial ad showed successfully");
                    if (userCallback != null) {
                        userCallback.onAdShowedFullScreenContent();
                    }
                }
            };

            mInterstitialAd.setFullScreenContentCallback(wrapperCallback);
            mInterstitialAd.show(activity);
        } else {
            Log.d(TAG, "Interstitial ad not loaded");
            if (userCallback != null) {
                userCallback.onAdFailedToShowFullScreenContent(null);
            }
        }
    }

    public static void showInterstitialAd(Activity activity) {
        showInterstitialAd(activity, null);
    }

    public static void loadInterstitialAd(Context context) {
        if (!isAds || !canShowAds(context) || admob_interstitial.isEmpty()) {
            Log.d(TAG, "Interstitial ad not loaded: ads disabled or in break period");
            return;
        }

        // Load interstitial ad asynchronously to prevent UI blocking
        loadInterstitialAdAsync(context);
    }

    /**
     * Load interstitial ad asynchronously with optimized performance
     */
    private static void loadInterstitialAdAsync(Context context) {
        // Use background thread for ad request building to prevent UI blocking
        new Thread(() -> {
            try {
                AdRequest adRequest = new AdRequest.Builder().build();

                // Switch back to main thread for ad loading
                getMainHandler().post(() -> {
                    InterstitialAd.load(context, admob_interstitial, adRequest,
                            new InterstitialAdLoadCallback() {
                                @Override
                                public void onAdLoaded(@NonNull InterstitialAd interstitialAd) {
                                    mInterstitialAd = interstitialAd;
                                    Log.d(TAG, "Interstitial ad loaded successfully");

                                    // Set up ad callbacks for better user experience
                                    setupInterstitialAdCallbacks(interstitialAd);
                                }

                                @Override
                                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                                    Log.e(TAG, "Interstitial ad failed to load: " + loadAdError.getMessage());
                                    mInterstitialAd = null;

                                    // Retry loading after a delay if failed
                                    getMainHandler().postDelayed(() -> {
                                        if (context instanceof Activity && !((Activity) context).isFinishing()) {
                                            loadInterstitialAd(context);
                                        }
                                    }, 5000); // Retry after 5 seconds
                                }
                            });
                });

            } catch (Exception e) {
                Log.e(TAG, "Error loading interstitial ad asynchronously: " + e.getMessage());
            }
        }).start();
    }

    /**
     * Set up interstitial ad callbacks for better user experience
     */
    private static void setupInterstitialAdCallbacks(InterstitialAd interstitialAd) {
        interstitialAd.setFullScreenContentCallback(new FullScreenContentCallback() {
            @Override
            public void onAdClicked() {
                Log.d(TAG, "Interstitial ad clicked");
            }

            @Override
            public void onAdDismissedFullScreenContent() {
                Log.d(TAG, "Interstitial ad dismissed");
                mInterstitialAd = null;
            }

            @Override
            public void onAdFailedToShowFullScreenContent(AdError adError) {
                Log.e(TAG, "Interstitial ad failed to show: " + adError.getMessage());
                mInterstitialAd = null;
            }

            @Override
            public void onAdImpression() {
                Log.d(TAG, "Interstitial ad recorded an impression");
            }

            @Override
            public void onAdShowedFullScreenContent() {
                Log.d(TAG, "Interstitial ad showed fullscreen content");
            }
        });
    }

    private static void incrementAdCount(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        int adCount = prefs.getInt(KEY_AD_COUNT, 0) + 1;
        int limit = new Random().nextInt(MAX_ADS_BEFORE_BREAK - MIN_ADS_BEFORE_BREAK + 1) + MIN_ADS_BEFORE_BREAK;

        if (adCount >= limit) {
            // Reset counter and set next ad time
            prefs.edit()
                    .putInt(KEY_AD_COUNT, 0)
                    .putLong(KEY_NEXT_AD_TIME, System.currentTimeMillis() + AD_BREAK_DURATION)
                    .apply();
            Log.d(TAG, "Ad limit reached. Taking a break for " + (AD_BREAK_DURATION / 1000) + " seconds");
        } else {
            prefs.edit().putInt(KEY_AD_COUNT, adCount).apply();
            Log.d(TAG, "Ad count increased to: " + adCount + "/" + limit);
        }
    }

    private static boolean canShowAds(Context context) {
        if (!isAds) return false;

        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        long nextAdTime = prefs.getLong(KEY_NEXT_AD_TIME, 0);
        return System.currentTimeMillis() >= nextAdTime;
    }
    private static boolean canShowAdsBaner(Context context) {
        if (!isAds) return false;

        SharedPreferences prefs = context.getSharedPreferences(BANERPREF, Context.MODE_PRIVATE);
        long nextAdTime = prefs.getLong(KEY_NEXT_AD_TIME, 0);
        return System.currentTimeMillis() >= nextAdTime;
    }

    public static void loadBannerAd(Activity activity, ViewGroup adContainer) {
        if (!isAds || adContainer == null || !canShowAdsBaner(activity) || admob_banner.isEmpty()) {
            Log.d(TAG, "Banner ad not loaded: ads disabled or in break period");
            if (adContainer != null) {
                adContainer.removeAllViews();
                adContainer.setVisibility(View.GONE);
            }
            return;
        }

        // Load banner ad asynchronously to prevent UI blocking
        loadBannerAdAsync(activity, adContainer);
    }

    /**
     * Load banner ad asynchronously with optimized performance
     */
    private static void loadBannerAdAsync(Activity activity, ViewGroup adContainer) {
        SharedPreferences prefs = activity.getSharedPreferences(BANERPREF, Context.MODE_PRIVATE);
        final AtomicInteger clickCount = new AtomicInteger(prefs.getInt(KEY_CLICK_COUNT, 0));
        long lastHideTime = prefs.getLong(KEY_LAST_HIDE_TIME, 0);
        long currentTime = System.currentTimeMillis();

        Log.d(TAG, "Loading banner ad asynchronously - Current click count: " + clickCount.get());

        try {
            AdView adView = new AdView(activity);
            adView.setAdSize(AdSize.BANNER);
            adView.setAdUnitId(admob_banner);

            // Set up ad listener for proper click tracking
            adView.setAdListener(new AdListener() {
                @Override
                public void onAdClicked() {
                    super.onAdClicked();
                    Log.d(TAG, "Banner ad clicked through AdListener!");
                    handleAdClick(activity, adContainer, clickCount, adView);
                }
            });

            adContainer.removeAllViews();
            adContainer.addView(adView);

            // Check visibility state
            if (clickCount.get() >= MAX_CLICKS && currentTime - lastHideTime < AD_BREAK_DURATION) {
                int remainingSeconds = (int)((AD_BREAK_DURATION - (currentTime - lastHideTime)) / 1000);
                Log.d(TAG, "Ad should be hidden. Time remaining: " + remainingSeconds + " seconds");
                adContainer.setVisibility(View.GONE);
            } else {
                Log.d(TAG, "Ad should be visible");
                adContainer.setVisibility(View.VISIBLE);
                if (currentTime - lastHideTime >= AD_BREAK_DURATION) {
                    Log.d(TAG, "Resetting click count after break duration");
                    clickCount.set(0);
                    prefs.edit().putInt(KEY_CLICK_COUNT, 0).apply();
                }
            }

            AdRequest adRequest = new AdRequest.Builder().build();
            adView.loadAd(adRequest);
            Log.d(TAG, "Loading banner ad with ID: " + admob_banner);

        } catch (Exception e) {
            Log.e(TAG, "Error loading banner ad: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private static void handleAdClick(Activity activity, ViewGroup adContainer, AtomicInteger clickCount, AdView adView) {
        SharedPreferences prefs = activity.getSharedPreferences(BANERPREF, Context.MODE_PRIVATE);
        int newCount = clickCount.incrementAndGet();
        Log.d(TAG, "Ad clicked! New click count: " + newCount);

        prefs.edit().putInt(KEY_CLICK_COUNT, newCount).apply();

        if (newCount >= MAX_CLICKS) {
            long hideTime = System.currentTimeMillis();
            prefs.edit().putLong(KEY_LAST_HIDE_TIME, hideTime).apply();
            Log.d(TAG, "Max clicks (" + MAX_CLICKS + ") reached! Hiding ad");

            adContainer.setVisibility(View.GONE);

            // Schedule visibility restore using safe handler
            getMainHandler().postDelayed(() -> {
                if (activity != null && !activity.isFinishing()) {
                    Log.d(TAG, "Break time over, showing ad again");
                    adContainer.setVisibility(View.VISIBLE);
                    clickCount.set(0);
                    prefs.edit().putInt(KEY_CLICK_COUNT, 0).apply();
                    Log.d(TAG, "Click count reset to 0");

                    // Reload the banner ad
                    loadBannerAd(activity, adContainer);
                }
            }, AD_BREAK_DURATION);

            int hideSeconds = (int)(AD_BREAK_DURATION / 1000);
            Log.d(TAG, "Ad will reappear after " + hideSeconds + " seconds");
        }

        // Notify server
        notifyServerAboutClick(activity);
    }

    private static void notifyServerAboutClick(Context context) {
        Log.d(TAG, "Notifying server about ad click");
        RequestQueue queue = Volley.newRequestQueue(context);

        try {
            JSONObject jsonBody = new JSONObject();
            jsonBody.put("event", "ad_click");
            jsonBody.put("timestamp", System.currentTimeMillis());
            jsonBody.put("ad_type", "banner");

            // Try new API first
            String newApiUrl = Config.buildApiUrl(Config.ENDPOINT_ADS_TRACK);
            JsonObjectRequest newRequest = new JsonObjectRequest(
                    Request.Method.POST,
                    newApiUrl,
                    jsonBody,
                    response -> Log.d(TAG, "Click tracked successfully via new API: " + response.toString()),
                    error -> {
                        Log.w(TAG, "New API tracking failed, trying fallback: " + error.toString());
                        // Fallback to old API
                        trackClickFallback(context, jsonBody);
                    }
            );

            queue.add(newRequest);
        } catch (Exception e) {
            Log.e(TAG, "Error creating click tracking request: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Fallback method to track clicks using old API
     */
    private static void trackClickFallback(Context context, JSONObject jsonBody) {
        RequestQueue queue = Volley.newRequestQueue(context);
        String fallbackUrl = Config.buildApiUrl(Config.ENDPOINT_ADS_TRACK);

        JsonObjectRequest request = new JsonObjectRequest(
                Request.Method.POST,
                fallbackUrl,
                jsonBody,
                response -> Log.d(TAG, "Click tracked successfully via fallback API: " + response.toString()),
                error -> Log.e(TAG, "Error tracking click via fallback API: " + error.toString())
        );

        queue.add(request);
    }
}
