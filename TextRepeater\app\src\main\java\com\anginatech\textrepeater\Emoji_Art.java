package com.anginatech.textrepeater;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.Toolbar;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.MaterialToolbar;

import java.util.ArrayList;
import java.util.List;

public class Emoji_Art extends AppCompatActivity {

    MaterialToolbar stylish_MaterialToolbar;
    private EmojiAdapter emojiAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_emoji_art);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });


        SharedPreferences sharedPreferences = getSharedPreferences("nightModePrefs", MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }

        stylish_MaterialToolbar = findViewById(R.id.stylish_MaterialToolbar);

        RecyclerView emoji_RecyclerView = findViewById(R.id.emoji_RecyclerView);



        EmojiListManager manager = new EmojiListManager();
        List<String> emojiList = manager.getEmojiList();

        emojiAdapter = new EmojiAdapter(this, emojiList);

        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        emoji_RecyclerView.setLayoutManager(layoutManager);
        emoji_RecyclerView.setHasFixedSize(true);
        emoji_RecyclerView.setAdapter(emojiAdapter);


        stylish_MaterialToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(Emoji_Art.this, MainActivity.class));
                finish();
            }
        });

        stylish_MaterialToolbar.setTitleTextAppearance(Emoji_Art.this, R.style.RobotoBoldTextAppearance);






    }




    @Override
    public void onBackPressed() {
        startActivity(new Intent(Emoji_Art.this, MainActivity.class));
        finish();
        super.onBackPressed();
    }
}