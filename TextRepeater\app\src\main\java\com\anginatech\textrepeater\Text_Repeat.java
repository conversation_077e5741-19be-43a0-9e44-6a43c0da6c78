package com.anginatech.textrepeater;


import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;

import android.os.Bundle;
import android.os.Handler;

import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;

import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;

import android.widget.Toast;
import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.MaterialToolbar;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


public class Text_Repeat extends AppCompatActivity {


    private EditText editEnterText, editLimit;
    private CheckBox checkBoxLine;
    private Button buttonRepeat;
    private AppCompatButton buttonChoose;
    private RecyclerView rcyclerView;
    private TextAdapter textAdapter;
    private List<String> repeatedTextList = new ArrayList<>();
    private Handler handler;
    private ExecutorService executorService;
    MaterialToolbar textRepeat_materialToolbar;
    ConstraintLayout copy_share_Layout,layoutCopy,layoutShare;
    StringBuilder textBuilder = new StringBuilder();



    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_text_repeat);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        SharedPreferences sharedPreferences = getSharedPreferences("nightModePrefs", MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }

        ///Initialize the element ========================================================================

        textRepeat_materialToolbar = findViewById(R.id.textRepeat_materialToolbar);
        copy_share_Layout = findViewById(R.id.copy_share_Layout);
        layoutCopy = findViewById(R.id.layoutCopy);
        layoutShare = findViewById(R.id.layoutShare);

        editEnterText = findViewById(R.id.editEnterText);
        editLimit = findViewById(R.id.editLimit);
        checkBoxLine = findViewById(R.id.checkBoxLine);
        buttonChoose = findViewById(R.id.buttonChoose);
        buttonRepeat = findViewById(R.id.buttonRepeat);

        rcyclerView = findViewById(R.id.rcyclerView);

        handler = new Handler(Looper.getMainLooper());
        executorService = Executors.newSingleThreadExecutor();

        rcyclerView.setLayoutManager(new LinearLayoutManager(this));
        textAdapter = new TextAdapter(new ArrayList<>());
        rcyclerView.setAdapter(textAdapter);

        buttonRepeat.setOnClickListener(view -> generateRepeatedText());
        layoutCopy.setOnClickListener(view -> copyText());
        layoutShare.setOnClickListener(view -> shareText());


        ///Initialize element ========================================================================



        textRepeat_materialToolbar.setOnMenuItemClickListener(new Toolbar.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {


                if (item.getItemId()==R.id.clear_Data) {
                    editEnterText.setText("");
                    editLimit.setText("");
                    copy_share_Layout.setVisibility(View.GONE);
                    repeatedTextList.clear();
                    textAdapter.updateData(new ArrayList<>());
                    Toast.makeText(Text_Repeat.this, "Cleared!", Toast.LENGTH_SHORT).show();
                }
                return true;
            }
        });



        textRepeat_materialToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(Text_Repeat.this, MainActivity.class));
                finish();

            }
        });

        textRepeat_materialToolbar.setTitleTextAppearance(Text_Repeat.this, R.style.RobotoBoldTextAppearance);



        buttonChoose.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showDialog();

            }
        });





    }


    private void generateRepeatedText() {

        repeatedTextList.clear();
        textAdapter.updateData(new ArrayList<>());
        String inputText = editEnterText.getText().toString().trim();
        String repeatCountStr = editLimit.getText().toString().trim();

        if (inputText.isEmpty() || repeatCountStr.isEmpty()) {
            Toast.makeText(this, "Please enter text and repeat count", Toast.LENGTH_SHORT).show();
            return;
        }

        if (inputText.codePointCount(0, inputText.length()) > 150) {
            Toast.makeText(this, "Max 150 characters allowed!", Toast.LENGTH_SHORT).show();
            return;
        }

        int repeatCount;
        try {
            repeatCount = Integer.parseInt(repeatCountStr);
        } catch (NumberFormatException e) {
            Toast.makeText(this, "Invalid number", Toast.LENGTH_SHORT).show();
            return;
        }

        // Enhanced limit checking with memory consideration
        if (repeatCount > 10000) {
            Toast.makeText(this, "Max limit is 10,000 repetitions!", Toast.LENGTH_SHORT).show();
            return;
        }

        // Warn for large operations
        if (repeatCount > 5000) {
            Toast.makeText(this, "Large operation detected. This may take a moment...", Toast.LENGTH_SHORT).show();
        }

        buttonRepeat.setEnabled(false);
        repeatedTextList.clear();
        textAdapter.updateData(new ArrayList<>());


        executorService.execute(() -> {
            generateText(inputText, repeatCount, checkBoxLine.isChecked());
            runOnUiThread(() -> {
                textAdapter.updateData(repeatedTextList);
                buttonRepeat.setEnabled(true);
                copy_share_Layout.setVisibility(View.VISIBLE);
            });
        });


    }

    /**
     * Optimized text generation with better performance for large counts
     * Uses StringBuilder for efficient string concatenation and batch processing
     */
    private void generateText(String text, int count, boolean addNewLine) {
        String separator = addNewLine ? "\n" : "";

        // Clear previous data
        textBuilder.setLength(0);

        // Use batch processing for better performance
        int batchSize = 1000;
        int processedCount = 0;

        while (processedCount < count) {
            int currentBatchSize = Math.min(batchSize, count - processedCount);

            // Process current batch
            for (int i = 0; i < currentBatchSize; i++) {
                String textToAdd = text;
                if (addNewLine && (processedCount + i) < count - 1) {
                    textToAdd += separator;
                }

                repeatedTextList.add(textToAdd);
                textBuilder.append(text);

                if ((processedCount + i) < count - 1) {
                    textBuilder.append("\n");
                }
            }

            processedCount += currentBatchSize;

            // Update UI periodically for large counts to show progress
            if (processedCount % batchSize == 0 && processedCount < count) {
                final int currentProgress = processedCount;
                runOnUiThread(() -> {
                    // Show progress for large operations
                    if (count > 2000) {
                        int progress = (currentProgress * 100) / count;
                        // You can add a progress indicator here if needed
                        Log.d("TextGeneration", "Progress: " + progress + "%");
                    }
                });
            }
        }

        Log.d("TextGeneration", "Generated " + count + " repetitions efficiently");
    }



        private void showDialog () {
            SMSBottomSheetDialog dialog = SMSBottomSheetDialog.newInstance();
            dialog.show(getSupportFragmentManager(), "SMSBottomSheetDialog");
        }


        @Override
        public void onBackPressed () {
            startActivity(new Intent(Text_Repeat.this, MainActivity.class));
            finish();
            super.onBackPressed();
        }

    private void copyText() {
        if (repeatedTextList.isEmpty()) {
            Toast.makeText(this, "Nothing to copy", Toast.LENGTH_SHORT).show();
            return;
        }

        String joiner = checkBoxLine.isChecked() ? "\n" : "";
        String finalText = TextUtils.join(joiner, repeatedTextList);

        if (finalText.length() > 100000) {
            Toast.makeText(this, "Text too long to copy (Max 100k characters)", Toast.LENGTH_LONG).show();
            return;
        }

        ClipboardManager clipboard = (ClipboardManager) getSystemService(Context.CLIPBOARD_SERVICE);
        ClipData clip = ClipData.newPlainText("repeated_text", finalText);
        clipboard.setPrimaryClip(clip);
        Toast.makeText(this, "Copied to clipboard", Toast.LENGTH_SHORT).show();
    }

    private void shareText() {
        if (repeatedTextList.isEmpty()) {
            Toast.makeText(this, "Nothing to share", Toast.LENGTH_SHORT).show();
            return;
        }

        String joiner = checkBoxLine.isChecked() ? "\n" : "";
        String finalText = TextUtils.join(joiner, repeatedTextList);

        if (finalText.length() > 50000) {
            Toast.makeText(this, "Text too long to share (Max 50k characters)", Toast.LENGTH_LONG).show();
            return;
        }

        Intent sendIntent = new Intent();
        sendIntent.setAction(Intent.ACTION_SEND);
        sendIntent.putExtra(Intent.EXTRA_TEXT, finalText);
        sendIntent.setType("text/plain");
        startActivity(Intent.createChooser(sendIntent, "Share via"));
    }

    public void updateSelectedSMS(String sms) {

        editEnterText.setText(""+sms);
    }

    }

