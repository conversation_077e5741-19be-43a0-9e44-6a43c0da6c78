package com.anginatech.textrepeater;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.Toolbar;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.appbar.MaterialToolbar;

import java.util.ArrayList;
import java.util.List;

public class Decoration_Text_Activity extends AppCompatActivity {


    RecyclerView decoration_RecyclerView;
    EditText decoration_editEnterText;
    MaterialToolbar decoration_MaterialToolbar;

    private DecorationAdapter adapter;
    private List<String> decorationList;


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_decoration_text);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });
        SharedPreferences sharedPreferences = getSharedPreferences("nightModePrefs", MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }

        //initialized element ======================================================================

        decoration_RecyclerView = findViewById(R.id.decoration_RecyclerView);
        decoration_editEnterText = findViewById(R.id.decoration_editEnterText);
        decoration_MaterialToolbar = findViewById(R.id.decoration_MaterialToolbar);




        //initialized element ======================================================================

        decoration_MaterialToolbar.setTitleTextAppearance(Decoration_Text_Activity.this,R.style.RobotoBoldTextAppearance);

        decoration_MaterialToolbar.setOnMenuItemClickListener(new Toolbar.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {
                if (item.getItemId()==R.id.clear_Data){
                    decoration_editEnterText.setText("");
                    decorationList.clear();
                }

                return true;
            }
        });

        decoration_MaterialToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(Decoration_Text_Activity.this, MainActivity.class));
                finish();
            }
        });


        decorationList = new ArrayList<>();
        adapter = new DecorationAdapter(Decoration_Text_Activity.this,decorationList);

        decoration_RecyclerView.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false));
        decoration_RecyclerView.setHasFixedSize(true);
        decoration_RecyclerView.setAdapter(adapter);

        decoration_editEnterText.addTextChangedListener(new TextWatcher() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                updateDecorations(s.toString());
            }

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void afterTextChanged(Editable s) {}
        });



    }



    @Override
    public void onBackPressed() {

        startActivity(new Intent(Decoration_Text_Activity.this, MainActivity.class));
        finish();
        super.onBackPressed();
    }

    private void updateDecorations(String text) {
        decorationList.clear();
        decorationList.add("★.•:¨¨:•.★ "+text+ "★.•:¨¨:•.★");
        decorationList.add("✿❀✿ "+text+" ✿❀✿");
        decorationList.add("✦.•°°•.❀ {"+text+"} ❀.•°°•.✦");
        decorationList.add("╰☆╮ "+text+ " ╰☆╮");
        decorationList.add("☾⋆⁺₊ {"+text+"} ₊⁺⋆☽");
        decorationList.add("♡´･ᴗ･♡ {"+text+"} ♡´･ᴗ･♡");
        decorationList.add("︵‿︵ {"+text+"} ︵‿︵");
        decorationList.add("︵‿︵‿☆ {"+text+"} ☆‿︵‿︵");
        decorationList.add("{❤‿❤}"+text+"{❤‿❤}");
        decorationList.add("🌹🌺💐{"+text+"}💐🌺🌹");
        decorationList.add("💕❤️💞{"+text+"}💞❤️💕");
        decorationList.add ("✦.•°°•.❀{"+text+"} ❀.•°°•.✦");
        decorationList.add(" ♡✿⌜✦°{"+text+"}°✦⌝✿♡");
        decorationList.add("🌺🌼🌿 {"+text+"} 🌿🌼🌺");
        decorationList.add("✨💎🌸 {"+text+"} 🌸💎✨");
        decorationList.add("≧◉◡◉≦ {"+text+"} ≧◉◡◉≦");
        decorationList.add("╚»★«╝ {"+text+"} ╚»★«╝");
        decorationList.add("♡‿♡ {"+text+"} ♡‿♡");
        decorationList.add("◥▶◀ {"+text+"} ▶◀◤");
        decorationList.add("⚜️❁╭━━⊱✦ {"+text+"} ✦⊰━━╮❁⚜️");
        decorationList.add("🌿✨⧫°∘⭑ {"+text+"} ⭑∘°⧫✨🌿");
        decorationList.add("🍁🎀━━━⊰✿ {"+text+"} ✿⊱━━━🎀🍁");
        decorationList.add("🔹🌟═══╰✧ {"+text+"} ✧╯═══🌟🔹");
        decorationList.add("🦋✶⊰✦∘°彡 {"+text+"} 彡°∘✦⊱✶🦋");
        decorationList.add("🌀❖━✵⊹╰✿ {"+text+"} ✿╯⊹✵━❖🌀");
        decorationList.add("⚡🍀⊱━━━✶彡 {"+text+"} 彡✶━━━⊰🍀⚡");
        decorationList.add("🌟🎶╰━━⊱✧∘ {"+text+"} ∘✧⊰━━╯🎶🌟");
        decorationList.add("︽✵︽ {"+text+"} ︽✵︽");
        decorationList.add("❁⋆⋅☆⋅⋆❁ {"+text+"} ❁⋆⋅☆⋅⋆❁");
        decorationList.add("★⌒ {"+text+"} ⌒★");
        decorationList.add("🌞🌟🌈 {"+text+"} 🌈🌟🌞");
        decorationList.add("✵⊱彡•∘✧⊹✿ {"+text+"}✿⊹✧∘•彡⊰✵");
        decorationList.add("✧⊹•∘✿彡⊰╰ {"+text+"} ╯⊱彡✿∘•⊹✧");
        decorationList.add("❦✧╰°•∘✿彡 {"+text+"} 彡✿∘•°╯✧❦");
        decorationList.add("🍀🌼🦋 {"+text+"} 🦋🌼🍀");
        decorationList.add("🌷💮🌸 {"+text+"} 🌸💮🌷");
        decorationList.add("✦☆•⊹✿⊱╰ {"+text+"} ╯⊰✿⊹•☆✦");
        decorationList.add("❖∘✵•⊹彡✧ {"+text+"} ✧彡⊹•✵∘❖");
        decorationList.add("💫🌙☾ {"+text+"} ☾🌙💫");
        decorationList.add("💕💞🌟 {"+text+"} 🌟💞💕");
        decorationList.add("✧•⊹∘°❀╰✿ {"+text+"} ✿╯❀°∘⊹•✧");
        decorationList.add("✯彡⊰•°•∘✿ {"+text+"} ✿∘•°•⊰彡✯");
        decorationList.add("🎶🎵🎤 {"+text+"} 🎤🎵🎶");
        decorationList.add("💐🌸🌺 {"+text+"} 🌺🌸💐");
        decorationList.add("⊱⋆˚✩ {"+text+"} ✩˚⋆⊰");
        decorationList.add("✿︵✧ {"+text+"} ✧︵✿");
        decorationList.add("❦✵✿⊹∘°⊱╰ {"+text+"} ╯⊰°∘⊹✿✵❦");
        decorationList.add("🌈☁️⭐ {"+text+"} ⭐☁️🌈");
        decorationList.add("🌟🌙⭐ {"+text+"} ⭐🌙🌟");
        decorationList.add("🍂🍁🌾 {"+text+"} 🌾🍁🍂");
        decorationList.add("🌀🌟🌈━━━ {"+text+"} ━━━🌈🌟🌀");
        decorationList.add("🔶💫⎯⎯✦ {"+text+"} ✦⎯⎯💫🔶");
        decorationList.add("🎀╭━━⊰✿ {"+text+"} ✿⊱━━╮🎀");
        decorationList.add("⭕✨°⧫° {"+text+"} °⧫°✨⭕");
        decorationList.add("⚡⚪❖═══ {"+text+"} ═══❖⚪⚡");
        decorationList.add("⊚🌸⊱✦彡 {"+text+"} 彡✦⊰🌸⊚");
        decorationList.add("🏵️🌟╰⊱∘ {"+text+"} ∘⊱╯🌟🏵️");
        decorationList.add("◇❀⭑╰━✦ {"+text+"} ✦━╯⭑❀◇");
        decorationList.add("⭑✶🌼╰⊰❀ {"+text+"} ❀⊱╯🌼✶⭑");
        decorationList.add("🌀⧫═══✦彡 {"+text+"} 彡✦═══⧫🌀");
        decorationList.add("❃⭐╭∘✶⊰ {"+text+"} ⊱✶∘╮⭐❃");
        decorationList.add("◇✵━✧∘彡 {"+text+"} 彡∘✧━✵◇");
        decorationList.add("🍂🎀━━━∘✿⊹ {"+text+"} ⊹✿∘━━━🎀🍂");
        decorationList.add("🔶✨⧫━━✦彡⊰ {"+text+"} ⊱彡✦━━⧫✨🔶");
        decorationList.add("🌺🏵️╭━━━⊹❀ {"+text+"} ❀⊹━━━╮🏵️🌺");
        decorationList.add("🌸⚪═══∘✵彡╰ {"+text+"} ╯彡✵∘═══⚪🌸");
        decorationList.add("🌈🌀✵━━━⊰✧∘ {"+text+"} ∘✧⊱━━━✵🌀🌈");
        adapter.notifyDataSetChanged();
    }




}