@echo off
echo ========================================
echo   Background Notification Service
echo ========================================
echo.

cd /d "%~dp0"

echo Starting background notification service...
echo.
echo This service will run continuously in the background
echo to process scheduled notifications automatically.
echo.
echo To stop the service, run: stop-notification-service.bat
echo To check status, run: check-notification-service.bat
echo.

REM Start the service in background
start /B C:\xampp\php\php.exe "cron\background-notification-service.php" start

echo Service started successfully!
echo.
echo You can now close this window. The service will continue
echo running in the background.
echo.
echo Check the log file at: cron\background-service.log
echo.
pause
