<?php
/**
 * Database Configuration for Text Repeater Admin Panel
 */

class Database {
    private $host = 'localhost';
    private $db_name = 'text_repeater_admin';
    private $username = 'root'; // Change this to your database username
    private $password = '';     // Change this to your database password
    private $conn;

    public function getConnection() {
        $this->conn = null;

        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8; SET time_zone = '+06:00';"
                )
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }

        return $this->conn;
    }
}

/**
 * Application Configuration
 */
class Config {
    // Application settings
    const APP_NAME = 'Text Repeater Admin Panel';
    const APP_VERSION = '1.0.0';
    const APP_URL = 'http://localhost/TextRepeaterAdminPanel'; // Change this to your domain

    // Security settings
    const SESSION_TIMEOUT = 3600; // 1 hour in seconds
    const PASSWORD_MIN_LENGTH = 6;
    const MAX_LOGIN_ATTEMPTS = 5;
    const LOGIN_LOCKOUT_TIME = 900; // 15 minutes in seconds

    // Firebase Cloud Messaging settings
    const FCM_SERVER_KEY = 'YOUR_FCM_SERVER_KEY_HERE'; // Replace with your FCM server key
    const FCM_SENDER_ID = 'YOUR_FCM_SENDER_ID_HERE';   // Replace with your FCM sender ID

    // AdMob settings
    const ADMOB_PUBLISHER_ID = 'pub-8559393431170327';

    // File upload settings
    const MAX_FILE_SIZE = 5242880; // 5MB in bytes
    const ALLOWED_IMAGE_TYPES = ['jpg', 'jpeg', 'png', 'gif'];
    const UPLOAD_PATH = 'uploads/';

    // Pagination settings
    const ITEMS_PER_PAGE = 20;

    // API settings
    const API_VERSION = 'v1';
    const API_RATE_LIMIT = 100; // requests per minute

    // Email settings (for notifications)
    const SMTP_HOST = 'smtp.gmail.com';
    const SMTP_PORT = 587;
    const SMTP_USERNAME = '<EMAIL>';
    const SMTP_PASSWORD = 'your-app-password';
    const FROM_EMAIL = '<EMAIL>';
    const FROM_NAME = 'Text Repeater Admin';

    // Timezone - Change this to your local timezone
    const DEFAULT_TIMEZONE = 'Asia/Dhaka'; // Bangladesh timezone (UTC+6)
    // Other common timezones:
    // 'America/New_York' (EST/EDT)
    // 'Europe/London' (GMT/BST)
    // 'Asia/Kolkata' (IST)
    // 'Asia/Tokyo' (JST)
    // 'Australia/Sydney' (AEST/AEDT)

    // Debug mode
    const DEBUG_MODE = true; // Set to false in production

    // Logging
    const LOG_LEVEL = 'INFO'; // DEBUG, INFO, WARNING, ERROR
    const LOG_FILE = 'logs/app.log';

    public static function init() {
        // Set timezone
        date_default_timezone_set(self::DEFAULT_TIMEZONE);

        // Set error reporting based on debug mode
        if (self::DEBUG_MODE) {
            error_reporting(E_ALL);
            ini_set('display_errors', 1);
        } else {
            error_reporting(0);
            ini_set('display_errors', 0);
        }

        // Start session if not already started
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        // Create necessary directories
        self::createDirectories();
    }

    private static function createDirectories() {
        $directories = [
            self::UPLOAD_PATH,
            'logs/',
            'cache/',
            'temp/'
        ];

        foreach ($directories as $dir) {
            if (!is_dir($dir)) {
                mkdir($dir, 0755, true);
            }
        }
    }

    public static function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'];
        $script = $_SERVER['SCRIPT_NAME'];
        $path = dirname($script);

        return $protocol . '://' . $host . $path;
    }
}

// Initialize configuration
Config::init();
?>
