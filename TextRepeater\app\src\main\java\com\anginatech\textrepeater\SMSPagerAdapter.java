package com.anginatech.textrepeater;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import java.util.ArrayList;
import java.util.List;

public class SMSPagerAdapter extends FragmentStateAdapter {

    private final List<List<String>> allMessages;

    public SMSPagerAdapter(@NonNull Fragment fragment, List<List<String>> allMessages) {
        super(fragment);
        this.allMessages = allMessages;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        SMSFragment fragment = new SMSFragment();
        Bundle args = new Bundle();
        args.putStringArrayList("smsList", new ArrayList<>(allMessages.get(position)));
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public int getItemCount() {
        return allMessages.size();
    }
}