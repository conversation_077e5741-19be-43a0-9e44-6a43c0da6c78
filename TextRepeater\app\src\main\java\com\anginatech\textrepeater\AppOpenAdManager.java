package com.anginatech.textrepeater;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.DefaultLifecycleObserver;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ProcessLifecycleOwner;

import com.google.android.gms.ads.AdError;
import com.google.android.gms.ads.AdRequest;
import com.google.android.gms.ads.FullScreenContentCallback;
import com.google.android.gms.ads.LoadAdError;
import com.google.android.gms.ads.appopen.AppOpenAd;

import java.util.Date;

/**
 * Manages App Open Ads for the Text Repeater application
 */
public class AppOpenAdManager implements Application.ActivityLifecycleCallbacks, DefaultLifecycleObserver {

    private static final String TAG = "AppOpenAdManager";
    private static final String PREFS_NAME = "AppOpenAdPrefs";
    private static final String KEY_LAST_SHOWN = "last_shown_time";
    private static final String KEY_SHOW_COUNT = "show_count";
    private static final long MIN_INTERVAL_BETWEEN_ADS = 4 * 60 * 1000; // 4 minutes
    private static final int MAX_ADS_PER_SESSION = 3;

    private AppOpenAd appOpenAd = null;
    private boolean isLoadingAd = false;
    private boolean isShowingAd = false;
    private long loadTime = 0;
    private Activity currentActivity;
    private MyApplication myApplication;

    // Ad unit ID - will be loaded from server
    private String appOpenAdUnitId = "";

    public AppOpenAdManager(MyApplication myApplication) {
        this.myApplication = myApplication;
        this.myApplication.registerActivityLifecycleCallbacks(this);
        ProcessLifecycleOwner.get().getLifecycle().addObserver(this);
    }

    /**
     * Set the App Open Ad unit ID from server configuration
     */
    public void setAppOpenAdUnitId(String adUnitId) {
        this.appOpenAdUnitId = adUnitId;
        Log.d(TAG, "App Open Ad Unit ID set: " + adUnitId);
    }

    /**
     * Reset loading state - useful for splash screen when retrying
     */
    public void resetLoadingState() {
        Log.d(TAG, "Resetting App Open Ad loading state");
        isLoadingAd = false;
        isShowingAd = false;
    }

    /**
     * Load an App Open Ad
     */
    public void loadAd(Context context) {
        if (isLoadingAd || isAdAvailable() || !AdsHelper.isAds || appOpenAdUnitId.isEmpty()) {
            Log.d(TAG, "Ad not loaded - isLoading: " + isLoadingAd +
                      ", isAvailable: " + isAdAvailable() +
                      ", adsEnabled: " + AdsHelper.isAds +
                      ", adUnitId: " + appOpenAdUnitId);
            return;
        }

        isLoadingAd = true;
        AdRequest request = new AdRequest.Builder().build();

        AppOpenAd.load(
            context,
            appOpenAdUnitId,
            request,
            new AppOpenAd.AppOpenAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull AppOpenAd ad) {
                    Log.d(TAG, "App Open Ad loaded successfully");
                    appOpenAd = ad;
                    isLoadingAd = false;
                    loadTime = new Date().getTime();

                    // Track ad load event
                    trackAdEvent(context, "app_open", "load", appOpenAdUnitId);
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    Log.e(TAG, "App Open Ad failed to load: " + loadAdError.getMessage());
                    isLoadingAd = false;

                    // Track ad load failure
                    trackAdEvent(context, "app_open", "fail", appOpenAdUnitId);
                }
            }
        );
    }

    /**
     * Check if an ad is available and not expired
     */
    private boolean isAdAvailable() {
        return appOpenAd != null && wasLoadTimeLessThanNHoursAgo(4);
    }

    /**
     * Check if the ad was loaded less than n hours ago
     */
    private boolean wasLoadTimeLessThanNHoursAgo(long numHours) {
        long dateDifference = new Date().getTime() - loadTime;
        long numMilliSecondsPerHour = 3600000;
        return (dateDifference < (numMilliSecondsPerHour * numHours));
    }

    /**
     * Show the App Open Ad if available and conditions are met
     */
    public void showAdIfAvailable(@NonNull Activity activity) {
        showAdIfAvailable(activity, null);
    }

    /**
     * Show the App Open Ad with callback - Enhanced for splash screen support
     */
    public void showAdIfAvailable(@NonNull Activity activity, @Nullable OnShowAdCompleteListener onShowAdCompleteListener) {
        showAdIfAvailable(activity, onShowAdCompleteListener, null);
    }

    /**
     * Show the App Open Ad with enhanced callbacks for splash screen support
     */
    public void showAdIfAvailable(@NonNull Activity activity,
                                @Nullable OnShowAdCompleteListener onShowAdCompleteListener,
                                @Nullable OnAdLoadingListener onAdLoadingListener) {
        if (isShowingAd) {
            Log.d(TAG, "App Open Ad is already showing");
            if (onShowAdCompleteListener != null) {
                onShowAdCompleteListener.onShowAdComplete();
            }
            return;
        }

        if (!isAdAvailable()) {
            Log.d(TAG, "App Open Ad is not ready yet, attempting to load");
            if (onAdLoadingListener != null) {
                onAdLoadingListener.onAdLoadingStarted();
            }
            // Load ad with enhanced callback support
            loadAdWithEnhancedCallback(activity, onShowAdCompleteListener, onAdLoadingListener);
            return;
        }

        if (!shouldShowAd(activity)) {
            Log.d(TAG, "App Open Ad should not be shown based on frequency rules");
            if (onShowAdCompleteListener != null) {
                onShowAdCompleteListener.onShowAdComplete();
            }
            return;
        }

        Log.d(TAG, "Showing App Open Ad");

        appOpenAd.setFullScreenContentCallback(new FullScreenContentCallback() {
            @Override
            public void onAdDismissedFullScreenContent() {
                Log.d(TAG, "App Open Ad dismissed");
                appOpenAd = null;
                isShowingAd = false;

                // Update show statistics
                updateShowStatistics(activity);

                if (onShowAdCompleteListener != null) {
                    onShowAdCompleteListener.onShowAdComplete();
                }

                // Load next ad
                loadAd(activity);
            }

            @Override
            public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                Log.e(TAG, "App Open Ad failed to show: " + adError.getMessage());
                appOpenAd = null;
                isShowingAd = false;

                if (onShowAdCompleteListener != null) {
                    onShowAdCompleteListener.onShowAdComplete();
                }

                // Load next ad
                loadAd(activity);
            }

            @Override
            public void onAdShowedFullScreenContent() {
                Log.d(TAG, "App Open Ad showed successfully");
                isShowingAd = true;

                // Track ad impression
                trackAdEvent(activity, "app_open", "impression", appOpenAdUnitId);
            }

            @Override
            public void onAdClicked() {
                Log.d(TAG, "App Open Ad clicked");

                // Track ad click
                trackAdEvent(activity, "app_open", "click", appOpenAdUnitId);
            }
        });

        appOpenAd.show(activity);
    }

    /**
     * Load ad with callback support for splash screen
     */
    private void loadAdWithCallback(@NonNull Context context, @Nullable OnShowAdCompleteListener onShowAdCompleteListener) {
        loadAdWithEnhancedCallback(context, onShowAdCompleteListener, null);
    }

    /**
     * Load ad with enhanced callback support for splash screen
     */
    private void loadAdWithEnhancedCallback(@NonNull Context context,
                                          @Nullable OnShowAdCompleteListener onShowAdCompleteListener,
                                          @Nullable OnAdLoadingListener onAdLoadingListener) {
        // Check if ads are disabled or no ad unit ID
        if (!AdsHelper.isAds || appOpenAdUnitId.isEmpty()) {
            Log.d(TAG, "Cannot load ad - adsEnabled: " + AdsHelper.isAds + ", adUnitId: " + appOpenAdUnitId);
            if (onAdLoadingListener != null) {
                onAdLoadingListener.onAdLoadingCompleted(false);
                onAdLoadingListener.onAdFailed("Ads disabled or no ad unit ID");
            }
            return;
        }

        // If already loading, wait for current loading to complete
        if (isLoadingAd) {
            Log.d(TAG, "Ad is already loading, waiting for completion...");
            // For splash screen, we should wait rather than fail immediately
            // Set up a retry mechanism
            new android.os.Handler().postDelayed(() -> {
                if (isLoadingAd) {
                    Log.w(TAG, "Ad still loading after delay, forcing reset and retry");
                    isLoadingAd = false; // Reset the loading state
                    loadAdWithEnhancedCallback(context, onShowAdCompleteListener, onAdLoadingListener);
                } else if (isAdAvailable()) {
                    // Ad loaded successfully during wait
                    Log.d(TAG, "Ad became available during wait");
                    if (onAdLoadingListener != null) {
                        onAdLoadingListener.onAdLoadingCompleted(true);
                    }
                    if (context instanceof Activity) {
                        showLoadedAdWithCallbacks((Activity) context, onShowAdCompleteListener, onAdLoadingListener);
                    }
                } else {
                    // Still no ad, retry loading
                    Log.d(TAG, "No ad available after wait, retrying load");
                    loadAdWithEnhancedCallback(context, onShowAdCompleteListener, onAdLoadingListener);
                }
            }, 2000); // Wait 2 seconds before retry
            return;
        }

        Log.d(TAG, "Loading App Open Ad for splash screen");
        isLoadingAd = true;
        AdRequest request = new AdRequest.Builder().build();

        AppOpenAd.load(
            context,
            appOpenAdUnitId,
            request,
            new AppOpenAd.AppOpenAdLoadCallback() {
                @Override
                public void onAdLoaded(@NonNull AppOpenAd ad) {
                    Log.d(TAG, "App Open Ad loaded successfully for splash screen");
                    appOpenAd = ad;
                    isLoadingAd = false;
                    loadTime = new Date().getTime();

                    // Track ad load event
                    trackAdEvent(context, "app_open", "load", appOpenAdUnitId);

                    // Notify loading completed successfully
                    if (onAdLoadingListener != null) {
                        onAdLoadingListener.onAdLoadingCompleted(true);
                    }

                    // Now try to show the ad
                    if (context instanceof Activity) {
                        showLoadedAdWithCallbacks((Activity) context, onShowAdCompleteListener, onAdLoadingListener);
                    } else {
                        Log.w(TAG, "Context is not an Activity, cannot show ad");
                        if (onAdLoadingListener != null) {
                            onAdLoadingListener.onAdFailed("Context is not an Activity");
                        }
                        if (onShowAdCompleteListener != null) {
                            onShowAdCompleteListener.onShowAdComplete();
                        }
                    }
                }

                @Override
                public void onAdFailedToLoad(@NonNull LoadAdError loadAdError) {
                    Log.e(TAG, "App Open Ad failed to load for splash screen: " + loadAdError.getMessage());
                    isLoadingAd = false;

                    // Track ad load failure
                    trackAdEvent(context, "app_open", "fail", appOpenAdUnitId);

                    // Notify loading failed
                    if (onAdLoadingListener != null) {
                        onAdLoadingListener.onAdLoadingCompleted(false);
                        onAdLoadingListener.onAdFailed(loadAdError.getMessage());
                    }

                    // Call completion callback on failure
                    if (onShowAdCompleteListener != null) {
                        onShowAdCompleteListener.onShowAdComplete();
                    }
                }
            }
        );
    }

    /**
     * Show the loaded ad immediately
     */
    private void showLoadedAd(@NonNull Activity activity, @Nullable OnShowAdCompleteListener onShowAdCompleteListener) {
        if (!isAdAvailable()) {
            Log.w(TAG, "Ad is no longer available after loading");
            if (onShowAdCompleteListener != null) {
                onShowAdCompleteListener.onShowAdComplete();
            }
            return;
        }

        if (!shouldShowAd(activity)) {
            Log.d(TAG, "Ad should not be shown based on frequency rules");
            if (onShowAdCompleteListener != null) {
                onShowAdCompleteListener.onShowAdComplete();
            }
            return;
        }

        Log.d(TAG, "Showing loaded App Open Ad");

        appOpenAd.setFullScreenContentCallback(new FullScreenContentCallback() {
            @Override
            public void onAdDismissedFullScreenContent() {
                Log.d(TAG, "App Open Ad dismissed");
                appOpenAd = null;
                isShowingAd = false;

                // Update show statistics
                updateShowStatistics(activity);

                if (onShowAdCompleteListener != null) {
                    onShowAdCompleteListener.onShowAdComplete();
                }

                // Load next ad
                loadAd(activity);
            }

            @Override
            public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                Log.e(TAG, "App Open Ad failed to show: " + adError.getMessage());
                appOpenAd = null;
                isShowingAd = false;

                if (onShowAdCompleteListener != null) {
                    onShowAdCompleteListener.onShowAdComplete();
                }

                // Load next ad
                loadAd(activity);
            }

            @Override
            public void onAdShowedFullScreenContent() {
                Log.d(TAG, "App Open Ad showed successfully");
                isShowingAd = true;

                // Track ad impression
                trackAdEvent(activity, "app_open", "impression", appOpenAdUnitId);
            }

            @Override
            public void onAdClicked() {
                Log.d(TAG, "App Open Ad clicked");

                // Track ad click
                trackAdEvent(activity, "app_open", "click", appOpenAdUnitId);
            }
        });

        appOpenAd.show(activity);
    }

    /**
     * Show the loaded ad immediately with enhanced callbacks
     */
    private void showLoadedAdWithCallbacks(@NonNull Activity activity,
                                         @Nullable OnShowAdCompleteListener onShowAdCompleteListener,
                                         @Nullable OnAdLoadingListener onAdLoadingListener) {
        if (!isAdAvailable()) {
            Log.w(TAG, "Ad is no longer available after loading");
            if (onAdLoadingListener != null) {
                onAdLoadingListener.onAdFailed("Ad is no longer available");
            }
            if (onShowAdCompleteListener != null) {
                onShowAdCompleteListener.onShowAdComplete();
            }
            return;
        }

        // Skip frequency rules for splash screen ads - they should always show
        if (onAdLoadingListener == null && !shouldShowAd(activity)) {
            Log.d(TAG, "Ad should not be shown based on frequency rules");
            if (onShowAdCompleteListener != null) {
                onShowAdCompleteListener.onShowAdComplete();
            }
            return;
        } else if (onAdLoadingListener != null) {
            Log.d(TAG, "Splash screen ad - bypassing frequency rules");
        }

        Log.d(TAG, "Showing loaded App Open Ad with enhanced callbacks");

        // Notify that ad is about to be shown
        if (onAdLoadingListener != null) {
            onAdLoadingListener.onAdShown();
        }

        appOpenAd.setFullScreenContentCallback(new FullScreenContentCallback() {
            @Override
            public void onAdDismissedFullScreenContent() {
                Log.d(TAG, "App Open Ad dismissed");
                appOpenAd = null;
                isShowingAd = false;

                // Update statistics
                updateShowStatistics(activity);

                // Notify ad dismissed
                if (onAdLoadingListener != null) {
                    onAdLoadingListener.onAdDismissed();
                }

                // Call completion callback
                if (onShowAdCompleteListener != null) {
                    onShowAdCompleteListener.onShowAdComplete();
                }

                // Load next ad
                loadAd(activity);
            }

            @Override
            public void onAdFailedToShowFullScreenContent(@NonNull AdError adError) {
                Log.e(TAG, "App Open Ad failed to show: " + adError.getMessage());
                appOpenAd = null;
                isShowingAd = false;

                // Track ad show failure
                trackAdEvent(activity, "app_open", "show_fail", appOpenAdUnitId);

                // Notify ad failed
                if (onAdLoadingListener != null) {
                    onAdLoadingListener.onAdFailed(adError.getMessage());
                }

                // Call completion callback
                if (onShowAdCompleteListener != null) {
                    onShowAdCompleteListener.onShowAdComplete();
                }

                // Load next ad
                loadAd(activity);
            }

            @Override
            public void onAdShowedFullScreenContent() {
                Log.d(TAG, "App Open Ad showed successfully");
                isShowingAd = true;

                // Track ad impression
                trackAdEvent(activity, "app_open", "impression", appOpenAdUnitId);
            }

            @Override
            public void onAdClicked() {
                Log.d(TAG, "App Open Ad clicked");

                // Track ad click
                trackAdEvent(activity, "app_open", "click", appOpenAdUnitId);
            }
        });

        appOpenAd.show(activity);
    }

    /**
     * Check if ad should be shown based on frequency rules
     */
    private boolean shouldShowAd(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);

        // Check time interval
        long lastShown = prefs.getLong(KEY_LAST_SHOWN, 0);
        long currentTime = System.currentTimeMillis();

        if (currentTime - lastShown < MIN_INTERVAL_BETWEEN_ADS) {
            Log.d(TAG, "Too soon to show another ad. Last shown: " +
                      ((currentTime - lastShown) / 1000) + " seconds ago");
            return false;
        }

        // Check session count
        int showCount = prefs.getInt(KEY_SHOW_COUNT, 0);
        if (showCount >= MAX_ADS_PER_SESSION) {
            Log.d(TAG, "Maximum ads per session reached: " + showCount);
            return false;
        }

        return true;
    }

    /**
     * Update show statistics
     */
    private void updateShowStatistics(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();

        editor.putLong(KEY_LAST_SHOWN, System.currentTimeMillis());
        editor.putInt(KEY_SHOW_COUNT, prefs.getInt(KEY_SHOW_COUNT, 0) + 1);
        editor.apply();

        Log.d(TAG, "Updated show statistics - Count: " + prefs.getInt(KEY_SHOW_COUNT, 0));
    }

    /**
     * Reset session statistics (call when app starts)
     */
    public void resetSessionStatistics(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(KEY_SHOW_COUNT, 0);
        editor.apply();

        Log.d(TAG, "Session statistics reset");
    }

    /**
     * Track ad events to server
     */
    private void trackAdEvent(Context context, String adType, String eventType, String adUnitId) {
        // This will be implemented to send data to your admin panel API
        Log.d(TAG, "Tracking ad event - Type: " + adType + ", Event: " + eventType + ", Unit: " + adUnitId);

        // You can implement API call here to track ad events
        // Example: ApiHelper.trackAdEvent(adType, eventType, adUnitId);
    }

    // Activity Lifecycle Callbacks
    @Override
    public void onActivityCreated(@NonNull Activity activity, @Nullable Bundle savedInstanceState) {}

    @Override
    public void onActivityStarted(@NonNull Activity activity) {
        if (!isShowingAd) {
            currentActivity = activity;
        }
    }

    @Override
    public void onActivityResumed(@NonNull Activity activity) {
        if (!isShowingAd) {
            currentActivity = activity;
        }
    }

    @Override
    public void onActivityPaused(@NonNull Activity activity) {}

    @Override
    public void onActivityStopped(@NonNull Activity activity) {}

    @Override
    public void onActivitySaveInstanceState(@NonNull Activity activity, @NonNull Bundle outState) {}

    @Override
    public void onActivityDestroyed(@NonNull Activity activity) {}

    // Lifecycle Observer Methods
    @Override
    public void onStart(@NonNull LifecycleOwner owner) {
        if (currentActivity != null) {
            showAdIfAvailable(currentActivity);
        }
        Log.d(TAG, "App moved to foreground");
    }

    /**
     * Interface for ad show completion callback
     */
    public interface OnShowAdCompleteListener {
        void onShowAdComplete();
    }

    /**
     * Interface for ad loading progress callback
     */
    public interface OnAdLoadingListener {
        void onAdLoadingStarted();
        void onAdLoadingCompleted(boolean success);
        void onAdShown();
        void onAdDismissed();
        void onAdFailed(String error);
    }
}
