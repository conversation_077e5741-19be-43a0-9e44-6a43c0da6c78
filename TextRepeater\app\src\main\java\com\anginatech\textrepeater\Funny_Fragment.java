package com.anginatech.textrepeater;


import android.database.Cursor;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.Response;
import com.android.volley.VolleyError;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


public class Funny_Fragment extends Fragment {


    private DatabaseHelper databaseHelper;
    private RecyclerView recyclerView;
    private SmsAdapter adapter;
    private List<String> smsList = new ArrayList<>();


    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View myView = inflater.inflate(R.layout.fragment_funny_, container, false);
        if (container!=null){
            container.removeAllViews();
        }




        databaseHelper = new DatabaseHelper(getContext());
        recyclerView = myView.findViewById(R.id.recyclerView);
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext(), LinearLayoutManager.VERTICAL, false));
        recyclerView.setHasFixedSize(true);

        loadSms("funnyTable");

        return myView;

    }

    private void loadSms(String catagory) {
        Cursor cursor = databaseHelper.getAllDatabyCatagory(catagory);
        if (cursor.moveToFirst()&&cursor!=null) {
            do {
                String message = cursor.getString(1);
                smsList.add(message);
            } while (cursor.moveToNext());
        }else {
            Toast.makeText(getContext(), "Something went wrong!Please Check your Internet connection", Toast.LENGTH_SHORT).show();
        }
        cursor.close();

        adapter = new SmsAdapter(getContext(), smsList);
        recyclerView.setAdapter(adapter);
    }



    }








