package com.anginatech.textrepeater;


import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AppCompatDelegate;
import androidx.appcompat.widget.Toolbar;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import java.util.ArrayList;
import java.util.HashMap;

public class Text_to_Imoji_Activity extends AppCompatActivity {

    MaterialToolbar texttoImoji_MaterialToolbar;
    EditText emoji_editEnterText;
    TextView text_to_Emoji_Display;
    Button buttonEmoji,select_Emoji_Button;
    ConstraintLayout emoji_Copy_share_Layout,constraintLayoutEmoji_Copy,constraintLayout_Emoji_Share;
    TextView select_text_emoji;



    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        setContentView(R.layout.activity_text_to_imoji);
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.main), (v, insets) -> {
            Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
            v.setPadding(systemBars.left, systemBars.top, systemBars.right, systemBars.bottom);
            return insets;
        });

        SharedPreferences sharedPreferences = getSharedPreferences("nightModePrefs", MODE_PRIVATE);
        boolean isNightMode = sharedPreferences.getBoolean("nightMode", false);

        if (isNightMode) {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_YES);
        } else {
            AppCompatDelegate.setDefaultNightMode(AppCompatDelegate.MODE_NIGHT_NO);
        }


        texttoImoji_MaterialToolbar = findViewById(R.id.texttoImoji_MaterialToolbar);
        emoji_editEnterText = findViewById(R.id.emoji_editEnterText);
        select_Emoji_Button = findViewById(R.id.select_Emoji_Button);
        text_to_Emoji_Display = findViewById(R.id.text_to_Emoji_Display);
        buttonEmoji = findViewById(R.id.buttonEmoji);
        emoji_Copy_share_Layout = findViewById(R.id.emoji_Copy_share_Layout);
        constraintLayoutEmoji_Copy = findViewById(R.id.constraintLayoutEmoji_Copy);
        constraintLayout_Emoji_Share = findViewById(R.id.constraintLayout_Emoji_Share);
        select_text_emoji = findViewById(R.id.select_text_emoji);



        texttoImoji_MaterialToolbar.setNavigationOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                startActivity(new Intent(Text_to_Imoji_Activity.this, MainActivity.class));
                finish();
            }
        });

        texttoImoji_MaterialToolbar.setTitleTextAppearance(Text_to_Imoji_Activity.this,R.style.RobotoBoldTextAppearance);


        texttoImoji_MaterialToolbar.setOnMenuItemClickListener(new Toolbar.OnMenuItemClickListener() {
            @Override
            public boolean onMenuItemClick(MenuItem item) {
                if (item.getItemId()==R.id.clear_Data) {
                    text_to_Emoji_Display.setText("");
                    emoji_editEnterText.setText("");
                    emoji_Copy_share_Layout.setVisibility(View.GONE);
                    select_Emoji_Button.setTextSize(16);
                    select_text_emoji.setText("Select Emoji");
                    select_Emoji_Button.setText("");
                }

                return false;
            }
        });


        select_Emoji_Button.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                showEmojiBottomSheet();

            }
        });







        buttonEmoji.setOnClickListener(v -> {
            text_to_Emoji_Display.setText("");

            String input = emoji_editEnterText.getText().toString().toUpperCase().trim();
            String emoji = select_Emoji_Button.getText().toString().trim();

            if (input.isEmpty() || emoji.isEmpty()) {
                Toast.makeText(this, "Please provide both letters/Text and Emoji!", Toast.LENGTH_SHORT).show();
                return;
            }
            select_text_emoji.setText("");
            StringBuilder result = new StringBuilder();

            for (char c : input.toCharArray()) {
                if (Character.isLetterOrDigit(c)) {
                    result.append(PatternGenerator.generatePattern(c, emoji)).append("\n");
                } else if (c == ' ') {
                    result.append("\n"); // Add blank line for space
                } else {
                    result.append("Invalid character: ").append(c).append("\n");
                }
            }

            // Display the generated patterns
            text_to_Emoji_Display.setText(result.toString());
            emoji_Copy_share_Layout.setVisibility(View.VISIBLE);
        });


        constraintLayoutEmoji_Copy.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {

                String text = text_to_Emoji_Display.getText().toString();
                TextCopyToClipboard(text);


            }
        });


        constraintLayout_Emoji_Share.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                String text = text_to_Emoji_Display.getText().toString();
                TextShareText(text);

            }
        });



    }



    @Override
    public void onBackPressed() {
        
        startActivity(new Intent(Text_to_Imoji_Activity.this, MainActivity.class));
        finish();
        super.onBackPressed();
    }


    private void showEmojiBottomSheet() {
        BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(this);
        View bottomSheetView = LayoutInflater.from(this).inflate(R.layout.emoji_dailog, null);
        bottomSheetDialog.setContentView(bottomSheetView);


        GridView emojiGridView = bottomSheetView.findViewById(R.id.emoji_GridView);
        String[] emojis = {
                "🤣", "😆", "😄", "😁", "😃", "😝", "🤪", "🥳", "😏",
                "😊", "😂", "😘", "😗", "😙", "😚", "😎", "🤔", "😢", "😡", "🤩", "😇",
                "😋", "😜", "😝", "🤪", "🥳", "😏", "🙃", "🥺", "😌", "🤗", "🤭", "🤫",
                "😍", "🥰", "❤️", "💖", "💕", "💞", "💓", "💗", "💘", "💝", "💟", "❣️",
                "❤️‍🔥", "❤️‍🩹", "💌", "👩‍❤️‍👨", "💔", "🌹", "💐",
                "🌞", "🌙", "🌸", "🌺", "🌻", "🍀", "🌊", "⛄", "🎄",
                "🍎", "🍕", "🎂", "🍩", "🍇", "🍉", "🍓", "🍒", "🍑", "🍍",
                "🥭", "🥝", "🥑", "🍔", "🍟", "🌮", "🥗", "🍱", "🍜", "🍦",
                "🧁", "🍰", "🥧", "🍹", "☕", "🍫", "🍪", "🍿", "🌭", "🥪",
                "🎁", "🎀", "🎉", "✨", "🌟", "🌈", "⚡", "🔥", "💎", "📱",

        };

        EmojiGridAdapter adapter = new EmojiGridAdapter(this, emojis);
        emojiGridView.setAdapter(adapter);

        emojiGridView.setOnItemClickListener((parent, view, position, id) -> {
            String selectedEmoji = emojis[position];
            select_Emoji_Button.setText(selectedEmoji);
            select_Emoji_Button.setTextSize(26);
            bottomSheetDialog.dismiss();
            select_text_emoji.setText("");
        });

        bottomSheetDialog.show();
    }


    private void TextCopyToClipboard(String text) {
        if (text.isEmpty()) {
            Toast.makeText(this, "No text to copy!", Toast.LENGTH_SHORT).show();
            return;
        }

        if (text.length() > 100000) {
            Toast.makeText(this, "Text too long to copy (Max 100k characters)", Toast.LENGTH_LONG).show();
            return;
        }
        android.content.ClipboardManager clipboard =
                (android.content.ClipboardManager) getSystemService(CLIPBOARD_SERVICE);
        android.content.ClipData clip = android.content.ClipData.newPlainText("text_to_emoji", text);
        clipboard.setPrimaryClip(clip);

        Toast.makeText(this, "Text copied to clipboard!", Toast.LENGTH_SHORT).show();

    }


    private void TextShareText(String text) {
        if (text.isEmpty()) {
            Toast.makeText(this, "No text to share!", Toast.LENGTH_SHORT).show();
            return;
        }

        if (text.length() > 50000) {
            Toast.makeText(this, "Text too long to share (Max 50k characters)", Toast.LENGTH_LONG).show();
            return;
        }
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, text);
        startActivity(Intent.createChooser(shareIntent, "Share via"));

    }






}