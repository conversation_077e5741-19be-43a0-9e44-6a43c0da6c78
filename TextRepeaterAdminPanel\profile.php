<?php
$page_title = 'My Profile';
require_once 'includes/header.php';

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['update_profile'])) {
        $username = trim($_POST['username']);
        $email = trim($_POST['email']);
        $full_name = trim($_POST['full_name']);

        // Validate input
        $errors = [];
        if (empty($username)) $errors[] = "Username is required";
        if (empty($email)) $errors[] = "Email is required";
        if (empty($full_name)) $errors[] = "Full name is required";

        // Check if username or email already exists (excluding current user)
        if (empty($errors)) {
            $stmt = $conn->prepare("SELECT id FROM admin_users WHERE (username = :username OR email = :email) AND id != :current_id");
            $stmt->bindParam(':username', $username);
            $stmt->bindParam(':email', $email);
            $stmt->bindParam(':current_id', $current_user['id']);
            $stmt->execute();
            if ($stmt->fetch()) {
                $errors[] = "Username or email already exists";
            }
        }

        if (empty($errors)) {
            try {
                $stmt = $conn->prepare("
                    UPDATE admin_users
                    SET username = :username, email = :email, full_name = :full_name, updated_at = NOW()
                    WHERE id = :id
                ");
                $stmt->bindParam(':username', $username);
                $stmt->bindParam(':email', $email);
                $stmt->bindParam(':full_name', $full_name);
                $stmt->bindParam(':id', $current_user['id']);
                $stmt->execute();

                // Update session data
                $_SESSION['user']['username'] = $username;
                $_SESSION['user']['email'] = $email;
                $_SESSION['user']['full_name'] = $full_name;
                $current_user = $_SESSION['user'];

                $success_message = "Profile updated successfully.";
            } catch (Exception $e) {
                $errors[] = "Error updating profile: " . $e->getMessage();
            }
        }
    } elseif (isset($_POST['change_password'])) {
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];

        // Validate input
        $errors = [];
        if (empty($current_password)) $errors[] = "Current password is required";
        if (empty($new_password)) $errors[] = "New password is required";
        if (strlen($new_password) < Config::PASSWORD_MIN_LENGTH) $errors[] = "New password must be at least " . Config::PASSWORD_MIN_LENGTH . " characters";
        if ($new_password !== $confirm_password) $errors[] = "New passwords do not match";

        // Verify current password
        if (empty($errors)) {
            $stmt = $conn->prepare("SELECT password FROM admin_users WHERE id = :id");
            $stmt->bindParam(':id', $current_user['id']);
            $stmt->execute();
            $user_data = $stmt->fetch();

            if (!password_verify($current_password, $user_data['password'])) {
                $errors[] = "Current password is incorrect";
            }
        }

        if (empty($errors)) {
            try {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $conn->prepare("
                    UPDATE admin_users
                    SET password = :password, updated_at = NOW()
                    WHERE id = :id
                ");
                $stmt->bindParam(':password', $hashed_password);
                $stmt->bindParam(':id', $current_user['id']);
                $stmt->execute();

                $success_message = "Password changed successfully.";
            } catch (Exception $e) {
                $errors[] = "Error changing password: " . $e->getMessage();
            }
        }
    }
}

// Get user's login history (with error handling for missing table)
$login_history = [];
try {
    $stmt = $conn->prepare("
        SELECT login_time, ip_address, user_agent
        FROM admin_login_history
        WHERE user_id = :user_id
        ORDER BY login_time DESC
        LIMIT 10
    ");
    $stmt->bindParam(':user_id', $current_user['id']);
    $stmt->execute();
    $login_history = $stmt->fetchAll();
} catch (PDOException $e) {
    // Table doesn't exist yet - this is okay, we'll show a message
    $login_history = [];
    $login_history_error = "Login history table not found. Please run the database setup script.";
}
?>

<?php if (isset($success_message)): ?>
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <?php echo $success_message; ?>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<?php if (isset($errors) && !empty($errors)): ?>
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
                <li><?php echo $error; ?></li>
            <?php endforeach; ?>
        </ul>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
<?php endif; ?>

<div class="row">
    <!-- Profile Information -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Profile Information</h6>
            </div>
            <div class="card-body">
                <form method="POST">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">Username</label>
                            <input type="text" class="form-control" id="username" name="username"
                                   value="<?php echo htmlspecialchars($current_user['username']); ?>" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?php echo htmlspecialchars($current_user['email']); ?>" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="full_name" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="full_name" name="full_name"
                               value="<?php echo htmlspecialchars($current_user['full_name']); ?>" required>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Role</label>
                            <input type="text" class="form-control"
                                   value="<?php echo ucfirst(str_replace('_', ' ', $current_user['role'])); ?>" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Status</label>
                            <input type="text" class="form-control"
                                   value="<?php echo ucfirst($current_user['status']); ?>" readonly>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Member Since</label>
                            <input type="text" class="form-control"
                                   value="<?php echo date('F j, Y', strtotime($current_user['created_at'])); ?>" readonly>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Last Login</label>
                            <input type="text" class="form-control"
                                   value="<?php echo $current_user['last_login'] ? date('F j, Y g:i A', strtotime($current_user['last_login'])) : 'Never'; ?>" readonly>
                        </div>
                    </div>

                    <button type="submit" name="update_profile" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Profile
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Account Summary -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Account Summary</h6>
            </div>
            <div class="card-body text-center">
                <div class="user-avatar mx-auto mb-3" style="width: 80px; height: 80px; font-size: 32px;">
                    <?php echo strtoupper(substr($current_user['full_name'], 0, 1)); ?>
                </div>
                <h5 class="mb-1"><?php echo htmlspecialchars($current_user['full_name']); ?></h5>
                <p class="text-muted mb-2">@<?php echo htmlspecialchars($current_user['username']); ?></p>
                <span class="badge bg-primary"><?php echo ucfirst(str_replace('_', ' ', $current_user['role'])); ?></span>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                        <i class="fas fa-key me-2"></i>Change Password
                    </button>
                    <a href="dashboard.php" class="btn btn-outline-secondary">
                        <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                    </a>
                    <a href="logout.php" class="btn btn-outline-danger">
                        <i class="fas fa-sign-out-alt me-2"></i>Logout
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Login History -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Recent Login History</h6>
            </div>
            <div class="card-body">
                <?php if (isset($login_history_error)): ?>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo $login_history_error; ?>
                        <br>
                        <a href="setup-database.php" class="btn btn-sm btn-primary mt-2">
                            <i class="fas fa-database me-2"></i>Run Database Setup
                        </a>
                    </div>
                <?php elseif (empty($login_history)): ?>
                    <p class="text-muted">No login history available.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>IP Address</th>
                                    <th>User Agent</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($login_history as $login): ?>
                                    <tr>
                                        <td>
                                            <?php echo date('M j, Y g:i A', strtotime($login['login_time'])); ?>
                                        </td>
                                        <td>
                                            <code><?php echo htmlspecialchars($login['ip_address']); ?></code>
                                        </td>
                                        <td>
                                            <small><?php echo htmlspecialchars(substr($login['user_agent'], 0, 100)); ?></small>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Change Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="new_password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required>
                        <div class="form-text">Minimum <?php echo Config::PASSWORD_MIN_LENGTH; ?> characters</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" name="change_password" class="btn btn-primary">Change Password</button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.user-avatar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
}
</style>

<?php require_once 'includes/footer.php'; ?>
