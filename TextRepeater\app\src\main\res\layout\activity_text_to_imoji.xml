<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Text_to_Imoji_Activity"
    android:background="@color/mother_layout_color"
    >



    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        >


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >



    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/texttoImoji_MaterialToolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:navigationIcon="@drawable/baseline_arrow_back_ios_24"
        app:title="Text to Emoji"
        app:navigationIconTint="@color/toolbar_text_color"
        app:titleTextColor="@color/toolbar_text_color"
        app:menu="@menu/settings_item"
        >


    </com.google.android.material.appbar.MaterialToolbar>


            <EditText
                android:id="@+id/emoji_editEnterText"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="10dp"
                android:layout_marginEnd="30dp"
                android:background="@drawable/edittext_backround"
                android:drawableStart="@drawable/baseline_text_fields_24"
                android:ems="10"
                android:hint=" Enter text"
                android:inputType="text"
                android:maxLength="100"
                android:paddingLeft="16dp"
                android:paddingEnd="10dp"
                android:textColor="@color/primary_text"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.402"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/texttoImoji_MaterialToolbar"
                app:layout_constraintVertical_bias="0.0" />


    <Button
        android:id="@+id/buttonEmoji"
        android:layout_width="170dp"
        android:layout_height="58dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="22dp"
        android:backgroundTint="@color/love"
        android:text="Generate"
        android:textSize="18sp"
        android:textColor="@color/white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/emoji_editEnterText"
        app:layout_constraintVertical_bias="0.0" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout"
                android:layout_width="match_parent"
                android:layout_height="350dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="20dp"
                android:layout_marginEnd="16dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/buttonEmoji"
                app:layout_constraintVertical_bias="0.0">

                <androidx.core.widget.NestedScrollView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:fadeScrollbars="false"
                    android:scrollbarSize="3dp"
                    android:scrollbarThumbVertical="@color/love"
                    android:scrollbars="vertical"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/text_to_Emoji_Display"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:gravity="start"
                        android:padding="6dp"
                        android:layout_marginStart="10dp"
                        android:textColor="@color/primary_text"
                        android:textSize="18sp"

                        />


                </androidx.core.widget.NestedScrollView>


            </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/emoji_Copy_share_Layout"
        android:layout_width="match_parent"
        android:layout_height="58dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/constraintLayout"
        app:layout_constraintVertical_bias="0.0"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginTop="2dp"
        android:background="@drawable/copy_share_back"
        android:visibility="gone"

        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayoutEmoji_Copy"
            android:layout_width="170dp"
            android:layout_height="match_parent"
            android:clickable="true"
            android:foreground="?attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0">


            <ImageView
                android:id="@+id/imageView10"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginStart="38dp"
                android:src="@drawable/baseline_content_copy_24"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView41"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:fontFamily="@font/allerta"
                android:text="Copy"
                android:textColor="@color/secondary_text"
                android:textSize="17sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/imageView10"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.508" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraintLayout_Emoji_Share"
            android:layout_width="170dp"
            android:layout_height="match_parent"
            android:clickable="true"
            android:foreground="?attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toEndOf="@+id/constraintLayoutEmoji_Copy"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imageView4"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginStart="36dp"
                android:src="@drawable/baseline_share_24"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:fontFamily="@font/allerta"
                android:text="Share"
                android:textColor="@color/secondary_text"
                android:textSize="17sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/imageView4"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.508" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <View
            android:layout_width="2dp"
            android:layout_height="match_parent"
            android:layout_marginVertical="10dp"
            android:background="@drawable/view_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/constraintLayout_Emoji_Share"
            app:layout_constraintStart_toEndOf="@+id/constraintLayoutEmoji_Copy"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="160dp"
                android:layout_height="52dp"
               android:layout_marginTop="24dp"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="16dp"
                android:background="@drawable/edittext_backround"
                app:layout_constraintBottom_toTopOf="@+id/constraintLayout"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/buttonEmoji"
                app:layout_constraintTop_toBottomOf="@+id/emoji_editEnterText"
                app:layout_constraintVertical_bias="0.0"

                >


                <TextView
                    android:id="@+id/select_text_emoji"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:gravity="center"
                    android:text="Select Emoji"
                    android:textColor="@color/primary_text"
                    android:textSize="17sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />





                <Button
                    android:id="@+id/select_Emoji_Button"
                    android:layout_width="150dp"
                    android:layout_height="58dp"
                    android:elevation="0dp"
                    android:gravity="center"
                    android:background="@android:color/transparent"
                    android:textAllCaps="false"
                    android:textColor="@color/primary_text"
                    android:textSize="16sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />




            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>




</androidx.constraintlayout.widget.ConstraintLayout>