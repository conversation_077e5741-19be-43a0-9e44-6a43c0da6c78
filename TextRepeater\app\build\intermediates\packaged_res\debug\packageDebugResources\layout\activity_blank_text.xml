<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Blank_Text_Activity"
    android:background="@color/mother_layout_color"
    >


    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/blank_MaterialToolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:navigationIcon="@drawable/baseline_arrow_back_ios_24"
        app:navigationIconTint="@color/toolbar_text_color"
        app:titleTextColor="@color/toolbar_text_color"
        app:title="@string/item_title7"
        app:menu="@menu/settings_item"
        >

    </com.google.android.material.appbar.MaterialToolbar>


    <EditText
        android:id="@+id/blank_editEnterlimit"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="30dp"
        android:background="@drawable/edittext_backround"
        android:drawableStart="@drawable/baseline_repeat_24"
        android:ems="10"
        android:hint=" Repetition limit"
        android:inputType="number"
        android:maxLength="5"
        android:paddingLeft="16dp"
        android:paddingEnd="10dp"
        android:textColor="@color/primary_text"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.402"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/blank_MaterialToolbar"
        app:layout_constraintVertical_bias="0.0" />


    <androidx.appcompat.widget.AppCompatCheckBox
        android:id="@+id/blank_New_Line"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="10dp"
        android:text="New Line"
        app:buttonTint="@color/love"
        android:textSize="16.5sp"
        android:fontFamily="@font/alice"
        app:layout_constraintBottom_toTopOf="@+id/blank_buttonRepeat"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/blank_editEnterlimit"
        app:layout_constraintVertical_bias="0.0"

        />

    <Button
        android:id="@+id/blank_buttonRepeat"
        android:layout_width="match_parent"
        android:layout_height="58dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="70dp"
        android:layout_marginEnd="36dp"
        android:backgroundTint="@color/love"
        android:text="Generate"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="18sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/blank_editEnterlimit"
        app:layout_constraintVertical_bias="0.0" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraintLayout8"
        android:layout_width="match_parent"
        android:layout_height="100dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/blank_buttonRepeat"
        app:layout_constraintVertical_bias="0.0">


        <TextView
            android:id="@+id/blank_Display"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0" />


    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/blank_layout_Copy_Share"
        android:layout_width="match_parent"
        android:layout_height="58dp"
        android:layout_marginEnd="16dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="2dp"
        android:layout_marginBottom="75dp"
        android:background="@drawable/copy_share_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/constraintLayout8"
        app:layout_constraintVertical_bias="0.0"
        android:visibility="gone"
        >


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/blankLayout_Copy"
            android:layout_width="170dp"
            android:layout_height="match_parent"
            android:clickable="true"
            android:foreground="?attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0">


            <ImageView
                android:id="@+id/imageView10"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginStart="38dp"
                android:src="@drawable/baseline_content_copy_24"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView41"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:fontFamily="@font/allerta"
                android:text="Copy"
                android:textColor="@color/secondary_text"
                android:textSize="17sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/imageView10"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.508" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/blankLayout_Share"
            android:layout_width="170dp"
            android:layout_height="match_parent"
            android:clickable="true"
            android:foreground="?attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintStart_toEndOf="@+id/blankLayout_Copy"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/imageView4"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginStart="36dp"
                android:src="@drawable/baseline_share_24"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/textView4"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:fontFamily="@font/allerta"
                android:text="Share"
                android:textColor="@color/secondary_text"
                android:textSize="17sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/imageView4"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.508" />

        </androidx.constraintlayout.widget.ConstraintLayout>


        <View
            android:layout_width="2dp"
            android:layout_height="match_parent"
            android:layout_marginVertical="10dp"
            android:background="@drawable/view_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/blankLayout_Share"
            app:layout_constraintStart_toEndOf="@+id/blankLayout_Copy"
            app:layout_constraintTop_toTopOf="parent" />


    </androidx.constraintlayout.widget.ConstraintLayout>







</androidx.constraintlayout.widget.ConstraintLayout>