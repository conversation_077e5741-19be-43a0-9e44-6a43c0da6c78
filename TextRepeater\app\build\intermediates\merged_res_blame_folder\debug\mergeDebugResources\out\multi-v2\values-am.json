{"logs": [{"outputFile": "com.anginatech.textrepeater.app-mergeDebugResources-55:/values-am/values-am.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\a6be65feae4b33c1c7fd18a0c0ae1d8a\\transformed\\appcompat-1.7.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,203,301,407,493,596,713,791,867,958,1051,1143,1237,1337,1430,1525,1618,1709,1800,1880,1980,2080,2176,2278,2378,2477,2627,2723", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "198,296,402,488,591,708,786,862,953,1046,1138,1232,1332,1425,1520,1613,1704,1795,1875,1975,2075,2171,2273,2373,2472,2622,2718,2798"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,155", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "311,409,507,613,699,802,919,997,1073,1164,1257,1349,1443,1543,1636,1731,1824,1915,2006,2086,2186,2286,2382,2484,2584,2683,2833,12761", "endColumns": "97,97,105,85,102,116,77,75,90,92,91,93,99,92,94,92,90,90,79,99,99,95,101,99,98,149,95,79", "endOffsets": "404,502,608,694,797,914,992,1068,1159,1252,1344,1438,1538,1631,1726,1819,1910,2001,2081,2181,2281,2377,2479,2579,2678,2828,2924,12836"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\6a607bbfcc83cfb280a4c2f3d8ba8b9e\\transformed\\play-services-basement-18.5.0\\res\\values-am\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "56", "startColumns": "4", "startOffsets": "5186", "endColumns": "131", "endOffsets": "5313"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\446fe71953247653d35c77c031840418\\transformed\\browser-1.8.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,151,246,352", "endColumns": "95,94,105,96", "endOffsets": "146,241,347,444"}, "to": {"startLines": "66,70,71,72", "startColumns": "4,4,4,4", "startOffsets": "6267,6571,6666,6772", "endColumns": "95,94,105,96", "endOffsets": "6358,6661,6767,6864"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\131f10e519d4cb41eac2bb9322c4fa1c\\transformed\\play-services-ads-24.3.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,239,286,337,398,461,546,606,696,776,876,926,984,1087,1158,1195,1269,1301,1337,1383,1447,1486", "endColumns": "39,46,50,60,62,84,59,89,79,99,49,57,102,70,36,73,31,35,45,63,38,55", "endOffsets": "238,285,336,397,460,545,605,695,775,875,925,983,1086,1157,1194,1268,1300,1336,1382,1446,1485,1541"}, "to": {"startLines": "133,134,135,136,137,138,139,140,141,142,143,144,145,146,148,149,150,151,152,153,154,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "11314,11358,11409,11464,11529,11596,11685,11749,11843,11927,12031,12085,12147,12254,12405,12446,12524,12560,12600,12650,12718,13167", "endColumns": "43,50,54,64,66,88,63,93,83,103,53,61,106,74,40,77,35,39,49,67,42,59", "endOffsets": "11353,11404,11459,11524,11591,11680,11744,11838,11922,12026,12080,12142,12249,12324,12441,12519,12555,12595,12645,12713,12756,13222"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\917d0d7e0c096b9e28e4e727a3aa98ed\\transformed\\play-services-base-18.5.0\\res\\values-am\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,291,426,544,642,765,884,988,1086,1210,1309,1450,1569,1700,1823,1879,1932", "endColumns": "97,134,117,97,122,118,103,97,123,98,140,118,130,122,55,52,66", "endOffsets": "290,425,543,641,764,883,987,1085,1209,1308,1449,1568,1699,1822,1878,1931,1998"}, "to": {"startLines": "48,49,50,51,52,53,54,55,57,58,59,60,61,62,63,64,65", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4261,4363,4502,4624,4726,4853,4976,5084,5318,5446,5549,5694,5817,5952,6079,6139,6196", "endColumns": "101,138,121,101,126,122,107,101,127,102,144,122,134,126,59,56,70", "endOffsets": "4358,4497,4619,4721,4848,4971,5079,5181,5441,5544,5689,5812,5947,6074,6134,6191,6262"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\c778fdfe6002d7eef6060ec9b8f3c394\\transformed\\core-1.13.1\\res\\values-am\\values-am.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,248,345,444,540,642,742", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "143,243,340,439,535,637,737,838"}, "to": {"startLines": "38,39,40,41,42,43,44,159", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3306,3399,3499,3596,3695,3791,3893,13066", "endColumns": "92,99,96,98,95,101,99,100", "endOffsets": "3394,3494,3591,3690,3786,3888,3988,13162"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.14.1\\transforms\\bc7f51ca89ffc0f741ddbaa1d183190a\\transformed\\material-1.12.0\\res\\values-am\\values-am.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,261,332,400,475,557,638,727,829,906,965,1029,1114,1176,1234,1319,1382,1444,1502,1568,1630,1685,1781,1838,1897,1953,2020,2125,2205,2286,2378,2463,2544,2673,2746,2817,2931,3013,3089,3140,3191,3257,3323,3396,3467,3542,3610,3683,3754,3821,3919,4004,4071,4158,4246,4320,4388,4473,4524,4602,4666,4746,4828,4890,4954,5017,5083,5178,5273,5358,5449,5504,5559,5635,5714,5789", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,70,67,74,81,80,88,101,76,58,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75,78,74,70", "endOffsets": "256,327,395,470,552,633,722,824,901,960,1024,1109,1171,1229,1314,1377,1439,1497,1563,1625,1680,1776,1833,1892,1948,2015,2120,2200,2281,2373,2458,2539,2668,2741,2812,2926,3008,3084,3135,3186,3252,3318,3391,3462,3537,3605,3678,3749,3816,3914,3999,4066,4153,4241,4315,4383,4468,4519,4597,4661,4741,4823,4885,4949,5012,5078,5173,5268,5353,5444,5499,5554,5630,5709,5784,5855"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,147,156,157,158", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,2929,3000,3068,3143,3225,3993,4082,4184,6363,6422,6486,6869,6931,6989,7074,7137,7199,7257,7323,7385,7440,7536,7593,7652,7708,7775,7880,7960,8041,8133,8218,8299,8428,8501,8572,8686,8768,8844,8895,8946,9012,9078,9151,9222,9297,9365,9438,9509,9576,9674,9759,9826,9913,10001,10075,10143,10228,10279,10357,10421,10501,10583,10645,10709,10772,10838,10933,11028,11113,11204,11259,12329,12841,12920,12995", "endLines": "5,33,34,35,36,37,45,46,47,67,68,69,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,147,156,157,158", "endColumns": "12,70,67,74,81,80,88,101,76,58,63,84,61,57,84,62,61,57,65,61,54,95,56,58,55,66,104,79,80,91,84,80,128,72,70,113,81,75,50,50,65,65,72,70,74,67,72,70,66,97,84,66,86,87,73,67,84,50,77,63,79,81,61,63,62,65,94,94,84,90,54,54,75,78,74,70", "endOffsets": "306,2995,3063,3138,3220,3301,4077,4179,4256,6417,6481,6566,6926,6984,7069,7132,7194,7252,7318,7380,7435,7531,7588,7647,7703,7770,7875,7955,8036,8128,8213,8294,8423,8496,8567,8681,8763,8839,8890,8941,9007,9073,9146,9217,9292,9360,9433,9504,9571,9669,9754,9821,9908,9996,10070,10138,10223,10274,10352,10416,10496,10578,10640,10704,10767,10833,10928,11023,11108,11199,11254,11309,12400,12915,12990,13061"}}]}]}