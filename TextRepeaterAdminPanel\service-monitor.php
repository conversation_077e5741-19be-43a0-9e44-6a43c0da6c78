<?php
/**
 * Background Notification Service Monitor
 * Web interface to monitor and control the background notification service
 */

require_once 'includes/header.php';

$pidFile = __DIR__ . '/cron/background-service.pid';
$logFile = __DIR__ . '/cron/background-service.log';

// Handle actions
$action = $_POST['action'] ?? $_GET['action'] ?? '';
$message = '';
$messageType = '';

if ($action) {
    switch ($action) {
        case 'start':
            if (isServiceRunning()) {
                $message = 'Service is already running';
                $messageType = 'warning';
            } else {
                $command = 'start /B C:\xampp\php\php.exe "' . __DIR__ . '\cron\background-notification-service.php" start';
                pclose(popen($command, "r"));
                sleep(2); // Give it time to start
                $message = 'Service started successfully';
                $messageType = 'success';
            }
            break;
            
        case 'stop':
            if (!isServiceRunning()) {
                $message = 'Service is not running';
                $messageType = 'warning';
            } else {
                $pid = trim(file_get_contents($pidFile));
                exec("taskkill /PID $pid /F");
                $message = 'Service stopped successfully';
                $messageType = 'success';
            }
            break;
            
        case 'restart':
            if (isServiceRunning()) {
                $pid = trim(file_get_contents($pidFile));
                exec("taskkill /PID $pid /F");
                sleep(2);
            }
            $command = 'start /B C:\xampp\php\php.exe "' . __DIR__ . '\cron\background-notification-service.php" start';
            pclose(popen($command, "r"));
            sleep(2);
            $message = 'Service restarted successfully';
            $messageType = 'success';
            break;
    }
}

function isServiceRunning() {
    global $pidFile;
    
    if (!file_exists($pidFile)) {
        return false;
    }
    
    $pid = trim(file_get_contents($pidFile));
    $output = shell_exec("tasklist /FI \"PID eq $pid\" 2>NUL");
    return strpos($output, $pid) !== false;
}

function getServiceStatus() {
    global $pidFile;
    
    if (!isServiceRunning()) {
        return [
            'status' => 'stopped',
            'pid' => null,
            'uptime' => null
        ];
    }
    
    $pid = trim(file_get_contents($pidFile));
    
    // Calculate uptime from log file
    $uptime = null;
    if (file_exists(__DIR__ . '/cron/background-service.log')) {
        $logs = file(__DIR__ . '/cron/background-service.log');
        foreach ($logs as $log) {
            if (strpos($log, 'Background Notification Service Starting') !== false) {
                preg_match('/\[(.*?)\]/', $log, $matches);
                if ($matches) {
                    $startTime = strtotime($matches[1]);
                    $uptime = time() - $startTime;
                    break;
                }
            }
        }
    }
    
    return [
        'status' => 'running',
        'pid' => $pid,
        'uptime' => $uptime
    ];
}

function formatUptime($seconds) {
    if (!$seconds) return 'Unknown';
    
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $seconds = $seconds % 60;
    
    return sprintf("%02d:%02d:%02d", $hours, $minutes, $seconds);
}

function getRecentLogs($lines = 20) {
    global $logFile;
    
    if (!file_exists($logFile)) {
        return [];
    }
    
    $logs = file($logFile);
    return array_slice($logs, -$lines);
}

$serviceStatus = getServiceStatus();
$recentLogs = getRecentLogs();
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-server"></i> Background Notification Service Monitor
                    </h3>
                </div>
                <div class="card-body">
                    
                    <?php if ($message): ?>
                    <div class="alert alert-<?php echo $messageType; ?> alert-dismissible">
                        <button type="button" class="close" data-dismiss="alert">&times;</button>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Service Status -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-<?php echo $serviceStatus['status'] === 'running' ? 'success' : 'danger'; ?>">
                                    <i class="fas fa-<?php echo $serviceStatus['status'] === 'running' ? 'play' : 'stop'; ?>"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Service Status</span>
                                    <span class="info-box-number">
                                        <?php echo ucfirst($serviceStatus['status']); ?>
                                        <?php if ($serviceStatus['pid']): ?>
                                            (PID: <?php echo $serviceStatus['pid']; ?>)
                                        <?php endif; ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="info-box">
                                <span class="info-box-icon bg-info">
                                    <i class="fas fa-clock"></i>
                                </span>
                                <div class="info-box-content">
                                    <span class="info-box-text">Uptime</span>
                                    <span class="info-box-number">
                                        <?php echo formatUptime($serviceStatus['uptime']); ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Control Buttons -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <div class="btn-group" role="group">
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="action" value="start">
                                    <button type="submit" class="btn btn-success" <?php echo $serviceStatus['status'] === 'running' ? 'disabled' : ''; ?>>
                                        <i class="fas fa-play"></i> Start Service
                                    </button>
                                </form>
                                
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="action" value="stop">
                                    <button type="submit" class="btn btn-danger" <?php echo $serviceStatus['status'] === 'stopped' ? 'disabled' : ''; ?>>
                                        <i class="fas fa-stop"></i> Stop Service
                                    </button>
                                </form>
                                
                                <form method="post" style="display: inline;">
                                    <input type="hidden" name="action" value="restart">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-redo"></i> Restart Service
                                    </button>
                                </form>
                                
                                <button type="button" class="btn btn-info" onclick="location.reload()">
                                    <i class="fas fa-sync"></i> Refresh
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Logs -->
                    <div class="row">
                        <div class="col-md-12">
                            <h4>Recent Log Entries</h4>
                            <div class="card">
                                <div class="card-body">
                                    <?php if (empty($recentLogs)): ?>
                                        <p class="text-muted">No log entries found.</p>
                                    <?php else: ?>
                                        <pre style="max-height: 400px; overflow-y: auto; font-size: 12px;"><?php
                                            foreach (array_reverse($recentLogs) as $log) {
                                                echo htmlspecialchars($log);
                                            }
                                        ?></pre>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-refresh every 30 seconds
setTimeout(function() {
    location.reload();
}, 30000);
</script>

<?php require_once 'includes/footer.php'; ?>
