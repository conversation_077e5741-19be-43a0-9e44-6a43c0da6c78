package com.anginatech.textrepeater;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import com.anginatech.textrepeater.models.Category;
import com.anginatech.textrepeater.models.TextMessage;

import java.util.ArrayList;
import java.util.List;

/**
 * Enhanced DatabaseHelper for dynamic categories
 */
public class DynamicDatabaseHelper extends SQLiteOpenHelper {

    private static final String DATABASE_NAME = "dynamicSmsDatabase.db";
    private static final int DATABASE_VERSION = 1;
    
    // Unified table for all categories
    private static final String TABLE_MESSAGES = "messages";
    private static final String TABLE_CATEGORIES = "categories";
    
    // Messages table columns
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_MESSAGE = "message";
    private static final String COLUMN_CATEGORY_NAME = "category_name";
    private static final String COLUMN_SERVER_ID = "server_id";
    
    // Categories table columns
    private static final String COLUMN_CAT_ID = "id";
    private static final String COLUMN_CAT_NAME = "name";
    private static final String COLUMN_CAT_DISPLAY_NAME = "display_name";
    private static final String COLUMN_CAT_DESCRIPTION = "description";
    private static final String COLUMN_CAT_SORT_ORDER = "sort_order";

    public DynamicDatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        // Create categories table
        String createCategoriesTable = "CREATE TABLE " + TABLE_CATEGORIES + " (" +
                COLUMN_CAT_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                COLUMN_CAT_NAME + " TEXT UNIQUE NOT NULL, " +
                COLUMN_CAT_DISPLAY_NAME + " TEXT NOT NULL, " +
                COLUMN_CAT_DESCRIPTION + " TEXT, " +
                COLUMN_CAT_SORT_ORDER + " INTEGER DEFAULT 0)";
        db.execSQL(createCategoriesTable);

        // Create messages table
        String createMessagesTable = "CREATE TABLE " + TABLE_MESSAGES + " (" +
                COLUMN_ID + " INTEGER PRIMARY KEY AUTOINCREMENT, " +
                COLUMN_SERVER_ID + " TEXT, " +
                COLUMN_MESSAGE + " TEXT NOT NULL, " +
                COLUMN_CATEGORY_NAME + " TEXT NOT NULL)";
        db.execSQL(createMessagesTable);

        Log.d("DynamicDB", "Database tables created successfully");
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_MESSAGES);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_CATEGORIES);
        onCreate(db);
    }

    /**
     * Insert or update category
     */
    public void insertOrUpdateCategory(Category category) {
        SQLiteDatabase db = this.getWritableDatabase();
        
        try {
            // Check if category exists
            Cursor cursor = db.rawQuery("SELECT * FROM " + TABLE_CATEGORIES + " WHERE " + COLUMN_CAT_NAME + " = ?", 
                    new String[]{category.getName()});
            
            ContentValues values = new ContentValues();
            values.put(COLUMN_CAT_NAME, category.getName());
            values.put(COLUMN_CAT_DISPLAY_NAME, category.getDisplayName());
            values.put(COLUMN_CAT_DESCRIPTION, category.getDescription());
            values.put(COLUMN_CAT_SORT_ORDER, category.getSortOrder());
            
            if (cursor.getCount() > 0) {
                // Update existing category
                db.update(TABLE_CATEGORIES, values, COLUMN_CAT_NAME + " = ?", new String[]{category.getName()});
                Log.d("DynamicDB", "Category updated: " + category.getName());
            } else {
                // Insert new category
                db.insert(TABLE_CATEGORIES, null, values);
                Log.d("DynamicDB", "Category inserted: " + category.getName());
            }
            cursor.close();
        } catch (Exception e) {
            Log.e("DynamicDB", "Error inserting/updating category: " + e.getMessage());
        }
    }

    /**
     * Insert or update message
     */
    public void insertOrUpdateMessage(String serverId, String message, String categoryName) {
        SQLiteDatabase db = this.getWritableDatabase();
        
        try {
            // Check if message exists
            Cursor cursor = db.rawQuery("SELECT * FROM " + TABLE_MESSAGES + " WHERE " + COLUMN_SERVER_ID + " = ? AND " + COLUMN_CATEGORY_NAME + " = ?", 
                    new String[]{serverId, categoryName});
            
            ContentValues values = new ContentValues();
            values.put(COLUMN_SERVER_ID, serverId);
            values.put(COLUMN_MESSAGE, message);
            values.put(COLUMN_CATEGORY_NAME, categoryName);
            
            if (cursor.getCount() > 0) {
                // Update existing message
                db.update(TABLE_MESSAGES, values, COLUMN_SERVER_ID + " = ? AND " + COLUMN_CATEGORY_NAME + " = ?", 
                        new String[]{serverId, categoryName});
                Log.d("DynamicDB", "Message updated for category: " + categoryName);
            } else {
                // Insert new message
                db.insert(TABLE_MESSAGES, null, values);
                Log.d("DynamicDB", "Message inserted for category: " + categoryName);
            }
            cursor.close();
        } catch (Exception e) {
            Log.e("DynamicDB", "Error inserting/updating message: " + e.getMessage());
        }
    }

    /**
     * Get all categories ordered by sort_order
     */
    public List<Category> getAllCategories() {
        List<Category> categories = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        try {
            Cursor cursor = db.rawQuery("SELECT * FROM " + TABLE_CATEGORIES + " ORDER BY " + COLUMN_CAT_SORT_ORDER + " ASC, " + COLUMN_CAT_DISPLAY_NAME + " ASC", null);
            
            if (cursor.moveToFirst()) {
                do {
                    Category category = new Category();
                    category.setId(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_CAT_ID)));
                    category.setName(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_CAT_NAME)));
                    category.setDisplayName(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_CAT_DISPLAY_NAME)));
                    category.setDescription(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_CAT_DESCRIPTION)));
                    category.setSortOrder(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_CAT_SORT_ORDER)));
                    categories.add(category);
                } while (cursor.moveToNext());
            }
            cursor.close();
        } catch (Exception e) {
            Log.e("DynamicDB", "Error getting categories: " + e.getMessage());
        }
        
        return categories;
    }

    /**
     * Get messages by category name
     */
    public List<TextMessage> getMessagesByCategory(String categoryName) {
        List<TextMessage> messages = new ArrayList<>();
        SQLiteDatabase db = this.getReadableDatabase();
        
        try {
            Cursor cursor = db.rawQuery("SELECT * FROM " + TABLE_MESSAGES + " WHERE " + COLUMN_CATEGORY_NAME + " = ?", 
                    new String[]{categoryName});
            
            if (cursor.moveToFirst()) {
                do {
                    TextMessage message = new TextMessage();
                    message.setId(cursor.getInt(cursor.getColumnIndexOrThrow(COLUMN_ID)));
                    message.setMessage(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_MESSAGE)));
                    message.setCategoryName(cursor.getString(cursor.getColumnIndexOrThrow(COLUMN_CATEGORY_NAME)));
                    messages.add(message);
                } while (cursor.moveToNext());
            }
            cursor.close();
        } catch (Exception e) {
            Log.e("DynamicDB", "Error getting messages for category " + categoryName + ": " + e.getMessage());
        }
        
        return messages;
    }

    /**
     * Get messages by category name as string list (for backward compatibility)
     */
    public List<String> getMessagesAsStringList(String categoryName) {
        List<String> messages = new ArrayList<>();
        List<TextMessage> textMessages = getMessagesByCategory(categoryName);
        
        for (TextMessage textMessage : textMessages) {
            messages.add(textMessage.getMessage());
        }
        
        return messages;
    }

    /**
     * Clear all data - COMPLETE REPLACEMENT
     * Ensures NO old data remains in database
     */
    public void clearAllData() {
        SQLiteDatabase db = this.getWritableDatabase();
        db.beginTransaction();
        try {
            // Get counts before clearing
            int messageCount = getTotalMessageCount();
            int categoryCount = getCategoryCount();

            Log.d("DynamicDB", "Clearing all data - Messages: " + messageCount + ", Categories: " + categoryCount);

            // Delete all messages first (due to foreign key constraints)
            int deletedMessages = db.delete(TABLE_MESSAGES, null, null);
            Log.d("DynamicDB", "Deleted " + deletedMessages + " messages");

            // Delete all categories
            int deletedCategories = db.delete(TABLE_CATEGORIES, null, null);
            Log.d("DynamicDB", "Deleted " + deletedCategories + " categories");

            // Verify tables are empty
            int remainingMessages = getTotalMessageCount();
            int remainingCategories = getCategoryCount();

            if (remainingMessages == 0 && remainingCategories == 0) {
                db.setTransactionSuccessful();
                Log.d("DynamicDB", "✅ ALL DATA CLEARED SUCCESSFULLY - Database is now empty");
            } else {
                Log.e("DynamicDB", "❌ CLEAR FAILED - Remaining: Messages=" + remainingMessages + ", Categories=" + remainingCategories);
                throw new RuntimeException("Failed to clear all data completely");
            }

        } catch (Exception e) {
            Log.e("DynamicDB", "Error clearing all data: " + e.getMessage(), e);
            throw e;
        } finally {
            db.endTransaction();
        }
    }

    /**
     * Get total message count
     */
    public int getTotalMessageCount() {
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.rawQuery("SELECT COUNT(*) FROM " + TABLE_MESSAGES, null);
        int count = 0;
        if (cursor.moveToFirst()) {
            count = cursor.getInt(0);
        }
        cursor.close();
        return count;
    }

    /**
     * Get category count
     */
    public int getCategoryCount() {
        SQLiteDatabase db = this.getReadableDatabase();
        Cursor cursor = db.rawQuery("SELECT COUNT(*) FROM " + TABLE_CATEGORIES, null);
        int count = 0;
        if (cursor.moveToFirst()) {
            count = cursor.getInt(0);
        }
        cursor.close();
        return count;
    }
}
