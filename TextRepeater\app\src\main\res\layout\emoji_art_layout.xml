<!-- item_sms.xml -->
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"

    >


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/smsItemClick"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:clickable="true"
        android:foreground="?attr/selectableItemBackground"
        >


        <TextView
            android:id="@+id/textPosition"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:fontFamily="@font/allerta"
            android:gravity="center"
            android:text="1"
            android:textColor="@color/love"
            android:textSize="19sp"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginStart="16dp"
            android:layout_marginTop="22dp"
            app:layout_constraintHorizontal_bias="0.085"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0"
            />

        <TextView
            android:id="@+id/text1"
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:fontFamily="@font/artifika"
            android:gravity="start"
            android:layout_marginEnd="16dp"
            android:layout_marginStart="18dp"
            android:text="Hello"
            android:paddingTop="10dp"
            android:paddingBottom="18dp"
            android:layout_marginTop="8dp"
            android:textColor="@color/primary_text"
            android:textSize="17sp"
            android:layout_marginBottom="6dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.094"
            app:layout_constraintStart_toEndOf="@+id/textPosition"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.6" />

        <View
            android:layout_width="match_parent"
            android:layout_height="0.8dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintVertical_bias="1.0"
            android:background="@color/emoji_view_color"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>
