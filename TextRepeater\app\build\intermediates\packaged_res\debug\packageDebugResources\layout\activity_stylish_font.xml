<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Stylish_Font_Activity"
    android:background="@color/mother_layout_color"
    >


    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/stylish_MaterialToolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:menu="@menu/settings_item"
        app:navigationIcon="@drawable/baseline_arrow_back_ios_24"
        app:navigationIconTint="@color/toolbar_text_color"
        app:title="Stylish Font"
        app:titleTextColor="@color/toolbar_text_color">


    </com.google.android.material.appbar.MaterialToolbar>





    <EditText
        android:id="@+id/editStylish_Text"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="30dp"
        android:background="@drawable/edittext_backround"
        android:drawableStart="@drawable/baseline_text_fields_24"
        android:ems="10"
        android:hint=" Enter text"
        android:inputType="text"
        android:maxLength="300"
        android:paddingLeft="16dp"
        android:paddingEnd="10dp"
        android:textColor="@color/primary_text"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/stylish_MaterialToolbar"
        app:layout_constraintVertical_bias="0.0" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/stylish_RecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:clipToPadding="false"
        android:paddingBottom="120dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/editStylish_Text"

        />





</androidx.constraintlayout.widget.ConstraintLayout>