package com.anginatech.textrepeater;




import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.Intent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import com.google.android.material.bottomsheet.BottomSheetDialog;


import java.util.List;

public class SmsAdapter extends RecyclerView.Adapter<SmsAdapter.SmsViewHolder> {

    private Context context;
    private List<String> smsList;

    public SmsAdapter(Context context, List<String> smsList) {
        this.context = context;
        this.smsList = smsList;
    }

    @NonNull
    @Override
    public SmsViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_sms, parent, false);
        return new SmsViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SmsViewHolder holder, int position) {
            holder.smsTextView.setText(smsList.get(position));
            int number = position+1;
            holder.positionText.setText(""+number);
            String sms = smsList.get(position);
            holder.smsItemClick.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {

                    BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(context);
                    View bottomSheetView = LayoutInflater.from(context).inflate(R.layout.sms_item_dailog, null);

                    TextView smsItemText = bottomSheetView.findViewById(R.id.smsItemText);
                    CardView cardViewCopy = bottomSheetView.findViewById(R.id.cardViewCopy);
                    CardView cardViewShare = bottomSheetView.findViewById(R.id.cardViewShare);
                    smsItemText.setText(""+sms);

                    cardViewCopy.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {

                            ClipboardManager clipboard = (ClipboardManager) context.getSystemService(Context.CLIPBOARD_SERVICE);
                            ClipData clip = ClipData.newPlainText("Copied Text", sms);
                            clipboard.setPrimaryClip(clip);

                            Toast.makeText(context, "Text copied to clipboard!", Toast.LENGTH_SHORT).show();
                            bottomSheetDialog.dismiss();

                        }


                    });

                    cardViewShare.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View v) {
                            Intent shareIntent = new Intent(Intent.ACTION_SEND);
                            shareIntent.setType("text/plain");
                            shareIntent.putExtra(Intent.EXTRA_TEXT, sms);
                            context.startActivity(Intent.createChooser(shareIntent, "Share SMS via"));
                            bottomSheetDialog.dismiss();

                        }
                    });

                    bottomSheetDialog.setContentView(bottomSheetView);
                    bottomSheetDialog.show();

                }
            });



    }

    @Override
    public int getItemCount() {
        return smsList.size();
    }

    public static class SmsViewHolder extends RecyclerView.ViewHolder {
        TextView smsTextView,positionText;
        ConstraintLayout smsItemClick;


        public SmsViewHolder(@NonNull View itemView) {
            super(itemView);
            smsTextView = itemView.findViewById(R.id.smsText);
            positionText = itemView.findViewById(R.id.positionText);
            smsItemClick = itemView.findViewById(R.id.smsItemClick);
        }
    }





}