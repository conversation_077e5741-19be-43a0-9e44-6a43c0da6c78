<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".Navigation_Activity"
    android:background="@color/mother_layout_color"
    >


    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/skipButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:text="SKIP"
        android:elevation="0dp"
        android:textSize="16sp"
        android:clickable="true"
        android:foreground="?attr/selectableItemBackground"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.996"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0"
        android:layout_marginEnd="16dp"
        android:textColor="@color/love"
        android:layout_marginTop="20dp"
        android:background="@android:color/transparent"
        />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/sliderViewPager"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="75dp"
        android:layout_marginBottom="170dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />


    <LinearLayout
        android:id="@+id/dot_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.498"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/sliderViewPager"
        app:layout_constraintVertical_bias="0.0"
        />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/nextButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:background="@drawable/button_backround"
        android:text="NEXT"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:layout_marginTop="44dp"
        android:layout_marginBottom="32dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/dot_layout"
        app:layout_constraintVertical_bias="0.0"
        />






</androidx.constraintlayout.widget.ConstraintLayout>