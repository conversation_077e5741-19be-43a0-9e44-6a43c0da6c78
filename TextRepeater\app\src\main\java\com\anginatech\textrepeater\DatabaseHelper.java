package com.anginatech.textrepeater;

import android.content.ContentValues;
import android.content.Context;
import android.database.Cursor;
import android.database.sqlite.SQLiteDatabase;
import android.database.sqlite.SQLiteOpenHelper;
import android.util.Log;

import java.util.ArrayList;
import java.util.List;

public class DatabaseHelper extends SQLiteOpenHelper {

    private static final String DATABASE_NAME = "smsDatabase.db";
    private static final int DATABASE_VERSION = 1;
    private static final String COLUMN_ID = "id";
    private static final String COLUMN_MESSAGE = "message";

    private static final String TABLE_ROMANTIC = "romanticTable";
    private static final String TABLE_SAD = "sadTable";
    private static final String TABLE_FUNNY = "funnyTable";

    public DatabaseHelper(Context context) {
        super(context, DATABASE_NAME, null, DATABASE_VERSION);
    }

    @Override
    public void onCreate(SQLiteDatabase db) {
        String createTable1 = "CREATE TABLE " + TABLE_ROMANTIC + " (" +
                COLUMN_ID + " TEXT PRIMARY KEY, " +
                COLUMN_MESSAGE + " TEXT)";
        db.execSQL(createTable1);
        String createTable2 = "CREATE TABLE " + TABLE_SAD + " (" +
                COLUMN_ID + " TEXT PRIMARY KEY, " +
                COLUMN_MESSAGE + " TEXT)";
        db.execSQL(createTable2);

        String createTable3 = "CREATE TABLE " + TABLE_FUNNY + " (" +
                COLUMN_ID + " TEXT PRIMARY KEY, " +
                COLUMN_MESSAGE + " TEXT)";
        db.execSQL(createTable3);
    }

    @Override
    public void onUpgrade(SQLiteDatabase db, int oldVersion, int newVersion) {
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_ROMANTIC);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_SAD);
        db.execSQL("DROP TABLE IF EXISTS " + TABLE_FUNNY);
        onCreate(db);
    }

    public void insertOrUpdateDataRomantic(String id, String message) {
        SQLiteDatabase db = this.getWritableDatabase();


        Cursor cursor = db.rawQuery("SELECT * FROM " + TABLE_ROMANTIC + " WHERE " + COLUMN_ID + " = ?", new String[]{id});
        if (cursor.getCount() > 0) {
            ContentValues values = new ContentValues();
            values.put(COLUMN_MESSAGE, message);

            db.update(TABLE_ROMANTIC, values, COLUMN_ID + " = ?", new String[]{id});
            Log.d("DB_LOG", "Data updated for ID: " + id);
        } else {
            // Insert new record
            ContentValues values = new ContentValues();
            values.put(COLUMN_ID, id);
            values.put(COLUMN_MESSAGE, message);

            db.insert(TABLE_ROMANTIC, null, values);
            Log.d("DB_LOG", "Data inserted for ID: " + id);
        }
        cursor.close();
    }

    public void insertOrUpdateDataSad(String id, String message) {
        SQLiteDatabase db = this.getWritableDatabase();


        Cursor cursor = db.rawQuery("SELECT * FROM " + TABLE_SAD + " WHERE " + COLUMN_ID + " = ?", new String[]{id});
        if (cursor.getCount() > 0) {
            ContentValues values = new ContentValues();
            values.put(COLUMN_MESSAGE, message);

            db.update(TABLE_SAD, values, COLUMN_ID + " = ?", new String[]{id});
            Log.d("DB_LOG", "Data updated for ID: " + id);
        } else {
            // Insert new record
            ContentValues values = new ContentValues();
            values.put(COLUMN_ID, id);
            values.put(COLUMN_MESSAGE, message);

            db.insert(TABLE_SAD, null, values);
            Log.d("DB_LOG", "Data inserted for ID: " + id);
        }
        cursor.close();
    }

    public void insertOrUpdateDataFunny(String id, String message) {
        SQLiteDatabase db = this.getWritableDatabase();


        Cursor cursor = db.rawQuery("SELECT * FROM " + TABLE_FUNNY + " WHERE " + COLUMN_ID + " = ?", new String[]{id});
        if (cursor.getCount() > 0) {
            ContentValues values = new ContentValues();
            values.put(COLUMN_MESSAGE, message);

            db.update(TABLE_FUNNY, values, COLUMN_ID + " = ?", new String[]{id});
            Log.d("DB_LOG", "Data updated for ID: " + id);
        } else {
            // Insert new record
            ContentValues values = new ContentValues();
            values.put(COLUMN_ID, id);
            values.put(COLUMN_MESSAGE, message);

            db.insert(TABLE_FUNNY, null, values);
            Log.d("DB_LOG", "Data inserted for ID: " + id);
        }
        cursor.close();
    }

    public List<String> getMessages(String tableName) {
        List<String> messages = new ArrayList<>();
        SQLiteDatabase db = getReadableDatabase();
        Cursor cursor = db.rawQuery("SELECT message FROM " + tableName, null);
        if (cursor.moveToFirst()) {
            do {
                messages.add(cursor.getString(0));
            } while (cursor.moveToNext());
        }
        cursor.close();
        return messages;
    }



    public Cursor getAllDatabyCatagory(String Table_name) {
        SQLiteDatabase db = this.getReadableDatabase();
        return db.rawQuery("SELECT * FROM " + Table_name, null);
    }

    /**
     * Clear all data from all tables
     */
    public void clearAllData() {
        SQLiteDatabase db = this.getWritableDatabase();
        try {
            db.delete(TABLE_ROMANTIC, null, null);
            db.delete(TABLE_SAD, null, null);
            db.delete(TABLE_FUNNY, null, null);
            Log.d("DatabaseHelper", "All legacy data cleared");
        } catch (Exception e) {
            Log.e("DatabaseHelper", "Error clearing all data: " + e.getMessage());
        }
    }
}
