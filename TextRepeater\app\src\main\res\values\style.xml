<?xml version="1.0" encoding="utf-8"?>
<resources>


    <style name="Toolbar.TitleText" parent="TextAppearance.Widget.AppCompat.Toolbar.Title">
        <item name="android:textSize">20sp</item>
    </style>


    <style name="CollapsedTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@android:color/black</item>
    </style>

    <style name="ExpandedTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title">
        <item name="android:textSize">22sp</item>
        <item name="android:textColor">@android:color/white</item>
    </style>

    <style name="MyTabLayoutTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:ellipsize">none</item>
        <item name="android:singleLine">false</item>
        <item name="android:maxLines">2</item>
        <item name="android:gravity">center</item>
        <item name="android:lineSpacingExtra">2dp</item>
    </style>

    <style name="DialogTabLayoutTextAppearance" parent="TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse">
        <item name="android:textSize">15sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:ellipsize">none</item>
        <item name="android:singleLine">false</item>
        <item name="android:maxLines">2</item>
        <item name="android:gravity">center</item>
        <item name="android:lineSpacingExtra">3dp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:letterSpacing">0.01</item>
    </style>


    <style name="BottomSheetDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/slide_in</item>
        <item name="android:windowExitAnimation">@anim/slide_out</item>
    </style>


    <style name="RobotoBoldTextAppearance">
        <item name="android:fontFamily">@font/arima_madurai_bold</item>
        <item name="android:textSize">23sp</item>

    </style>



</resources>

