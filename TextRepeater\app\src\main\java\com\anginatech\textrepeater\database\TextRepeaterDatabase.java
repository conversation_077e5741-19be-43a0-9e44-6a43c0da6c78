package com.anginatech.textrepeater.database;

import android.content.Context;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.anginatech.textrepeater.database.dao.CategoryDao;
import com.anginatech.textrepeater.database.dao.MessageDao;
import com.anginatech.textrepeater.database.entities.CategoryEntity;
import com.anginatech.textrepeater.database.entities.MessageEntity;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Room Database for Text Repeater App
 * Replaces the old SQLite database implementation with modern Room architecture
 */
@Database(
    entities = {CategoryEntity.class, MessageEntity.class},
    version = 1,
    exportSchema = false
)
public abstract class TextRepeaterDatabase extends RoomDatabase {
    
    private static final String TAG = "TextRepeaterDatabase";
    private static final String DATABASE_NAME = "text_repeater_database";
    
    // Singleton instance
    private static volatile TextRepeaterDatabase INSTANCE;
    
    // Thread pool for database operations
    private static final int NUMBER_OF_THREADS = 4;
    public static final ExecutorService databaseWriteExecutor = Executors.newFixedThreadPool(NUMBER_OF_THREADS);
    
    // Abstract methods to get DAOs
    public abstract CategoryDao categoryDao();
    public abstract MessageDao messageDao();
    
    /**
     * Get database instance (Singleton pattern)
     */
    public static TextRepeaterDatabase getDatabase(final Context context) {
        if (INSTANCE == null) {
            synchronized (TextRepeaterDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                            context.getApplicationContext(),
                            TextRepeaterDatabase.class,
                            DATABASE_NAME
                        )
                        .addCallback(roomDatabaseCallback)
                        .addMigrations(MIGRATION_1_2) // For future migrations
                        .fallbackToDestructiveMigration() // For development - remove in production
                        .build();
                    
                    Log.d(TAG, "Room database created successfully");
                }
            }
        }
        return INSTANCE;
    }
    
    /**
     * Database callback for initialization
     */
    private static RoomDatabase.Callback roomDatabaseCallback = new RoomDatabase.Callback() {
        @Override
        public void onCreate(@NonNull SupportSQLiteDatabase db) {
            super.onCreate(db);
            Log.d(TAG, "Room database tables created successfully");
            
            // Perform any initial setup if needed
            databaseWriteExecutor.execute(() -> {
                // Database is created, ready for use
                Log.d(TAG, "Room database is ready for operations");
            });
        }
        
        @Override
        public void onOpen(@NonNull SupportSQLiteDatabase db) {
            super.onOpen(db);
            Log.d(TAG, "Room database opened successfully");
        }
    };
    
    /**
     * Migration from version 1 to 2 (for future use)
     */
    static final Migration MIGRATION_1_2 = new Migration(1, 2) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // Example migration - add new columns or tables
            // database.execSQL("ALTER TABLE categories ADD COLUMN new_column TEXT");
            Log.d(TAG, "Database migrated from version 1 to 2");
        }
    };
    
    /**
     * Clear all data from database - COMPLETE DATA REPLACEMENT
     * Used for fresh data synchronization
     */
    public void clearAllData() {
        databaseWriteExecutor.execute(() -> {
            try {
                Log.d(TAG, "Clearing all data from Room database...");
                
                // Clear messages first (due to foreign key constraints)
                messageDao().deleteAllMessages().blockingAwait();
                Log.d(TAG, "All messages cleared");
                
                // Clear categories
                categoryDao().deleteAllCategories().blockingAwait();
                Log.d(TAG, "All categories cleared");
                
                Log.d(TAG, "✅ All data cleared successfully - ready for fresh sync data");
                
            } catch (Exception e) {
                Log.e(TAG, "❌ Error clearing all data: " + e.getMessage(), e);
            }
        });
    }
    
    /**
     * Get database statistics
     */
    public void getDatabaseStats(DatabaseStatsCallback callback) {
        databaseWriteExecutor.execute(() -> {
            try {
                CategoryDao.CategoryStats categoryStats = categoryDao().getCategoryStats().blockingGet();
                MessageDao.MessageStats messageStats = messageDao().getMessageStats().blockingGet();
                
                DatabaseStats stats = new DatabaseStats(categoryStats, messageStats);
                
                // Return stats on main thread
                if (callback != null) {
                    callback.onStatsReady(stats);
                }
                
                Log.d(TAG, "Database stats: " + stats.toString());
                
            } catch (Exception e) {
                Log.e(TAG, "Error getting database stats: " + e.getMessage(), e);
                if (callback != null) {
                    callback.onError("Failed to get database statistics");
                }
            }
        });
    }
    
    /**
     * Check if database is empty
     */
    public void isDatabaseEmpty(DatabaseEmptyCallback callback) {
        databaseWriteExecutor.execute(() -> {
            try {
                int categoryCount = categoryDao().getTotalCategoriesCount().blockingGet();
                int messageCount = messageDao().getTotalMessagesCount().blockingGet();
                
                boolean isEmpty = (categoryCount == 0 && messageCount == 0);
                
                if (callback != null) {
                    callback.onResult(isEmpty, categoryCount, messageCount);
                }
                
                Log.d(TAG, "Database empty check: " + isEmpty + " (Categories: " + categoryCount + ", Messages: " + messageCount + ")");
                
            } catch (Exception e) {
                Log.e(TAG, "Error checking if database is empty: " + e.getMessage(), e);
                if (callback != null) {
                    callback.onError("Failed to check database status");
                }
            }
        });
    }
    
    /**
     * Close database and cleanup resources
     */
    public static void closeDatabase() {
        if (INSTANCE != null) {
            INSTANCE.close();
            INSTANCE = null;
            Log.d(TAG, "Database closed and instance cleared");
        }
    }
    
    /**
     * Database statistics container
     */
    public static class DatabaseStats {
        public final CategoryDao.CategoryStats categoryStats;
        public final MessageDao.MessageStats messageStats;
        
        public DatabaseStats(CategoryDao.CategoryStats categoryStats, MessageDao.MessageStats messageStats) {
            this.categoryStats = categoryStats;
            this.messageStats = messageStats;
        }
        
        @Override
        public String toString() {
            return "DatabaseStats{" +
                    "categories=" + categoryStats.toString() +
                    ", messages=" + messageStats.toString() +
                    '}';
        }
    }
    
    /**
     * Callback interface for database statistics
     */
    public interface DatabaseStatsCallback {
        void onStatsReady(DatabaseStats stats);
        void onError(String error);
    }
    
    /**
     * Callback interface for database empty check
     */
    public interface DatabaseEmptyCallback {
        void onResult(boolean isEmpty, int categoryCount, int messageCount);
        void onError(String error);
    }
}
