<?php
/**
 * Users Endpoint Handler
 * Handles all user management API requests
 */

class UsersEndpoint {
    private $conn;
    private $response;

    public function __construct() {
        $this->conn = getAPIDatabase();
        $this->response = new APIResponse();
    }

    /**
     * Handle users endpoint requests
     */
    public function handle($method, $pathParts) {
        $action = $pathParts[1] ?? '';

        switch ($action) {
            case 'register':
                return $this->handleRegistration($method);

            case 'update':
                return $this->handleUpdate($method);

            case 'profile':
                return $this->handleProfile($method);

            case 'stats':
                return $this->handleStats($method);

            case 'sessions':
                return $this->handleSessions($method);

            default:
                return $this->response->notFound('Users endpoint');
        }
    }

    /**
     * Handle user registration
     */
    private function handleRegistration($method) {
        if ($method !== 'POST') {
            return $this->response->error('Method not allowed', 405);
        }

        $input = $this->response->getJsonInput();
        if (!$input) {
            return $this->response->error('Invalid JSON input', 400);
        }

        // Validate required fields
        $requiredFields = ['device_id'];
        $errors = $this->response->validateRequired($input, $requiredFields);

        if (!empty($errors)) {
            return $this->response->validationError($errors);
        }

        try {
            // Check if user already exists
            $query = "SELECT id, created_at FROM app_users WHERE device_id = :device_id";
            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':device_id', $input['device_id']);
            $stmt->execute();

            $existingUser = $stmt->fetch();

            if ($existingUser) {
                // Update existing user
                $query = "UPDATE app_users SET
                         fcm_token = :fcm_token,
                         app_version = :app_version,
                         android_version = :android_version,
                         device_model = :device_model,
                         device_brand = :device_brand,
                         screen_resolution = :screen_resolution,
                         language = :language,
                         timezone = :timezone,
                         last_seen = NOW(),
                         total_sessions = total_sessions + 1,
                         updated_at = NOW()
                         WHERE device_id = :device_id";

                $isNewUser = false;
                $userId = $existingUser['id'];
            } else {
                // Insert new user
                $query = "INSERT INTO app_users
                         (device_id, fcm_token, app_version, android_version, device_model,
                          device_brand, screen_resolution, language, timezone, total_sessions)
                         VALUES
                         (:device_id, :fcm_token, :app_version, :android_version, :device_model,
                          :device_brand, :screen_resolution, :language, :timezone, 1)";

                $isNewUser = true;
            }

            $stmt = $this->conn->prepare($query);

            // Prepare variables for binding (bindParam requires variables, not expressions)
            $deviceId = $input['device_id'];
            $fcmToken = $input['fcm_token'] ?? '';
            $appVersion = $input['app_version'] ?? '';
            $androidVersion = $input['android_version'] ?? '';
            $deviceModel = $input['device_model'] ?? '';
            $deviceBrand = $input['device_brand'] ?? '';
            $screenResolution = $input['screen_resolution'] ?? '';
            $language = $input['language'] ?? 'en';
            $timezone = $input['timezone'] ?? 'UTC';

            $stmt->bindParam(':device_id', $deviceId);
            $stmt->bindParam(':fcm_token', $fcmToken);
            $stmt->bindParam(':app_version', $appVersion);
            $stmt->bindParam(':android_version', $androidVersion);
            $stmt->bindParam(':device_model', $deviceModel);
            $stmt->bindParam(':device_brand', $deviceBrand);
            $stmt->bindParam(':screen_resolution', $screenResolution);
            $stmt->bindParam(':language', $language);
            $stmt->bindParam(':timezone', $timezone);

            if ($stmt->execute()) {
                if ($isNewUser) {
                    $userId = $this->conn->lastInsertId();
                }

                // Log session
                $this->logUserSession($userId, $input);

                return $this->response->success([
                    'message' => $isNewUser ? 'User registered successfully' : 'User updated successfully',
                    'user_id' => $userId,
                    'is_new_user' => $isNewUser
                ]);
            } else {
                return $this->response->serverError('Failed to register user');
            }

        } catch (Exception $e) {
            error_log("User registration error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
            return $this->response->serverError('Database error occurred: ' . $e->getMessage());
        }
    }

    /**
     * Handle user profile updates
     */
    private function handleUpdate($method) {
        if ($method !== 'POST') {
            return $this->response->error('Method not allowed', 405);
        }

        $input = $this->response->getJsonInput();
        if (!$input) {
            return $this->response->error('Invalid JSON input', 400);
        }

        if (empty($input['device_id'])) {
            return $this->response->error('Device ID is required', 400);
        }

        try {
            $query = "UPDATE app_users SET
                     fcm_token = :fcm_token,
                     language = :language,
                     timezone = :timezone,
                     last_seen = NOW(),
                     updated_at = NOW()
                     WHERE device_id = :device_id";

            $stmt = $this->conn->prepare($query);

            // Prepare variables for binding
            $deviceId = $input['device_id'];
            $fcmToken = $input['fcm_token'] ?? '';
            $language = $input['language'] ?? 'en';
            $timezone = $input['timezone'] ?? 'UTC';

            $stmt->bindParam(':device_id', $deviceId);
            $stmt->bindParam(':fcm_token', $fcmToken);
            $stmt->bindParam(':language', $language);
            $stmt->bindParam(':timezone', $timezone);

            if ($stmt->execute()) {
                return $this->response->success(['message' => 'User updated successfully']);
            } else {
                return $this->response->serverError('Failed to update user');
            }

        } catch (Exception $e) {
            return $this->response->serverError('Database error occurred');
        }
    }

    /**
     * Handle user profile requests
     */
    private function handleProfile($method) {
        if ($method !== 'GET') {
            return $this->response->error('Method not allowed', 405);
        }

        $deviceId = $_GET['device_id'] ?? '';
        if (empty($deviceId)) {
            return $this->response->error('Device ID is required', 400);
        }

        try {
            $query = "SELECT
                        device_id,
                        app_version,
                        android_version,
                        device_model,
                        device_brand,
                        language,
                        timezone,
                        total_sessions,
                        created_at,
                        last_seen
                     FROM app_users
                     WHERE device_id = :device_id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':device_id', $deviceId);
            $stmt->execute();

            $user = $stmt->fetch();

            if ($user) {
                return $this->response->success($user);
            } else {
                return $this->response->notFound('User');
            }

        } catch (Exception $e) {
            return $this->response->serverError('Failed to get user profile');
        }
    }

    /**
     * Handle user statistics requests
     */
    private function handleStats($method) {
        if ($method !== 'GET') {
            return $this->response->error('Method not allowed', 405);
        }

        $deviceId = $_GET['device_id'] ?? '';
        if (empty($deviceId)) {
            return $this->response->error('Device ID is required', 400);
        }

        try {
            // Get user stats
            $query = "SELECT
                        COUNT(DISTINCT session_id) as total_sessions,
                        SUM(CASE WHEN event_type = 'impression' THEN 1 ELSE 0 END) as ad_impressions,
                        SUM(CASE WHEN event_type = 'click' THEN 1 ELSE 0 END) as ad_clicks,
                        SUM(revenue) as total_revenue
                     FROM ad_analytics
                     WHERE device_id = :device_id";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(':device_id', $deviceId);
            $stmt->execute();

            $stats = $stmt->fetch();

            return $this->response->success($stats);

        } catch (Exception $e) {
            return $this->response->serverError('Failed to get user statistics');
        }
    }

    /**
     * Log user session
     */
    private function logUserSession($userId, $sessionData) {
        try {
            // Check if user_sessions table exists first
            $stmt = $this->conn->query("SHOW TABLES LIKE 'user_sessions'");
            if ($stmt->rowCount() == 0) {
                // Table doesn't exist, skip session logging
                error_log("user_sessions table does not exist, skipping session logging");
                return;
            }

            $query = "INSERT INTO user_sessions
                     (user_id, session_id, app_version, started_at)
                     VALUES
                     (:user_id, :session_id, :app_version, NOW())";

            $stmt = $this->conn->prepare($query);

            // Prepare variables for binding
            $sessionId = $sessionData['session_id'] ?? uniqid();
            $appVersion = $sessionData['app_version'] ?? '';

            $stmt->bindParam(':user_id', $userId);
            $stmt->bindParam(':session_id', $sessionId);
            $stmt->bindParam(':app_version', $appVersion);
            $stmt->execute();

        } catch (Exception $e) {
            error_log("Failed to log user session: " . $e->getMessage());
            // Don't throw the exception, just log it since session logging is not critical
        }
    }
}
?>
